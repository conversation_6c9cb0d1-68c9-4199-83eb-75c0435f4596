server:
  port: 8087
  netty:
    connection-timeout: 43200
db:
  host:
    *************
  sshPort: 22
  port:
    5432
spring:
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
    dynamic:
      druid:
        maxActive: 40
        initialSize: 1
        maxWait: 30000
        minIdle: 1
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        validationQuery: select 'x'
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
      primary: allUser
      datasource:
        allUser: # 交换区库
          driver-class-name: org.postgresql.Driver
          url: jdbc:postgresql://${db.host}:${db.port}/all_user?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=GMT%2B8
          username: postgres
          password: 20191809
  # 自定义动态数据源
  customize:
    datasource:
      gz_dj:
        driver-class-name: org.postgresql.Driver
        url: jdbc:postgresql://*************:5432/gz_dj_tt?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=GMT%2B8
        username: postgres
        password: 20191809
      gz_dj_tmp:
        driver-class-name: org.postgresql.Driver
        url: jdbc:postgresql://*************:5432/gz_dj_tt_temp?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=GMT%2B8
        username: postgres
        password: 20191809
      all_user_tmp:
        driver-class-name: org.postgresql.Driver
        url: jdbc:postgresql://*************:5432/all_user_tt_temp?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=GMT%2B8
        username: postgres
        password: 20191809
  redis:
    redisson:
      file: classpath:redisson.yml
    timeout: 2000 # 连接或读取超时时长（毫秒）
    database: 21
    jedis:
      pool:
        max-active: 8 # 连接池最大连接数（使用负值表示没有限制）
        max-wait: 800 # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 8 # 连接池中的最大空闲连接
        min-idle: 2 # 连接池中的最小空闲连接
  servlet:
    multipart:
      enabled: true
      max-file-size: 1GB       #(这里是限制的文件大小)
      max-request-size: 1GB    #(这里是限制的文件大小)
  mvc:
    hiddenmethod:
      filter:
        enabled: true
  jackson:
    default-property-inclusion: NON_NULL
logging:
  level:
    com:
      zenith:
        front:
          mapper: debug   #debug只输出sql语句 trace 打印sql语句也打印sql执行结果集
sync:
  ips: *************:8080,*************:8080,localhost:8080

#zip解密密码
unzip:
  password: 4676d068-2048-4109-b8eb-98208e7e9652
intranet: true

# ================ 定时任务配置 ================
task:
#  enabledType: file,zzb-heart,zzb-org,transfer-org,flow-org,flow-mem
  # 开启与中组部的交互
  syncToZzB:
    enabled: true    # 是否启用该功能

# ========= 组织关系转接定时任务配置 =============
  # 新转入组织关系转接拉取时间周期（介绍信）
  pullLetterInTask:
    enabled: false    # 是否启用该任务
    cron: "0 10 * * * ?"  # 每小时的第10分钟执行
  
  # 本地转出办理过程拉取时间周期
  pullProcessOutTask:
    enabled: false    # 是否启用该任务
    cron: "0 25 * * * ?"  # 每小时的第25分钟执行
  
  # 关系转接定时修复时间周期（已经废弃）
  repairTransfer:
    enabled: false   # 已废弃，默认停用
    cron: "0 * * * * ?"
  
  # 关系转接本地党组织修正上传时间周期
  pushOrgAmendTask:
    enabled: false    # 是否启用该任务
    cron: "0 0 20 * * ?"  # 每天晚上8点执行
  
  # 关系转接批量核对修复时间周期
  repairTransFerData:
    enabled: false    # 已废弃，默认停用
    cron: "0 * * * * ?"  # 已废弃的任务，不再执行
  
  # 关系转接异常核对修复时间周期
  repairTransFerSwapAreaTask:
    enabled: false    # 已废弃，默认停用
    cron: "0 * * * * ?"  # 已废弃的任务，不再执行
  
  # 关系转接上传失败数据重新上传时间周期
  repairTransferUploadData:
    enabled: false    # 是否启用该任务
    cron: "0 20 0 * * ?"  # 每天零点20分执行
  
  # 转入本地组织关系核对时间周期（办理过程）
  pullProcessInTask:
    enabled: false
    cron: "0 20 * * * ?"  # 每小时的第20分钟执行


  # 组织关系转接月度上报任务
  pushTransferMonthStaticsTask:
    enabled: false    # 是否启用该任务
    cron: "0 0 2 1 * ?"  # 每月1号凌晨2点执行

# ========= 流动党员定时任务配置 ===========
  # 流入我省流动党员拉取定时任务周期
  pullFlowInTask:
    enabled: false    # 是否启用该任务
    cron: "0 0 * * * ?"  # 每小时的第0分钟执行
  
  # 我省流出党员拉取办理过程定时任务周期
  pullFlowOutTask:
    enabled: false    # 是否启用该任务
    cron: "0 5 * * * ?"  # 每小时的第5分钟执行
  
  # 流动党员月度上报任务
  pushFlowMonthStaticsTask:
    enabled: false    # 是否启用该任务
    cron: "0 0 1 1 * ?"  # 每月1号凌晨1点执行

  # 流动党组织相关独立任务
  downloadAllOrgFlow:
    enabled: false
    cron: "0 10 0 * * ?"  # 每天零点10分执行
  downloadNeedMyAudit:
    enabled: false
    cron: "0 0 0/2 * * ?"  # 每2小时执行一次
  downloadNeedOtherAudit:
    enabled: false
    cron: "0 0 0/2 * * ?"  # 每2小时执行一次
  uploadOrgFlowError:
    enabled: false
    cron: "0 0 0/2 * * ?"  # 每2小时执行一次
  
  # 流动党员相关独立任务
  downloadMemMessage:
    enabled: false
    cron: "0 15 * * * ?"  # 每小时的第15分钟执行
  downloadMemFwhdsj:
    enabled: false
    cron: "0 15 * * * ?"  # 每小时的第15分钟执行
  downloadMemRemindCheck:
    enabled: false
    cron: "0 15 * * * ?"  # 每小时的第15分钟执行
  downloadMemOtherRemind:
    enabled: false
    cron: "0 15 * * * ?"  # 每小时的第15分钟执行

  # 修复流动党员上传失败记录任务配置
  repairFlowUploadData:
    enabled: false
    cron: "0 40 0 * * ?"  # 每天零点40分执行

  # 修复流动党员唯一标识为空未上传问题任务配置
  repairMemFlowUqCodeNull:
    enabled: false    # 已废弃，默认停用
    cron: "0 * * * * ?"  # 已废弃的任务，不再执行

# ========= 中组部其他定时任务配置 ===========
  # 中组部探针任务
  pushHeartTask:
    enabled: false    # 是否启用该任务
    cron: "0 0/30 * * * ?"  # 每半小时执行一次


# ========= 党组织定时任务配置 =============
  # 党组织增量拉取时间周期
  pullOrgAddTask:
    enabled: false    # 是否启用该任务
    cron: "0 * * * * ?"


  # 党组织全量拉取时间周期
  pullOrgTask:
    enabled: false    # 是否启用该任务
    cron: "0 0 3 * * ?"  # 每天凌晨3点执行

  # ========= 流动党员推送到工作督查表到交换区 =============
  # 流动党员工作督查-当前流入
  pushMemFlowStaticsDangQianInTask:
    enabled: false    # 是否启用该任务
    cron: "0 10 3 * * ?"  # 每天凌晨3点10分执行

  # 流动党员工作督查-当前流出
  pushMemFlowStaticsDangQianOutTask:
    enabled: false    # 是否启用该任务
    cron: "0 20 3 * * ?"  # 每天凌晨3点20分执行

  # 流动党员工作督查-时段流入
  pushMemFlowStaticsShiDuanInTask:
    enabled: false    # 是否启用该任务
    cron: "0 30 3 1,11,21 * ?"  # 每月的1号，11号，21号执行凌晨3点30分执行

  # 流动党员工作督查-时段流出
  pushMemFlowStaticsShiDuanOutTask:
    enabled: false    # 是否启用该任务
    cron: "0 40 3 1,11,21 * ?"  # 每月的1号，11号，21号执行凌晨3点40分执行

  # ========= 流动党员流出数据状态校验 =============
  # 流动党员流出方-流回
  pullMemFlowStatusBackTask:
    enabled: false    # 是否启用该任务
    cron: "0 0/5 * * * ?"  # 每天凌晨3点10分执行

  # 流动党员流出方-接收
  pullMemFlowStatusReceiveTask:
    enabled: false    # 是否启用该任务
    cron: "0 0/5 * * * ?"  # 每天凌晨3点20分执行

  # 流动党员流出方-退回、超期
  pullMemFlowStatusRejectTask:
    enabled: false    # 是否启用该任务
    cron: "0 0/5 * * * ?"  # 每月的1号，11号，21号执行凌晨3点30分执行

  # 流动党员流出方-编辑
  pullMemFlowStatusUpdateTask:
    enabled: false    # 是否启用该任务
    cron: "0 0/5 * * * ?"  # 每一个小时执行一次


  # ========= 导出文件定时删除，并且处理数据库列表 =============
  # 定时删除过期的导出文件
  delExportFileTask:
    enabled: false    # 是否启用该任务
    cron: "0 0 2 * * ?"  # 每天凌晨2点执行

# ================ 定时任务配置 ================

feign:
  okhttp:
    enabled: true # 是否开启。默认为 false
  httpclient:
    # 开启 httpClient
    enabled: false

  # 请求登录参数是否加密处理
  paraEncrypt: true


# 内网信息
internal-service:
  # 数据包存放服务器
  zipSSH:
    sshHost: *************  # ssh地址
    sshPort: 22 # ssh端口
    sshUser: root
    sshPass: "Gzdj%1419!"

  # nginx server
  nginxSSH:
    sshHost: *************  # ssh地址
    sshPort: 22 # ssh端口
    sshUser: root
    sshPass: "Gzdj%1419!"
    filePath: "/home/<USER>/" # 前端包路径
    # 运维服务平台的前端包名称
    allUserDistName: "dj"
    # 党建平台的前端包名称
    djDistName: "djyw"
    cmdS:
      - "scp ${internal-service.zipSSH.sshUser}@${internal-service.zipSSH.sshHost}:{sourceFilePath} {filePath}"
      - "mv ${internal-service.nginxSSH.filePath}/{dist} ${internal-service.nginxSSH.filePath}/{dist}.`date +%y%m%d%H%M%s%S`"   # 重命名原来的前端包
      - "unzip -d ${internal-service.nginxSSH.filePath}/ {filePath}"     # 解压前端包更新到指定目录,程序替换包名
      - "mv ${internal-service.nginxSSH.filePath}/dist ${internal-service.nginxSSH.filePath}/{dist}"
      - "rm -rf {filePath}"     # 删除上传文件
      - "/usr/sbin/nginx -s reload"

  #all user server
  allSSH:
    sshHost: ************  # ssh地址
    sshPort: 22 # ssh端口
    sshUser: root
    sshPass: "Gzdj%1008!"
    filePath: "/home/<USER>/server"
    fileName: "zenith-all-user-0.0.1-SNAPSHOT.jar"
    cmdS:
      # 重命名dj服务包
      - "mv ${internal-service.allSSH.filePath}/lib/${internal-service.allSSH.fileName} ${internal-service.allSSH.filePath}/lib/${internal-service.allSSH.fileName}.`date +%y%m%d%H%M%s%S`"
      # 移动更新包到指定dj服务目录
      - "cp {filePath} ${internal-service.allSSH.filePath}/lib/${internal-service.allSSH.fileName}"
      # 重启服务
      - "sh ${internal-service.allSSH.filePath}/start.sh restart"
  #党建 server
  djSSH:
    sshHost: *************  # ssh地址
    sshPort: 22 # ssh端口
    sshUser: root
    sshPass: "Gzdj%1419!"
    filePath: "/home/<USER>/web_software/zenith-front_dj"
    fileName: "zenith-front-dj-1.1.10.301.jar"
    cmdS:
      # 重命名dj服务包
      - "mv ${internal-service.djSSH.filePath}/lib/${internal-service.djSSH.fileName} ${internal-service.djSSH.filePath}/lib/${internal-service.djSSH.fileName}.`date +%y%m%d%H%M%s%S`"
      # 移动更新包到指定dj服务目录
      - "cp {filePath} ${internal-service.djSSH.filePath}/lib/${internal-service.djSSH.fileName}"
      # 重启服务
      - "sh ${internal-service.allSSH.filePath}/start.sh restart"
  esSSH:
    sshHost: *************  # ssh地址
    sshPort: 22
    sshUser: root
    sshPass: "Gzdj%1419!"
    cmdS:
      - "sh /home/<USER>/es_clean.sh"
  # postgresql server
  pgSSH:
    sshHost: *************  # ssh地址
    sshPort: 22 # ssh端口
    sshUser: root
    sshPass: "dzgb@zt.423"
    cmdS:
      - "su postgres"
      - "cd /usr/pgsql-11/bin/"
      - "./pg_dump -d {srcDb} -O -x -c -f {destPath}"
      - "./psql -d {destDb} -f {srcPath}"
      - "exit"
  minioSSH:
    sshHost: ************  # ssh地址
    sshPort: 22 # ssh端口
    sshUser: root
    sshPass: "&GZY)#A8AA"

# minio附件打包完成回调地址
doneCallback: curl ************:8080/dataExchangeExport/doneCallback?id={}
# minio文件地址
minioDataFolderStr: /home/<USER>/minio/data/