<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.mapper.MiddleExchangeAreaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.entity.model.MiddleExchangeArea">
        <id column="id" property="id" />
        <result column="org_exchange_area_code" property="orgExchangeAreaCode" />
        <result column="d01001" property="d01001" />
        <result column="has_delete" property="hasDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, org_exchange_area_code, d01001, has_delete
    </sql>

</mapper>
