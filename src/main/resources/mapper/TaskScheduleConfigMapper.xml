<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.mapper.TaskScheduleConfigMapper">
    <resultMap id="BaseResultMap" type="com.zenith.front.entity.model.TaskScheduleConfig">
        <id column="id" property="id"/>
        <result column="task_type" property="taskType"/>
        <result column="bean_name" property="beanName"/>
        <result column="method_name" property="methodName"/>
        <result column="task_name" property="taskName"/>
        <result column="cron_expression" property="cronExpression"/>
        <result column="enabled" property="enabled"/>
        <result column="start_status" property="startStatus"/>
        <result column="description" property="description"/>
    </resultMap>
</mapper>