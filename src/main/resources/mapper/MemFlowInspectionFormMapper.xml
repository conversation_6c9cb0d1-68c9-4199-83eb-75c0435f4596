<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.mapper.MemFlowInspectionFormMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.entity.model.MemFlowInspectionForm">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="org_level_code" property="orgLevelCode" />
        <result column="a1" property="a1" />
        <result column="a2" property="a2" />
        <result column="a3" property="a3" />
        <result column="a4" property="a4" />
        <result column="a5" property="a5" />
        <result column="a6" property="a6" />
        <result column="a7" property="a7" />
        <result column="a8" property="a8" />
        <result column="a9" property="a9" />
        <result column="a10" property="a10" />
        <result column="a11" property="a11" />
        <result column="a12" property="a12" />
        <result column="a13" property="a13" />
        <result column="a14" property="a14" />
        <result column="a15" property="a15" />
        <result column="a16" property="a16" />
        <result column="a17" property="a17" />
        <result column="a18" property="a18" />
        <result column="a19" property="a19" />
        <result column="a20" property="a20" />
        <result column="a21" property="a21" />
        <result column="b1" property="b1" />
        <result column="b2" property="b2" />
        <result column="b3" property="b3" />
        <result column="b4" property="b4" />
        <result column="b5" property="b5" />
        <result column="b6" property="b6" />
        <result column="b7" property="b7" />
        <result column="b8" property="b8" />
        <result column="b9" property="b9" />
        <result column="b10" property="b10" />
        <result column="b11" property="b11" />
        <result column="b12" property="b12" />
        <result column="b13" property="b13" />
        <result column="b14" property="b14" />
        <result column="b15" property="b15" />
        <result column="b16" property="b16" />
        <result column="b17" property="b17" />
        <result column="b18" property="b18" />
        <result column="b19" property="b19" />
        <result column="b20" property="b20" />
        <result column="b21" property="b21" />
        <result column="b22" property="b22" />
        <result column="b23" property="b23" />
        <result column="b24" property="b24" />
        <result column="b25" property="b25" />
        <result column="b26" property="b26" />
        <result column="b27" property="b27" />
        <result column="b28" property="b28" />
        <result column="b29" property="b29" />
        <result column="b30" property="b30" />
        <result column="b31" property="b31" />
        <result column="b32" property="b32" />
        <result column="b33" property="b33" />
        <result column="b34" property="b34" />
        <result column="c1" property="c1" />
        <result column="c2" property="c2" />
        <result column="c3" property="c3" />
        <result column="c4" property="c4" />
        <result column="c5" property="c5" />
        <result column="c6" property="c6" />
        <result column="c7" property="c7" />
        <result column="c8" property="c8" />
        <result column="c9" property="c9" />
        <result column="c10" property="c10" />
        <result column="c11" property="c11" />
        <result column="c12" property="c12" />
        <result column="c13" property="c13" />
        <result column="c14" property="c14" />
        <result column="c15" property="c15" />
        <result column="c16" property="c16" />
        <result column="c17" property="c17" />
        <result column="d1" property="d1" />
        <result column="d2" property="d2" />
        <result column="d3" property="d3" />
        <result column="d4" property="d4" />
        <result column="d5" property="d5" />
        <result column="d6" property="d6" />
        <result column="d7" property="d7" />
        <result column="d8" property="d8" />
        <result column="d9" property="d9" />
        <result column="d10" property="d10" />
        <result column="d11" property="d11" />
        <result column="d12" property="d12" />
        <result column="d13" property="d13" />
        <result column="d14" property="d14" />
        <result column="d15" property="d15" />
        <result column="d16" property="d16" />
        <result column="d17" property="d17" />
        <result column="d18" property="d18" />
        <result column="d19" property="d19" />
        <result column="d20" property="d20" />
        <result column="d21" property="d21" />
        <result column="d22" property="d22" />
        <result column="d23" property="d23" />
        <result column="d24" property="d24" />
        <result column="d25" property="d25" />
        <result column="d26" property="d26" />
        <result column="d27" property="d27" />
        <result column="d28" property="d28" />
        <result column="d29" property="d29" />
        <result column="d30" property="d30" />
        <result column="e1" property="e1" />
        <result column="e2" property="e2" />
        <result column="e3" property="e3" />
        <result column="e4" property="e4" />
        <result column="e5" property="e5" />
        <result column="e6" property="e6" />
        <result column="e7" property="e7" />
        <result column="e8" property="e8" />
        <result column="e9" property="e9" />
        <result column="e10" property="e10" />
        <result column="e11" property="e11" />
        <result column="e12" property="e12" />
        <result column="e13" property="e13" />
        <result column="e14" property="e14" />
        <result column="e15" property="e15" />
        <result column="e16" property="e16" />
        <result column="e17" property="e17" />
        <result column="e18" property="e18" />
        <result column="e19" property="e19" />
        <result column="e20" property="e20" />
        <result column="e21" property="e21" />
        <result column="e22" property="e22" />
        <result column="e23" property="e23" />
        <result column="e24" property="e24" />
        <result column="e25" property="e25" />
        <result column="e26" property="e26" />
        <result column="e27" property="e27" />
        <result column="e28" property="e28" />
        <result column="e29" property="e29" />
        <result column="e30" property="e30" />
        <result column="e31" property="e31" />
        <result column="e32" property="e32" />
        <result column="e33" property="e33" />
        <result column="e34" property="e34" />
        <result column="e35" property="e35" />
        <result column="e36" property="e36" />
        <result column="e37" property="e37" />
        <result column="e38" property="e38" />
        <result column="e39" property="e39" />
        <result column="e40" property="e40" />
        <result column="e41" property="e41" />
        <result column="e42" property="e42" />
        <result column="e43" property="e43" />
        <result column="e44" property="e44" />
        <result column="e45" property="e45" />
        <result column="e46" property="e46" />
        <result column="e47" property="e47" />
        <result column="e48" property="e48" />
        <result column="e49" property="e49" />
        <result column="e50" property="e50" />
        <result column="e51" property="e51" />
        <result column="e52" property="e52" />
        <result column="e53" property="e53" />
        <result column="e54" property="e54" />
        <result column="e55" property="e55" />
        <result column="e56" property="e56" />
        <result column="e57" property="e57" />
        <result column="e58" property="e58" />
        <result column="e59" property="e59" />
        <result column="e60" property="e60" />
        <result column="e61" property="e61" />
        <result column="e62" property="e62" />
        <result column="e63" property="e63" />
        <result column="e64" property="e64" />
        <result column="e65" property="e65" />
        <result column="e66" property="e66" />
        <result column="e67" property="e67" />
        <result column="e68" property="e68" />
        <result column="e69" property="e69" />
        <result column="e70" property="e70" />
        <result column="f1" property="f1" />
        <result column="f2" property="f2" />
        <result column="f3" property="f3" />
        <result column="f4" property="f4" />
        <result column="f5" property="f5" />
        <result column="f6" property="f6" />
        <result column="f7" property="f7" />
        <result column="f8" property="f8" />
        <result column="f9" property="f9" />
        <result column="f10" property="f10" />
        <result column="f11" property="f11" />
        <result column="f12" property="f12" />
        <result column="f13" property="f13" />
        <result column="f14" property="f14" />
        <result column="f15" property="f15" />
        <result column="f16" property="f16" />
        <result column="f17" property="f17" />
        <result column="f18" property="f18" />
        <result column="f19" property="f19" />
        <result column="f20" property="f20" />
        <result column="f21" property="f21" />
        <result column="f22" property="f22" />
        <result column="f23" property="f23" />
        <result column="f24" property="f24" />
        <result column="f25" property="f25" />
        <result column="f26" property="f26" />
        <result column="f27" property="f27" />
        <result column="f28" property="f28" />
        <result column="f29" property="f29" />
        <result column="f30" property="f30" />
        <result column="f31" property="f31" />
        <result column="f32" property="f32" />
        <result column="f33" property="f33" />
        <result column="f34" property="f34" />
        <result column="f35" property="f35" />
        <result column="f36" property="f36" />
        <result column="f37" property="f37" />
        <result column="f38" property="f38" />
        <result column="f39" property="f39" />
        <result column="f40" property="f40" />
        <result column="f41" property="f41" />
        <result column="f42" property="f42" />
        <result column="f43" property="f43" />
        <result column="f44" property="f44" />
        <result column="f45" property="f45" />
        <result column="f46" property="f46" />
        <result column="f47" property="f47" />
        <result column="f48" property="f48" />
        <result column="f49" property="f49" />
        <result column="f50" property="f50" />
        <result column="f51" property="f51" />
        <result column="f52" property="f52" />
        <result column="f53" property="f53" />
        <result column="f54" property="f54" />
        <result column="f55" property="f55" />
        <result column="f56" property="f56" />
        <result column="f57" property="f57" />
        <result column="f58" property="f58" />
        <result column="f59" property="f59" />
        <result column="f60" property="f60" />
        <result column="f61" property="f61" />
        <result column="f62" property="f62" />
        <result column="f63" property="f63" />
        <result column="f64" property="f64" />
        <result column="f65" property="f65" />
        <result column="f66" property="f66" />
        <result column="f67" property="f67" />
        <result column="f68" property="f68" />
        <result column="f69" property="f69" />
        <result column="f70" property="f70" />
        <result column="org_name" property="orgName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, org_level_code, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11, a12, a13, a14, a15, a16, a17, a18, a19, a20, a21, b1, b2, b3, b4, b5, b6, b7, b8, b9, b10, b11, b12, b13, b14, b15, b16, b17, b18, b19, b20, b21, b22, b23, b24, b25, b26, b27, b28, b29, b30, b31, b32, b33, b34, c1, c2, c3, c4, c5, c6, c7, c8, c9, c10, c11, c12, c13, c14, c15, c16, c17, d1, d2, d3, d4, d5, d6, d7, d8, d9, d10, d11, d12, d13, d14, d15, d16, d17, d18, d19, d20, d21, d22, d23, d24, d25, d26, d27, d28, d29, d30, e1, e2, e3, e4, e5, e6, e7, e8, e9, e10, e11, e12, e13, e14, e15, e16, e17, e18, e19, e20, e21, e22, e23, e24, e25, e26, e27, e28, e29, e30, e31, e32, e33, e34, e35, e36, e37, e38, e39, e40, e41, e42, e43, e44, e45, e46, e47, e48, e49, e50, e51, e52, e53, e54, e55, e56, e57, e58, e59, e60, e61, e62, e63, e64, e65, e66, e67, e68, e69, e70, f1, f2, f3, f4, f5, f6, f7, f8, f9, f10, f11, f12, f13, f14, f15, f16, f17, f18, f19, f20, f21, f22, f23, f24, f25, f26, f27, f28, f29, f30, f31, f32, f33, f34, f35, f36, f37, f38, f39, f40, f41, f42, f43, f44, f45, f46, f47, f48, f49, f50, f51, f52, f53, f54, f55, f56, f57, f58, f59, f60, f61, f62, f63, f64, f65, f66, f67, f68, f69, f70, org_name
    </sql>
    <select id="findNewList" resultType="com.zenith.front.entity.model.MemFlowInspectionForm">
        select *
        from (select *,
                     ROW_NUMBER() OVER(PARTITION BY org_level_code  ORDER BY create_time  DESC) as rn
              from ccp_mem_flow_inspection_form where type = #{type} ) t
        where t.rn = 1
          and t.org_level_code like concat(#{orgLevelCode}, '%')
        ORDER BY LENGTH(org_level_code), org_level_code
    </select>

    <select id="eachNodeDataList" resultType="com.zenith.front.entity.model.MemFlowInspectionForm">
        select * from ccp_mem_flow_inspection_form f
        inner join (select org_level_code,max(create_time) as max_time from ccp_mem_flow_inspection_form where type = #{type}
        group by org_level_code) n
        on f.org_level_code = n.org_level_code and f.create_time = n.max_time and f.type = #{type}
    </select>

</mapper>
