<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.mapper.TransferLetterMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.entity.model.TransferLetter">
        <id column="id" property="id" />
        <result column="letter_number" property="letterNumber" />
        <result column="JSX0001" property="jsx0001" />
        <result column="JSX0002" property="jsx0002" />
        <result column="JSX0003" property="jsx0003" />
        <result column="JSX0004" property="jsx0004" />
        <result column="JSX0005" property="jsx0005" />
        <result column="JSX0006" property="jsx0006" />
        <result column="JSX0007" property="jsx0007" />
        <result column="JSX0008" property="jsx0008" />
        <result column="JSX0009" property="jsx0009" />
        <result column="JSX0010" property="jsx0010" />
        <result column="JSX0011" property="jsx0011" />
        <result column="JSX0012" property="jsx0012" />
        <result column="JSX0013" property="jsx0013" />
        <result column="JSX0014" property="jsx0014" />
        <result column="JSX0015" property="jsx0015" />
        <result column="JSX0016" property="jsx0016" />
        <result column="JSX0017" property="jsx0017" />
        <result column="JSX0018" property="jsx0018" />
        <result column="JSX0019" property="jsx0019" />
        <result column="JSX0020" property="jsx0020" />
        <result column="JSX0021" property="jsx0021" />
        <result column="JSX0022" property="jsx0022" />
        <result column="JSX0025" property="jsx0025" />
        <result column="JSX0029" property="jsx0029" />
        <result column="JSX0030" property="jsx0030" />
        <result column="JSX0031" property="jsx0031" />
        <result column="transfer_type" property="transferType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, letter_number, JSX0001, JSX0002, JSX0003, JSX0004, JSX0005, JSX0006, JSX0007, JSX0008, JSX0009, JSX0010, JSX0011, JSX0012, JSX0013, JSX0014, JSX0015, JSX0016, JSX0017, JSX0018, JSX0019, JSX0020, JSX0021, JSX0022, JSX0025, JSX0029, JSX0030, JSX0031
    </sql>

</mapper>
