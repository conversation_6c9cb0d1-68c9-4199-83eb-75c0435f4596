<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.mapper.FlowUploadMapper">

    <select id="findCheXiaoNotUpload" resultType="com.zenith.front.entity.model.FlowUploadData">
        select * from flow_upload_data where flow_uq_code in(select flow_uq_code from flow_upload_data where  flow_uq_code in(select flow_uq_code from mem_flow where flow_step ='6' and flow_uq_code like '052%' and  is_province = '0' order by create_time desc)
                                                             group by flow_uq_code HAVING count(flow_uq_code) = 1)  order by create_time desc
    </select>
</mapper>
