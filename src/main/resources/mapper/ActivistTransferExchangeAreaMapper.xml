<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.mapper.ActivistTransferExchangeAreaMapper">


    <select id="findByTargetCode" resultType="com.zenith.front.entity.model.ActivistTransferExchangeArea">
        SELECT
        *
        FROM
        activist_transfer_exchange_area
        WHERE
        update_time > (now()::timestamp +'-30 day')
        AND
        "target_org_id" in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="findBySrcCode" resultType="com.zenith.front.entity.model.ActivistTransferExchangeArea">
        SELECT
        *
        FROM
        activist_transfer_exchange_area
        WHERE
        update_time > (now()::timestamp +'-30 day')
        AND
        "src_org_id" in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


</mapper>
