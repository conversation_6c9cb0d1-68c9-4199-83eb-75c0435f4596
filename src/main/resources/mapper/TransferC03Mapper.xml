<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.mapper.TransferC03Mapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.entity.model.TransferC03">
        <id column="C03ID" property="c03id" />
        <result column="C01ID" property="c01id" />
        <result column="C03001" property="c03001" />
        <result column="C03002" property="c03002" />
        <result column="C03003" property="c03003" />
        <result column="C03004" property="c03004" />
        <result column="C03005" property="c03005" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        C03ID, C01ID, C03001, C03002, C03003, C03004, C03005
    </sql>

</mapper>
