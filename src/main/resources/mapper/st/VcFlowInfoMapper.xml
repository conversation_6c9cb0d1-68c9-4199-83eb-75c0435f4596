<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zenith.front.mapper.st.VcFlowInfoMapper">


    <select id="list" resultType="com.zenith.front.entity.model.st.VcFlowInfo">
        -- 查询指定模块 指定人员 or 指定机构审批 or 指定角色 审批数据和 历史关联数据
        select
        distinct vfi.*
        from vc_flow_info vfi
        left join vc_flow_detail vfd on vfd.delete_time is null and vfd.flow_id = vfi.id
        where vfi.delete_time is null and vfi.flow_type_code = #{dto.flowTypeCode}
          /* 审批状态*/
        <if test="dto.auditStatus != null and dto.auditStatus != ''">
            and vfi.audit_status = #{dto.auditStatus}
        </if>
        /* 当前需要审批的数据*/
        and (
            (vfi.audit_status = '1' and (vfi.audit_user_type = '1' and vfi.audit_user_id = #{dto.auditUserId}
        <if test="dto.auditRoleIds != null and dto.auditRoleIds.size() > 0">
            or (
            vfi.audit_user_type = '2' and vfi.audit_user_id in
            <foreach collection="dto.auditRoleIds" item="roleId" open="(" close=")" separator=",">
                #{roleId}
            </foreach>
            )
        </if>
        or (vfi.audit_user_type = '3' and vfi.audit_user_id = #{dto.auditOrgLevelCode})))
           /*历史审批的数据根据审核人ID具体查询*/
         or vfd.audit_user_id = #{dto.auditUserId}
        )
    </select>
</mapper>

