<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zenith.front.mapper.st.VcPleasDestroyHolidayMapper">


    <select id="list" resultType="com.zenith.front.entity.vo.st.VcPleasDestroyHolidayVO">
        select
               vpdh.*,
               flow.audit_status,
               flow.process_end,
               flow.total_point,
               flow.now_point,
               flow.audit_User_Type,
               flow.audit_User_Id,
               flow.node_Key,
               flow.id flowId
        from vc_pleas_destroy_holiday vpdh
        left join
            (
                select
                    distinct vfi.*
                from vc_flow_info vfi
                    left join vc_flow_detail vfd on vfd.delete_time is null and vfd.flow_id = vfi.id
                where
                    vfi.delete_time is null
                  and vfi.flow_type_code = #{dto.flowDto.flowTypeCode, jdbcType = VARCHAR}
                    and (
                        vfi.audit_status = '1'
                        and (
                                vfi.audit_user_type = '1' and
                                vfi.audit_user_id = #{dto.flowDto.createUserId, jdbcType = VARCHAR}
                                <if test="dto.flowDto.auditRoleIds != null and dto.flowDto.auditRoleIds.size() > 0">
                                    or (
                                        vfi.audit_user_type = '2'
                                        and vfi.audit_user_id in
                                        <foreach collection="dto.flowDto.auditRoleIds" item="roleId" open="(" close=")" separator=",">
                                            #{roleId}
                                        </foreach>
                                    )
                                </if>
                                or (
                                    vfi.audit_user_type = '3' and vfi.audit_user_id = #{dto.flowDto.auditOrgLevelCode, jdbcType = VARCHAR})
                            )
                            or vfi.create_user_id = #{dto.flowDto.createUserId, jdbcType = VARCHAR}
                            or vfd.audit_user_id = #{dto.flowDto.createUserId, jdbcType = VARCHAR}
                            <if test="dto.flowDto.auditRoleIds != null and dto.flowDto.auditRoleIds.size() > 0 and (dto.flowDto.auditRoleIds.contains('08') or dto.flowDto.auditRoleIds.contains('07'))">
                                or vfi.audit_status = '2'
                            </if>
                    )
            ) flow on flow.biz_no = vpdh.id
        where
            vpdh.delete_time is null
            and (
                <choose>
                    <when test="dto.flowDto.auditRoleIds != null and dto.flowDto.auditRoleIds.size() > 0 and dto.flowDto.auditRoleIds.contains('08')">
                        flow.id is not null and vpdh.days > 10
                    </when>
                    <when test="dto.flowDto.auditRoleIds != null and dto.flowDto.auditRoleIds.size() > 0 and dto.flowDto.auditRoleIds.contains('07')">
                        flow.id is not null and vpdh.days >= 3
                    </when>
                    <otherwise>
                        flow.id is not null
                    </otherwise>
                </choose>
                     or vpdh.create_user_id = #{dto.flowDto.createUserId, jdbcType = VARCHAR} )
            <choose>
                <when test="dto.backward != null and dto.backward == true">
                    and vpdh.org_level_code  = #{dto.orgLevelCode, jdbcType = VARCHAR}
                </when>
                <otherwise>
                    and vpdh.org_level_code like concat(#{dto.orgLevelCode, jdbcType = VARCHAR}, '%')
                </otherwise>
            </choose>
            <if test="dto.auditStatus != null and dto.auditStatus != ''">
                and flow.audit_status = #{dto.auditStatus, jdbcType = VARCHAR}
            </if>
            <if test="dto.name != null and dto.name != ''">
                and vpdh.mem_Name like concat('%', #{dto.name, jdbcType = VARCHAR}, '%')
            </if>
            <if test="dto.holidayStartDate != null">
                and vpdh.holiday_start_date >= #{dto.holidayStartDate, jdbcType = TIMESTAMP}
            </if>
            <if test="dto.holidayEndDate != null">
                and vpdh.holiday_end_date &lt;= #{dto.holidayEndDate, jdbcType = TIMESTAMP}
            </if>
            <if test="dto.startDays != null">
                and vpdh.days >= #{dto.startDays, jdbcType = INTEGER}
            </if>
            <if test="dto.endDays != null">
                and vpdh.days &lt;= #{dto.endDays, jdbcType = INTEGER}
            </if>

            <if test="dto.vcDictD200Code != null">
                and vpdh.vc_dict_d200_code = #{dto.vcDictD200Code, jdbcType = VARCHAR}
            </if>
    </select>

    <select id="isInternalList" resultType="com.zenith.front.entity.vo.st.VcPleasDestroyHolidayVO">
        select
        vpdh.*,
        flow.audit_status,
        flow.process_end,
        flow.total_point,
        flow.now_point,
        flow.audit_User_Type,
        flow.audit_User_Id,
        flow.node_Key,
        flow.id flowId
        from vc_pleas_destroy_holiday vpdh
        left join
        (
        select
        distinct vfi.*
        from vc_flow_info vfi
        left join vc_flow_detail vfd on vfd.delete_time is null and vfd.flow_id = vfi.id
        where
        vfi.delete_time is null

        ) flow on flow.biz_no = vpdh.id
        where
        vpdh.delete_time is null
        and flow.flow_type_code = #{dto.flowDto.flowTypeCode, jdbcType = VARCHAR}
        <choose>
            <when test="dto.backward != null and dto.backward == true">
                and vpdh.org_level_code  = #{dto.orgLevelCode, jdbcType = VARCHAR}
            </when>
            <otherwise>
                and vpdh.org_level_code like concat(#{dto.orgLevelCode, jdbcType = VARCHAR}, '%')
            </otherwise>
        </choose>
        <if test="dto.auditStatus != null and dto.auditStatus != ''">
            and flow.audit_status = #{dto.auditStatus, jdbcType = VARCHAR}
        </if>
        <if test="dto.name != null and dto.name != ''">
            and vpdh.mem_Name like concat('%', #{dto.name, jdbcType = VARCHAR}, '%')
        </if>
        <if test="dto.holidayStartDate != null">
            and vpdh.holiday_start_date >= #{dto.holidayStartDate, jdbcType = TIMESTAMP}
        </if>
        <if test="dto.holidayEndDate != null">
            and vpdh.holiday_end_date &lt;= #{dto.holidayEndDate, jdbcType = TIMESTAMP}
        </if>
        <if test="dto.startDays != null">
            and vpdh.days >= #{dto.startDays, jdbcType = INTEGER}
        </if>
        <if test="dto.endDays != null">
            and vpdh.days &lt;= #{dto.endDays, jdbcType = INTEGER}
        </if>

        <if test="dto.vcDictD200Code != null">
            and vpdh.vc_dict_d200_code = #{dto.vcDictD200Code, jdbcType = VARCHAR}
        </if>
    </select>
</mapper>

