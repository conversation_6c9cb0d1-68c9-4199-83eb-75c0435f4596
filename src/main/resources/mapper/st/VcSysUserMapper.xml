<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zenith.front.mapper.st.VcSysUserMapper">

    <select id="findPageOn" resultType="com.zenith.front.entity.vo.st.VcSysUserVO">
        select
        vsu.user_id,
        su.account,
        vsu.role_code,
        vsu.origin_org_level_code
        from vc_sys_user vsu
        left join all_user su on su.user_id = vsu.user_id
        where vsu.origin_org_level_code = #{dto.orgLevelCode}
        <if test="dto.vcRoleCode != null and dto.vcRoleCode != ''">
            and vsu.role_code = #{dto.vcRoleCode}
        </if>
    </select>
</mapper>

