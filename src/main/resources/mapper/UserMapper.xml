<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.mapper.UserMapper">

    <select id="findByAccount" resultType="com.zenith.front.entity.model.User">
        SELECT *
        FROM all_user
        WHERE is_delete = 0
          AND account = #{account}
    </select>

    <select id="findByUserId" resultType="com.zenith.front.entity.model.User">
        SELECT
        *
        FROM
        all_user
        WHERE
        user_id = #{userId}
    </select>

    <select id="findByUkey" resultType="com.zenith.front.entity.model.User">
        SELECT
        *
        FROM
        all_user
        WHERE
        ukey = #{ukey}
    </select>


</mapper>
