<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.mapper.OrgFlowMapper">


    <select id="findMaxD01001" resultType="com.zenith.front.entity.model.OrgFlow">
        SELECT d01001
        FROM ccp_org_flow
        WHERE d01001 IS NOT NULL
        ORDER BY CAST(SUBSTRING(d01001 FROM '\d{8}$') AS INTEGER) DESC
            LIMIT 1
    </select>

    <select id="searchFLowOrg" resultType="com.zenith.front.entity.vo.OrgExchangeFlowVO">
        SELECT
            code as code,
            org_code as orgCode,
            parent_name AS parentName,
            "name" AS name,
            source_type AS dataType,
            administrative_division as divisionCode,
            case when source_type = '1' then '1' else '0' end as type,
            contacter as contacter,
            contact_phone as contactPhone,
            d200_code as d200Code,
            '0' as flowOutStatus
        FROM
            ccp_org_flow
        WHERE
            is_enable = 1 AND "name" like CONCAT('%',#{orgName},'%') AND delete_time is null
            <if test="d01Code != null and d01Code != ''">
                and d01_code = #{d01Code}
            </if>
        UNION
        SELECT
            d01id as code,
            d01001 as orgCode,
            '' AS parentName,
            d01002 AS name,
            '2' AS dataType,
            d01008 as divisionCode,
            '0' AS type,
            d01005 as contacter,
            d01006 as contactPhone,
            case when d01012 = '1' then '1' else '2' end as d200Code,
            '0' as flowOutStatus
        FROM
            transfer_org_flow
        WHERE
            d01002 like CONCAT('%',#{orgName},'%') and d01up1 ='0'
            <if test="d01Code != null and d01Code != ''">
                and d01004 = #{d01Code}
            </if>
    </select>

    <select id="searchFlowOrgOne" resultType="com.zenith.front.entity.vo.OrgExchangeFlowVO">
        SELECT
            code as code,
            org_code as orgCode,
            parent_name AS parentName,
            "name" AS name,
            source_type AS dataType,
            administrative_division as divisionCode,
            case when source_type = '1' then '1' else '0' end as type,
            contacter as contacter,
            contact_phone as contactPhone
        FROM
            ccp_org_flow
        WHERE
            is_enable = 1 AND delete_time is null
            <if test="code != null and code !=''">
                and code = #{code}
            </if>
        UNION
        SELECT
            d01id as code,
            d01001 as orgCode,
            '' AS parentName,
            d01002 AS name,
            '2' AS dataType,
            d01008 as divisionCode,
            '0' AS type,
            d01005 as contacter,
            d01006 as contactPhone
        FROM
            transfer_org_flow
        WHERE
            d01up1 ='0'
            <if test="code != null and code !=''">
                and d01id = #{code}
            </if>
    </select>

</mapper>