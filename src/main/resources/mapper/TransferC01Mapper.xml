<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.mapper.TransferC01Mapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.entity.model.TransferC01">
        <id column="id" property="id" />
        <result column="letter_id" property="letterId" />
        <result column="C01ID" property="c01id" />
        <result column="D01ID" property="d01id" />
        <result column="C01001" property="c01001" />
        <result column="C01002" property="c01002" />
        <result column="C0100P" property="c0100p" />
        <result column="C01003" property="c01003" />
        <result column="C01004" property="c01004" />
        <result column="C01005" property="c01005" />
        <result column="C01006" property="c01006" />
        <result column="C01007" property="c01007" />
        <result column="C01008" property="c01008" />
        <result column="C01009" property="c01009" />
        <result column="C01010" property="c01010" />
        <result column="C01011" property="c01011" />
        <result column="C01012" property="c01012" />
        <result column="C01013" property="c01013" />
        <result column="C01014" property="c01014" />
        <result column="C01015" property="c01015" />
        <result column="C01016" property="c01016" />
        <result column="C01017" property="c01017" />
        <result column="C01018" property="c01018" />
        <result column="C01019" property="c01019" />
        <result column="C01020" property="c01020" />
        <result column="C01021" property="c01021" />
        <result column="C01022" property="c01022" />
        <result column="C01023" property="c01023" />
        <result column="C01024" property="c01024" />
        <result column="C01025" property="c01025" />
        <result column="C01026" property="c01026" />
        <result column="C01027" property="c01027" />
        <result column="C01028" property="c01028" />
        <result column="C01UP1" property="c01up1" />
        <result column="C01UP2" property="c01up2" />
        <result column="C01UP3" property="c01up3" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, letter_id, C01ID, D01ID, C01001, C01002, C0100P, C01003, C01004, C01005, C01006, C01007, C01008, C01009, C01010, C01011, C01012, C01013, C01014, C01015, C01016, C01017, C01018, C01019, C01020, C01021, C01022, C01023, C01024, C01025, C01026, C01027, C01028, C01UP1, C01UP2, C01UP3
    </sql>

</mapper>
