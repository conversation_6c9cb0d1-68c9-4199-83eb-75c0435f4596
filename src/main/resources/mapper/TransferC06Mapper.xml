<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.mapper.TransferC06Mapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.entity.model.TransferC06">
        <id column="C01ID" property="c01id" />
        <result column="C06001" property="c06001" />
        <result column="C06002" property="c06002" />
        <result column="C06003" property="c06003" />
        <result column="C06004" property="c06004" />
        <result column="C06005" property="c06005" />
        <result column="C06006" property="c06006" />
        <result column="C06007" property="c06007" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        C01ID, C06001, C06002, C06003, C06004, C06005, C06006, C06007
    </sql>

</mapper>
