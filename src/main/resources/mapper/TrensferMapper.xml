<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zenith.front.mapper.TransFerMapper">

    <select id="findByTargetCode" resultType="com.zenith.front.entity.model.TransFerExchangeArea">
        SELECT
        *
        FROM
        transfer_exchange_area
        WHERE
<!--        delete_time is null-->
<!--        and-->
        update_time >  (now()::timestamp +'-190 day')
        AND
        "target_org_id" in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>



    <select id="findBySrcCode" resultType="com.zenith.front.entity.model.TransFerExchangeArea">
        SELECT
        *
        FROM
        transfer_exchange_area
        WHERE
        update_time >  (now()::timestamp +'-190 day')
        AND
        "src_org_id" in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>



    <select id="findBySrcCodeAndType" resultType="com.zenith.front.entity.model.TransFerExchangeArea">
        SELECT
        *
        FROM
        transfer_exchange_area
        WHERE
        create_time >  (now()::timestamp +'-101 day')
        AND
        type = '224'
        AND
        status = 2
        AND
        "src_org_id" in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findByTargetCodeAndType" resultType="com.zenith.front.entity.model.TransFerExchangeArea">
        SELECT
        *
        FROM
        transfer_exchange_area
        WHERE
        create_time >  (now()::timestamp +'-90 day')
        AND
        "target_org_id" in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findBySrcExchangeKey" resultType="com.zenith.front.entity.model.TransFerExchangeArea">
        SELECT
        *
        FROM
        transfer_exchange_area
        WHERE
        update_time >  (now()::timestamp +'-90 day')
        AND
        src_exchange_key = #{orgByExchangeKey}
    </select>




    <select id="findByTargetCompleteCode" resultType="com.zenith.front.entity.model.TransFerExchangeArea">
        SELECT
        *
        FROM
        transfer_exchange_area
        WHERE
        (type IS NULL OR type=212)
        AND
        "target_org_id" in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>



    <select id="findBySrcCompleteCode" resultType="com.zenith.front.entity.model.TransFerExchangeArea">
        SELECT
        *
        FROM
        transfer_exchange_area
        WHERE
        (type IS NULL OR type=212)
        AND
        "src_org_id" in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>




    <select id="findByTransferId" resultType="com.zenith.front.entity.model.TransFerExchangeArea">
        SELECT
        *
        FROM
        transfer_exchange_area
        WHERE
        transfer_id = #{transferId}
    </select>

</mapper>
