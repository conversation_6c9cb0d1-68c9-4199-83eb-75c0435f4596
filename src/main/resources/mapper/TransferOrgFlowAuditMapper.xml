<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.mapper.TransferOrgFlowAuditMapper">


    <select id="findNginxKey" resultType="java.lang.String">
        select node_key
        from dev_ops
        where datname = (select city from dict_d151 where key = #{d01008}) limit 1
    </select>
</mapper>
