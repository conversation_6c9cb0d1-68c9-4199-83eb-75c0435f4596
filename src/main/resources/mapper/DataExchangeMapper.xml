<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.mapper.DataExchangeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.entity.model.DataExchange">
        <id column="id" property="id"/>
        <result column="org_code" property="orgCode"/>
        <result column="org_name" property="orgName"/>
        <result column="file_name" property="fileName"/>
        <result column="upload_capacity" property="uploadCapacity"/>
        <result column="file_url" property="fileUrl"/>
        <result column="file_format" property="fileFormat"/>
        <result column="accept" property="accept"/>
        <result column="clean" property="clean"/>
        <result column="user_id" property="userId"/>
        <result column="update_account" property="updateAccount"/>
        <result column="version_number" property="versionNumber"/>
        <result column="accept_time" property="acceptTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, org_code, org_name, file_name, upload_capacity, file_url, file_format, accept, clean, user_id, update_account, version_number, accept_time, create_time
    </sql>

</mapper>
