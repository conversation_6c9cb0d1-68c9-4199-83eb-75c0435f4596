<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.mapper.UnitExchangeAreaMapper">

    <select id="findOrgByName" resultType="com.zenith.front.entity.model.UnitExchangeArea">
        SELECT
         *
        FROM
        "unit_exchange_area"
        <where>
            "delete_time" is NULL
            <if test="unitListDTO.orgName != null and unitListDTO.orgName != ''">
                and "name" like concat('%',#{unitListDTO.orgName},'%')
            </if>
            <if test="unitListDTO.code != null and unitListDTO.code != ''">
                and "code" = #{unitListDTO.code}
            </if>
        </where>
        ORDER BY "id" DESC
    </select>


    <select id="findUnitByCode" resultType="com.zenith.front.entity.model.UnitExchangeArea" parameterType="java.lang.String">
        select * from "unit_exchange_area" where "code" = #{code}
    </select>

</mapper>
