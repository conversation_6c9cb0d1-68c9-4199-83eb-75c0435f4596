<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.mapper.TransferProcessMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.entity.model.TransferProcess">
        <id column="id" property="id" />
        <result column="letter_id" property="letterId" />
        <result column="letter_number" property="letterNumber" />
        <result column="BLGC0001" property="blgc0001" />
        <result column="BLGC0002" property="blgc0002" />
        <result column="BLGC0003" property="blgc0003" />
        <result column="BLGC0004" property="blgc0004" />
        <result column="BLGC0005" property="blgc0005" />
        <result column="BLGC0006" property="blgc0006" />
        <result column="BLGC0007" property="blgc0007" />
        <result column="BLGC0008" property="blgc0008" />
        <result column="BLGC0009" property="blgc0009" />
        <result column="BLGC0010" property="blgc0010" />
        <result column="BLGC0011" property="blgc0011" />
        <result column="BLGC0012" property="blgc0012" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, letter_id, letter_number, BLGC0001, BLGC0002, BLGC0003, BLGC0004, BLGC0005, BLGC0006, BLGC0007, BLGC0008, BLGC0009, BLGC0010, BLGC0011, BLGC0012
    </sql>

</mapper>
