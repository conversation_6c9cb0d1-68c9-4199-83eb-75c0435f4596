<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.mapper.TransferC05Mapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.entity.model.TransferC05">
        <id column="C05ID" property="c05id" />
        <result column="C01ID" property="c01id" />
        <result column="C05001" property="c05001" />
        <result column="C05002" property="c05002" />
        <result column="C05003" property="c05003" />
        <result column="C05004" property="c05004" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        C05ID, C01ID, C05001, C05002, C05003, C05004
    </sql>

</mapper>
