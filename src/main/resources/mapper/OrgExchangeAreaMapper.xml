<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.mapper.OrgExchangeAreaMapper">

    <select id="findOrgByName" resultType="com.zenith.front.entity.model.OrgExchangeArea">
        SELECT
         *
        FROM
        "org_exchange_area"
        <where>
            "delete_time" is NULL
            <if test="orgListDTO.orgName != null and orgListDTO.orgName != ''">
                and "name" like concat('%',#{orgListDTO.orgName},'%')
            </if>
            <if test="orgListDTO.d01CodeList != null and orgListDTO.d01CodeList.size() > 0">
                and "d01_code" in
                <foreach item="item" index="index" collection="orgListDTO.d01CodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="orgListDTO.orgTypeList != null and orgListDTO.orgTypeList.size() > 0">
                and "org_type" in
                <foreach item="item" index="index" collection="orgListDTO.orgTypeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="orgListDTO.orgCode != null and orgListDTO.orgCode != ''">
                and "org_code" like concat(#{orgListDTO.orgCode},'%')
                and (d01_code like '63%' or d01_code like '931%' or d01_code like '932%')
            </if>

        </where>
        ORDER BY length("org_code"),"create_time" DESC,"id" DESC
    </select>


    <select id="findOrgByCode" resultType="com.zenith.front.entity.model.OrgExchangeArea" parameterType="java.lang.String">
        select * from "org_exchange_area" where "code" = #{code}
    </select>


    <select id="findOrgByExchangeKey" resultType="java.lang.String" parameterType="java.lang.String">
        select code from "org_exchange_area" where "exchange_key" = #{exchangeKey}
    </select>

    <select id="findOrgAndParents" resultType="com.zenith.front.entity.model.OrgExchangeArea">
        WITH RECURSIVE org AS (
            SELECT code,
                   org_code,
                   is_approval_mem,
                   parent_code,
                   exchange_key,
                   name,
                   contacter,
                   contact_phone
            FROM org_exchange_area
            WHERE code = #{code}
            UNION ALL
            SELECT org_exchange_area.code,
                   org_exchange_area.org_code,
                   org_exchange_area.is_approval_mem,
                   org_exchange_area.parent_code,
                   org_exchange_area.exchange_key,
                   org_exchange_area.name,
                   org_exchange_area.contacter,
                   org_exchange_area.contact_phone
            FROM org_exchange_area,
                 org
            WHERE org_exchange_area.code = org.parent_code)
        SELECT *
        FROM org
        WHERE exchange_key = #{exchangeKey}
        ORDER BY "length"(org_code) DESC;
    </select>

    <select id="findApprovalOrg" resultType="com.zenith.front.entity.vo.OrgExchangeAreaVO">
        SELECT
               id,
               d01id as code,
               d01001 AS parentName,
               d01002 AS name,
               d01003 AS contacter,
               d01004 AS contactPhone,
               '0' AS type,
               '' as orgCode,
               d01008 AS administrativeRegion,
               d01008 AS divisionCode,
               jddm as nodeCode
        FROM
            transfer_org
        WHERE
            d01up1 = '0' AND d01002 like CONCAT('%',#{orgName},'%')
        UNION
        SELECT
               id::TEXT as id,
               code as code,
               parent_name as parentName,
               name,
               contacter,
               contact_phone as contactPhone,
               '1' AS type,
               org_code as orgCode,
               administrative_region AS administrativeRegion,
               administrative_region AS divisionCode,
               '052000000001' as nodeCode
        FROM
            org_exchange_area
        WHERE
            is_approval_mem = '1' AND "name" like CONCAT('%',#{orgName},'%')
    </select>

    <select id="findOrgD01001" resultType="com.zenith.front.entity.vo.OrgD01001VO">
        select name orgName, org_code orgCode, d01001  from org_exchange_area
        <where>
            delete_time is null
            and d01001 is not null
            <if test="dto.orgName != null and dto.orgName != ''">
                and name like concat('%', #{dto.orgName}, '%')
            </if>

            <if test="dto.orgCode != null and dto.orgCode != ''">
                and org_code like concat('%', #{dto.orgCode}, '%')
            </if>
        </where>
    </select>

    <select id="findTransferOrg" resultType="com.zenith.front.entity.vo.OrgExchangeAreaVO">
        SELECT
            id,
            d01id as code,
            d01001 AS parentName,
            d01002 AS name,
            d01003 AS contacter,
            d01004 AS contactPhone,
            '0' AS type,
            '' as orgCode,
            d01008 AS administrativeRegion,
            d01008 AS divisionCode,
            jddm as nodeCode
        FROM
            transfer_org
        WHERE
            d01up1 = '0' AND d01002 like CONCAT('%',#{orgName},'%')
    </select>


</mapper>
