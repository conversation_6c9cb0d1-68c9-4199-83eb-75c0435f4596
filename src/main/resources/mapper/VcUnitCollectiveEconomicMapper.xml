<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.villagecommunity.mapper.VcUnitCollectiveEconomicMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.villagecommunity.model.VcUnitCollectiveEconomic">
        <result column="code" property="code"/>
        <result column="org_code" property="orgCode"/>
        <result column="economic_org_code" property="economicOrgCode"/>
        <result column="industry_name" property="industryName"/>
        <result column="income_channel" property="incomeChannel"/>
        <result column="income_industry" property="incomeIndustry"/>
        <result column="delete_time" property="deleteTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="unit_code" property="unitCode"/>
        <result column="unit_name" property="unitName"/>
        <result column="d128_code" property="d128Code"/>
        <result column="d128_name" property="d128Name"/>
        <result column="develop_money" property="developMoney"/>
        <result column="d129_code" property="d129Code"/>
        <result column="d129_name" property="d129Name"/>
        <result column="organ_amount" property="organAmount"/>
        <result column="benefit_population" property="benefitPopulation"/>
        <result column="has_economic_village" property="hasEconomicVillage"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code
        , org_code, economic_org_code, industry_name, income_channel, income_industry, delete_time, create_time, update_time, unit_code, unit_name, d128_code, d128_name, develop_money, d129_code, d129_name, organ_amount, benefit_population, has_economic_village
    </sql>

</mapper>
