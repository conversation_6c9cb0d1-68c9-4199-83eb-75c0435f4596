<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.mapper.DevOpsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.entity.model.DevOps">
        <id column="datname" property="datname"/>
        <result column="node_key" property="nodeKey"/>
        <result column="node_name" property="nodeName"/>
        <result column="server_location" property="serverLocation"/>
        <result column="server_port" property="serverPort"/>
        <result column="server_directory" property="serverDirectory"/>
        <result column="create_time" property="createTime"/>
        <result column="status" property="status"/>
        <result column="sort" property="sort"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        datname, node_key, node_name, server_location, server_port, server_directory, create_time, status,sort
    </sql>

    <select id="getList" resultType="com.zenith.front.entity.model.DevOps">
        select * from dev_ops
        where server_port is not null
        <if test="data.status!=null and data.status!=''">
            and status=#{data.status}
        </if>
        <if test="data.keyword!=null and data.keyword!=''">
            and node_name like concat('%',#{data.keyword},'%')
        </if>
        <if test="data.node!=null and data.node!=''">
            and datname like concat(#{data.node},'%')
        </if>
        order by sort,datname
    </select>

</mapper>
