<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.villagecommunity.mapper.VcUnitIncomeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.villagecommunity.model.VcUnitIncome">
        <result column="code" property="code"/>
        <result column="economic_code" property="economicCode"/>
        <result column="income" property="income"/>
        <result column="income_amount" property="incomeAmount"/>
        <result column="delete_time" property="deleteTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
        <result column="incur_debts_amount" property="incurDebtsAmount"/>
        <result column="outlay" property="outlay"/>
        <result column="property" property="property"/>
        <result column="ownership" property="ownership"/>
        <result column="unit_code" property="unitCode"/>
        <result column="has_secretary_economy" property="hasSecretaryEconomy"/>
        <result column="collective_economic_liabilities" property="collectiveEconomicLiabilities"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code
        , economic_code, income, income_amount, delete_time, create_time, update_time, remark, incur_debts_amount, outlay, property, ownership, unit_code, has_secretary_economy, collective_economic_liabilities
    </sql>


</mapper>
