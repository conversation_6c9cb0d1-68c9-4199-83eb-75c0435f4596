<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.villagecommunity.mapper.VcMemInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.villagecommunity.model.VcMemInfo">
        <id column="code" property="code"/>
        <result column="idcard" property="idcard"/>
        <result column="party_member" property="partyMem"/>
        <result column="join_org_date" property="joinOrgDate"/>
        <result column="d08_code" property="d08Code"/>
        <result column="mem_code" property="memCode"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code, idcard, party_mem, join_org_date, d08_code, mem_code
    </sql>

    <delete id="clear">
        DELETE FROM vc_mem_info;
    </delete>
</mapper>
