<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.mapper.TransferOrgMapper">

    <select id="findOutsideOrgByName" resultType="com.zenith.front.entity.model.TransferOrg" parameterType="java.lang.String">
        SELECT
         *
        FROM
        "transfer_org"
        WHERE
        d01002  like concat('%',#{orgName},'%')
        AND
        d01up1 = '0'

    </select>

</mapper>
