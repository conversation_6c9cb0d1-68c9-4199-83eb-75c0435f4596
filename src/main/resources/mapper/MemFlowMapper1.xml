<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.mapper.MemFlow1Mapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.entity.model.MemFlow1">
        <id column="code" property="code"/>
        <result column="flow_uq_code" property="flowUqCode"/>
        <result column="mem_code" property="memCode"/>
        <result column="mem_name" property="memName"/>
        <result column="mem_sex_code" property="memSexCode"/>
        <result column="mem_sex_name" property="memSexName"/>
        <result column="mem_idcard" property="memIdcard"/>
        <result column="mem_phone" property="memPhone"/>
        <result column="mem_org_code" property="memOrgCode"/>
        <result column="mem_org_name" property="memOrgName"/>
        <result column="mem_org_org_code" property="memOrgOrgCode"/>
        <result column="mem_org_phone" property="memOrgPhone"/>
        <result column="mem_d09_code" property="memD09Code"/>
        <result column="mem_d09_name" property="memD09Name"/>
        <result column="mem_info" property="memInfo"/>
        <result column="is_province" property="isProvince"/>
        <result column="cross_node" property="crossNode"/>
        <result column="out_place_code" property="outPlaceCode"/>
        <result column="out_place_name" property="outPlaceName"/>
        <result column="out_org_code" property="outOrgCode"/>
        <result column="out_org_name" property="outOrgName"/>
        <result column="out_org_branch_code" property="outOrgBranchCode"/>
        <result column="out_org_branch_name" property="outOrgBranchName"/>
        <result column="out_org_branch_org_code" property="outOrgBranchOrgCode"/>
        <result column="out_administrative_division_code" property="outAdministrativeDivisionCode"/>
        <result column="out_administrative_division_name" property="outAdministrativeDivisionName"/>
        <result column="lost_contact_code" property="lostContactCode"/>
        <result column="lost_contact_name" property="lostContactName"/>
        <result column="flow_type_code" property="flowTypeCode"/>
        <result column="flow_type_name" property="flowTypeName"/>
        <result column="flow_reason_code" property="flowReasonCode"/>
        <result column="flow_reason_name" property="flowReasonName"/>
        <result column="out_time" property="outTime"/>
        <result column="register_time" property="registerTime"/>
        <result column="out_org_remarks" property="outOrgRemarks"/>
        <result column="out_org_d04_code" property="outOrgD04Code"/>
        <result column="out_org_d04_name" property="outOrgD04Name"/>
        <result column="party_expenses_out_time" property="partyExpensesOutTime"/>
        <result column="party_expenses_in_time" property="partyExpensesInTime"/>
        <result column="in_org_life" property="inOrgLife"/>
        <result column="in_org_life_code" property="inOrgLifeCode"/>
        <result column="in_org_life_name" property="inOrgLifeName"/>
        <result column="in_feedback" property="inFeedback"/>
        <result column="mz_appraisal" property="mzAppraisal"/>
        <result column="is_hold" property="isHold"/>
        <result column="has_flow_back" property="hasFlowBack"/>
        <result column="flow_back_time" property="flowBackTime"/>
        <result column="cancel_time" property="cancelTime"/>
        <result column="stop_time" property="stopTime"/>
        <result column="flow_out" property="flowOut"/>
        <result column="flow_in" property="flowIn"/>
        <result column="flow_step" property="flowStep"/>
        <result column="in_org_code" property="inOrgCode"/>
        <result column="in_org_name" property="inOrgName"/>
        <result column="in_org_phone" property="inOrgPhone"/>
        <result column="in_org_d04_code" property="inOrgD04Code"/>
        <result column="in_org_d04_name" property="inOrgD04Name"/>
        <result column="in_unit_d04_code" property="inUnitD04Code"/>
        <result column="in_unit_d04_name" property="inUnitD04Name"/>
        <result column="in_unit_d16_code" property="inUnitD16Code"/>
        <result column="in_unit_d16_name" property="inUnitD16Name"/>
        <result column="in_mem_d09_code" property="inMemD09Code"/>
        <result column="in_mem_d09_name" property="inMemD09Name"/>
        <result column="in_mem_d20_code" property="inMemD20Code"/>
        <result column="in_mem_d20_name" property="inMemD20Name"/>
        <result column="in_mem_phone" property="inMemPhone"/>
        <result column="in_receiving_time" property="inReceivingTime"/>
        <result column="move_to_county_time" property="moveToCountyTime"/>
        <result column="has_county_library" property="hasCountyLibrary"/>
        <result column="reject_reason_code" property="rejectReasonCode"/>
        <result column="reject_reason_name" property="rejectReasonName"/>
        <result column="reject_time" property="rejectTime"/>
        <result column="reject_org_code" property="rejectOrgCode"/>
        <result column="reject_org_name" property="rejectOrgName"/>
        <result column="reject_org_phone" property="rejectOrgPhone"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_account" property="updateAccount"/>
        <result column="paired_contact" property="pairedContact"/>
        <result column="paired_contact_phone" property="pairedContactPhone"/>
    </resultMap>

    <select id="findCallbackListByOrgCode" resultType="com.zenith.front.entity.model.MemFlow1">
        SELECT *
        FROM mem_flow
        WHERE (
        flow_step &lt;&gt; '1'
        <if test="orgCodeList!= null and orgCodeList.size >0">
            AND(
            <trim prefixOverrides="OR">
                <foreach collection="orgCodeList" item="items" index="index" separator="">
                    OR mem_org_org_code like concat(#{items},'%')
                </foreach>
            </trim>
            )
        </if>
        )
        <if test="extraOrgCodeList!= null and extraOrgCodeList.size >0">
            OR (
            <trim prefixOverrides="OR">
                <foreach collection="extraOrgCodeList" item="items" index="index" separator="">
                    OR out_org_branch_org_code like concat(#{items},'%')
                </foreach>
            </trim>
            )
        </if>
    </select>
    <select id="findMaxFlow" resultType="com.zenith.front.entity.model.MemFlow1">
        select * from mem_flow where flow_uq_code like concat(#{data},'%') and flow_uq_code is not null  order by flow_uq_code desc limit 1
    </select>
    <select id="findNotReceiveList" resultType="java.lang.String">
        SELECT mem_flow.code
        FROM mem_flow
        WHERE mem_flow.is_province = '0'
        AND mem_flow.flow_step = '1'
        and mem_flow.flow_uq_code like '052%'
        AND EXISTS (
        SELECT 1
        FROM flow_content
        WHERE flow_content.date_type LIKE '%根据根节点批量接收信息下载接口PullMemFlowStatusReceiveTask%'
        AND flow_content.flow_uq_code LIKE CONCAT('%', mem_flow.flow_uq_code, '%')
        )
        ORDER BY mem_flow.create_time DESC;
    </select>
    <select id="findRepetitionNotUpload" resultType="java.lang.String">
        select code from mem_flow where flow_uq_code in(select flow_uq_code from mem_flow where flow_uq_code like '052%' GROUP BY flow_uq_code HAVING count(flow_uq_code) > 1)
         and create_time &gt;= '2025-01-01' and flow_step = '1'
    </select>
    <select id="findLxfsCallbackListByOrgCodeList" resultType="com.zenith.front.entity.model.MemFlow1">
        SELECT code,out_org_contact,out_org_contact_phone
        FROM mem_flow
        WHERE (
        flow_step = '1' and mem_flow.is_province ='0' and (out_org_contact is not null or out_org_contact_phone is not null)
        <if test="orgCodeList!= null and orgCodeList.size >0">
            AND(
            <trim prefixOverrides="OR">
                <foreach collection="orgCodeList" item="items" index="index" separator="">
                    OR mem_org_org_code like concat(#{items},'%')
                </foreach>
            </trim>
            )
        </if>
        )
    </select>
</mapper>
