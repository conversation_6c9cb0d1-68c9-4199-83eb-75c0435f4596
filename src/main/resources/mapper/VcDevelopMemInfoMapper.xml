<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.villagecommunity.mapper.VcDevelopMemInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.villagecommunity.model.VcDevelopMemInfo">
        <id column="code" property="code" />
        <result column="name" property="name" />
        <result column="idcard" property="idcard" />
        <result column="d06_code" property="d06Code" />
        <result column="d06_name" property="d06Name" />
        <result column="d48_code" property="d48Code" />
        <result column="d48_name" property="d48Name" />
        <result column="sex_code" property="sexCode" />
        <result column="sex_name" property="sexName" />
        <result column="phone" property="phone" />
        <result column="d07_code" property="d07Code" />
        <result column="d07_name" property="d07Name" />
        <result column="d09_code" property="d09Code" />
        <result column="d09_name" property="d09Name" />
        <result column="d08_code" property="d08Code" />
        <result column="d08_name" property="d08Name" />
        <result column="org_code" property="orgCode" />
        <result column="org_name" property="orgName" />
        <result column="create_time" property="createTime" />
        <result column="data_type" property="dataType" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code, name, idcard, d06_code, d06_name, d48_code, d48_name, sex_code, sex_name, phone, d07_code, d07_name, d09_code, d09_name, d08_code, d08_name, org_code, org_name, create_time, data_type, update_time, delete_time
    </sql>

</mapper>
