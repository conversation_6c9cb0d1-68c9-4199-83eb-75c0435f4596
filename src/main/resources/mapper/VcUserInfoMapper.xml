<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.mapper.VcUserInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.villagecommunity.model.VcUserInfo">
        <id column="id" property="id" />
        <result column="account" property="account" />
        <result column="password" property="password" />
        <result column="idcard" property="idcard" />
        <result column="data_type" property="dataType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, account, password, idcard, data_type
    </sql>

</mapper>
