<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.mapper.VcUnitInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.villagecommunity.model.VcUnitInfo">
        <id column="code" property="code" />
        <result column="name" property="name" />
        <result column="credit_code" property="creditCode" />
        <result column="d04_code" property="d04Code" />
        <result column="d04_name" property="d04Name" />
        <result column="d35_code" property="d35Code" />
        <result column="d35_name" property="d35Name" />
        <result column="is_legal" property="isLegal" />
        <result column="org_code" property="orgCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code, name, credit_code, d04_code, d04_name, d35_code, d35_name, is_legal, org_code
    </sql>

</mapper>
