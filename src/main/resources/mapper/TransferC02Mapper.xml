<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.mapper.TransferC02Mapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.entity.model.TransferC02">
        <id column="C02ID" property="c02id" />
        <result column="C01ID" property="c01id" />
        <result column="C02001" property="c02001" />
        <result column="C02002" property="c02002" />
        <result column="C02003" property="c02003" />
        <result column="C02004" property="c02004" />
        <result column="C02005" property="c02005" />
        <result column="C02006" property="c02006" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        C02ID, C01ID, C02001, C02002, C02003, C02004, C02005, C02006
    </sql>

</mapper>
