server:
  netty:
    connection-timeout: 432000
db:
  host:
    *************
  sshPort: 22
  port:
    5432
spring:
  datasource:
    dynamic:
      druid:
        maxActive: 40
        initialSize: 1
        maxWait: 30000
        minIdle: 1
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        validationQuery: select 'x'
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
      primary: allUser
      datasource:
        allUser: # 交换区库
          driver-class-name: org.postgresql.Driver
          url: jdbc:postgresql://${db.host}:${db.port}/all_user?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=GMT%2B8
          username: postgres
          password: 20191809
  # 自定义动态数据源
  customize:
    datasource:
      gz_dj:
        driver-class-name: org.postgresql.Driver
        url: jdbc:postgresql://${db.host}:${db.port}/gz_dj?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=GMT%2B8
        username: postgres
        password: 20191809
      gz_dj_tmp:
        driver-class-name: org.postgresql.Driver
        url: jdbc:postgresql://${db.host}:${db.port}/gz_dj_temp?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=GMT%2B8
        username: postgres
        password: 20191809
      all_user_tmp:
        driver-class-name: org.postgresql.Driver
        url: jdbc:postgresql://${db.host}:${db.port}/all_user_temp?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=GMT%2B8
        username: postgres
        password: 20191809
  redis:
    redisson:
      file: classpath:redisson.yml
    timeout: 2000 # 连接或读取超时时长（毫秒）
    database: 21
    jedis:
      pool:
        max-active: 8 # 连接池最大连接数（使用负值表示没有限制）
        max-wait: 800 # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 8 # 连接池中的最大空闲连接
        min-idle: 2 # 连接池中的最小空闲连接
  servlet:
    multipart:
      enabled: true
      max-file-size: 10GB       #(这里是限制的文件大小)
      max-request-size: 10GB    #(这里是限制的文件大小)
  mvc:
    hiddenmethod:
      filter:
        enabled: true
  jackson:
    default-property-inclusion: non_null
logging:
  level:
    com:
      zenith:
        front:
          mapper: debug   #debug只输出sql语句 trace 打印sql语句也打印sql执行结果集
sync:
  ips: *************:8080,*************:8080,localhost:8080

#zip解密密码
unzip:
  password: 4676d068-2048-4109-b8eb-98208e7e9652
intranet: true

# 新转入组织关系转接拉取时间周期（介绍信）
pullLetterInTask:
  cron: 0 */3 * * * ?
# 本地转出办理过程拉取时间周期
pullProcessOutTask:
  cron: 0 */10 * * * ?
# 关系转接党组织增量拉取时间周期
pullOrgAddTask:
  cron: 0 0 0/7 * * ?
# 关系转接定时修复时间周期（已经废弃）
repairTransfer:
  cron: 0 */50 * * * ?
# 关系转接党组织全量拉取时间周期
pullOrgTask:
  cron: 0 18 12 * * ?
# 关系转接本地党组织修正上传时间周期
pushOrgAmendTask:
  cron: 0 18 18 * * ?
# 关系转接批量核对修复时间周期
repairTransFerData:
  cron: 0 0 1 1/15 * ?
# 关系转接异常核对修复时间周期
repairTransFerSwapAreaTask:
  cron: 0 0 1 1/15 * ?
# 关系转接上传失败数据重新上传时间周期
repairTransferUploadData:
  cron: 0 0 0/5 * * ?
# 转入本地组织关系核对时间周期（办理过程）
pullProcessInTask:
  cron: 0 0 0/3 * * ?
# 流入我省流动党员拉取定时任务周期
pullFlowInTask:
  cron: 0 0/30 * * * ?
# 我省流出党员拉取办理过程定时任务周期
pullFlowOutTask:
  cron: 0 0/30 * * * ?

feign:
  okhttp:
    enabled: true # 是否开启。默认为 false
  httpclient:
    # 开启 httpClient
    enabled: false

  # 请求登录参数是否加密处理
  paraEncrypt: true


# 内网信息
internal-service:
  # 运维平台服务器,数据包存放服务器
  zipSSH:
    sshHost: **************  # ssh地址
    sshPort: 22 # ssh端口
    sshUser:
    sshPass: ""

  # nginx server
  nginxSSH:
    sshHost: **************  # ssh地址
    sshPort: 22 # ssh端口
    sshUser:
    sshPass: ""
    filePath: "/home/<USER>/" # 前端包路径
    # 运维服务平台的前端包名称
    allUserDistName: "dj"
    # 党建平台的前端包名称
    djDistName: "djyw"
    cmdS:
      - "su root"
      - "123456"
      - "mv ${internal-service.nginxSSH.filePath}/{dist} ${internal-service.nginxSSH.filePath}/{dist}.`date +%y%m%d%H%M%s%S`"   # 重命名原来的前端包
      - "unzip -d ${internal-service.nginxSSH.filePath}/ {filePath}"     # 解压前端包更新到指定目录,程序替换包名
      - "mv ${internal-service.nginxSSH.filePath}/dist ${internal-service.nginxSSH.filePath}/{dist}"
      - "rm -rf {filePath}"     # 删除上传文件
      - "systemctl restart nginx"

  #all user server,一般和zipSSH是在一台服务器上，可以直接使用，注意工程路径和jar包名称是否一样
  allSSH:
    sshHost: ${internal-service.zipSSH.sshHost}  # ssh地址
    sshPort: ${internal-service.zipSSH.sshPort} # ssh端口
    sshUser: ${internal-service.zipSSH.sshUser}
    sshPass: ${internal-service.zipSSH.sshPass}
    filePath: "/home/<USER>/web_software/zenith-all-user/lib"
    fileName: "zenith-all-user-0.0.1-SNAPSHOT.jar"
    cmdS:
      # 重命名dj服务包
      - "mv ${internal-service.allSSH.filePath}/${internal-service.allSSH.fileName} ${internal-service.allSSH.filePath}/${internal-service.allSSH.fileName}.`date +%y%m%d%H%M%s%S`"
      # 移动更新包到指定dj服务目录
      - "cp {filePath} ${internal-service.allSSH.filePath}/${internal-service.allSSH.fileName}"
      # 重启服务
      - "systemctl restart all-user"

  #党建 server，如果党建服务和运维服务不在同一台服务器上，此处ssh配置需要单独配置，不要引用zipSSH的配置
  djSSH:
    sshHost: ${internal-service.zipSSH.sshHost}
    sshPort: ${internal-service.zipSSH.sshPort}
    sshUser: ${internal-service.zipSSH.sshUser}
    sshPass: ${internal-service.zipSSH.sshPass}
    filePath: "/home/<USER>/web_software/zenith-front_dj/lib"
    fileName: "zenith-front-dj-1.1.10.301.jar"
    cmdS:
      # 重命名dj服务包
      - "mv ${internal-service.djSSH.filePath}/${internal-service.djSSH.fileName} ${internal-service.djSSH.filePath}/${internal-service.djSSH.fileName}.`date +%y%m%d%H%M%s%S`"
      # 移动更新包到指定dj服务目录
      - "cp {filePath} ${internal-service.djSSH.filePath}/${internal-service.djSSH.fileName}"
      # 重启服务
      - "systemctl restart dj"
  #清除Es 索引脚本的服务器配置r，如果和运维服务不在同一台服务器上，此处ssh配置需要单独配置，不要引用zipSSH的配置
  esSSH:
    sshHost: ${internal-service.zipSSH.sshHost}
    sshPort: ${internal-service.zipSSH.sshPort}
    sshUser: ${internal-service.zipSSH.sshUser}
    sshPass: ${internal-service.zipSSH.sshPass}
    cmdS:
      - "sh /home/<USER>/es_clean.sh"

  # postgresql server，单独配置数据库服务器
  pgSSH:
    sshHost: ************* # ssh地址
    sshPort: 22 # ssh端口
    sshUser:
    sshPass: ""
    cmdS:
      - "su root"
      - "123456"
      - "su postgres -c '/usr/lib/postgresql/12/bin/pg_dump -U postgres {srcDb} > {destPath}'"
      - "su postgres -c '/usr/lib/postgresql/12/bin/pg_dump -U postgres {srcDb} > {destPath}'"
      - "su postgres -c '/usr/lib/postgresql/12/bin/psql -f {srcPath} {destDb}'"
      - "exit"
