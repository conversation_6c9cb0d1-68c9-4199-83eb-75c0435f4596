spring:
  datasource:
    driver-class-name: org.postgresql.Driver
    url: ************************************************************************************************************************
    username: postgres
    password: 20191809
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      maxActive: 40
      initialSize: 1
      maxWait: 30000
      minIdle: 1
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      validationQuery: select 'x'
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxOpenPreparedStatements: 20
  redis:
      redisson:
        file: classpath:redisson.yml
      timeout: 2000 # 连接或读取超时时长（毫秒）
      database: 2
      jedis:
        pool:
          max-active: 8 # 连接池最大连接数（使用负值表示没有限制）
          max-wait: 800 # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-idle: 8 # 连接池中的最大空闲连接
          min-idle: 2 # 连接池中的最小空闲连接
  servlet:
    multipart:
      enabled: true
      max-file-size: 20MB       #(这里是限制的文件大小)
      max-request-size: 20MB    #(这里是限制的文件大小)
  jackson:
    default-property-inclusion: non_null
sync:
  ips: *************:8080,*************:8080,localhost:8080

letter:
  cron: 0 */3 * * * ?
process:
  cron: 0 */10 * * * ?
addOrg:
  cron: 0 0 0/1 * * ?
repairTransfer:
  cron: 0 */50 * * * ?
pullOrg:
  cron: 0 18 12 * * ?
orgAmend:
  cron: 0 08 23 * * ?
repairTransFer:
  cron: 0 30 14 * * ?
repairTransferUploadData:
  cron: 0 15 22 * * ?
PullProcessInTask:
  cron: 0 59 01 * * ?
#  开启某些任务的定时开关
switch:
  # 开启定时拉取新组织关系转入（PullLetterInTask、PullProcessInTask、PullProcessOutTask、）、流动党员（PullFlowInTask、PullFlowOutTask、）、
  one: true
  # 定时开启党组织全量拉取（PullOrgAddTask、PullOrgTask）、中组部心跳机制（PushHeartTask）、中组部预备党员定时修复（PushOrgAmendTask）
  two: true
  # 定时开启中组部关系转接月度上报（PushTransferMonthStaticsTask）、流动党员月度上报（PushFlowMonthStaticsTask）、
  # 及关系转接和流动党员修复上传（RepairTransFerSwapAreaTask、RepairTransferUploadData）
  three: true