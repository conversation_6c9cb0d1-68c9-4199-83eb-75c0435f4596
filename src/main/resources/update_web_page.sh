#!/bin/bash
echo "开始更新web界面"
# 当前时间字符串 例 202202020202
cur_date=$(date +"%Y%m%d%H%M%S")
cd /tmp/ || exit
# 存在dist目录时
if [ -d "/tmp/dist/" ]; then
  rm -rf /tmp/dist/
fi
# 更新基础工程dj时
if [ -f "/tmp/dj.zip" ]; then
  # 解压
  unzip dj.zip -d /tmp/
  if [ -d "/tmp/dist/" ]; then
    # 重命名dj文件
    if [ -d "/home/<USER>/dj/" ]; then
      mv /home/<USER>/dj /home/<USER>/dj"${cur_date}"
    fi
    # 移动文件
    mv dist /home/<USER>/dj
    if [ -d "/home/<USER>/dj/" ]; then
      if [ -d "/home/<USER>/djtest" ]; then
        mv /home/<USER>/djtest /home/<USER>/djtest"${cur_date}"
        # 复制dj为djtest，更新测试环境
        cp -r /home/<USER>/dj /home/<USER>/djtest
      fi
    fi
  fi
  rm -rf /tmp/dj.zip
fi
# 更新运维工程djyw时
if [ -f "/tmp/djyw.zip" ]; then
  unzip djyw.zip -d /tmp/
  if [ -d "/tmp/dist/" ]; then
    if [ -d "/home/<USER>/djyw/" ]; then
      mv /home/<USER>/djyw /home/<USER>/djyw"${cur_date}"
    fi
    mv dist /home/<USER>/djyw
  fi
  rm -rf /tmp/djyw.zip
fi
# 更新在线帮助手册工程dj-doc时
if [ -f "/tmp/dj-doc.zip" ]; then
  unzip dj-doc.zip -d /tmp/
  if [ -d "/tmp/dist/" ]; then
    if [ -d "/home/<USER>/dj-doc/" ]; then
      mv /home/<USER>/dj-doc /home/<USER>/dj-doc"${cur_date}"
    fi
    if [ -d "/tmp/dist/" ]; then
      mv dist /home/<USER>/dj-doc
    fi
  fi
  rm -rf /tmp/dj-doc.zip
fi
