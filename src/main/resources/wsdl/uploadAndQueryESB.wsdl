<!--  Published by JAX-WS RI at http://jax-ws.dev.java.net. RI's version is JAX-WS RI 2.2.3-b01-.  -->
<!--  Generated by JAX-WS RI at http://jax-ws.dev.java.net. RI's version is JAX-WS RI 2.2.3-b01-.  -->
<definitions xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:wsp="http://www.w3.org/ns/ws-policy" xmlns:wsp1_2="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://impl.ws.application.dataswap.css.com/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.xmlsoap.org/wsdl/" targetNamespace="http://impl.ws.application.dataswap.css.com/" name="ESBUploadAnQuerybusinessServiceImplService">
<types/>
<message name="query">
<part name="arg0" type="xsd:string"/>
</message>
<message name="queryResponse">
<part name="return" type="xsd:string"/>
</message>
<message name="upload">
<part name="arg0" type="xsd:string"/>
</message>
<message name="uploadResponse">
<part name="return" type="xsd:string"/>
</message>
<portType name="ESBUploadAnQuerybusinessServiceImpl">
<operation name="query">
<input wsam:Action="http://impl.ws.application.dataswap.css.com/ESBUploadAnQuerybusinessServiceImpl/queryRequest" message="tns:query"/>
<output wsam:Action="http://impl.ws.application.dataswap.css.com/ESBUploadAnQuerybusinessServiceImpl/queryResponse" message="tns:queryResponse"/>
</operation>
<operation name="upload">
<input wsam:Action="http://impl.ws.application.dataswap.css.com/ESBUploadAnQuerybusinessServiceImpl/uploadRequest" message="tns:upload"/>
<output wsam:Action="http://impl.ws.application.dataswap.css.com/ESBUploadAnQuerybusinessServiceImpl/uploadResponse" message="tns:uploadResponse"/>
</operation>
</portType>
<binding name="ESBUploadAnQuerybusinessServiceImplPortBinding" type="tns:ESBUploadAnQuerybusinessServiceImpl">
<soap:binding transport="http://schemas.xmlsoap.org/soap/http" style="rpc"/>
<operation name="query">
<soap:operation soapAction=""/>
<input>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"/>
</input>
<output>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"/>
</output>
</operation>
<operation name="upload">
<soap:operation soapAction=""/>
<input>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"/>
</input>
<output>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"/>
</output>
</operation>
</binding>
<service name="ESBUploadAnQuerybusinessServiceImplService">
<port name="ESBUploadAnQuerybusinessServiceImplPort" binding="tns:ESBUploadAnQuerybusinessServiceImplPortBinding">
<!--<soap:address location="http://*************:8899/dataswap/services/uploadAndQueryESB"/>-->
<!--    上面是测试环境，下面是生产环境-->
<soap:address location="http://*************:8898/dataswap/services/uploadAndQueryESB"/>
</port>
</service>
</definitions>