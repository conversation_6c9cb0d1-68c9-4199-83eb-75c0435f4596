<?xml version="1.0" encoding="UTF-8"?><!-- Published by JAX-WS RI at http://jax-ws.dev.java.net. RI's version is JAX-WS RI 2.2.3-b01-. --><!-- Generated by JAX-WS RI at http://jax-ws.dev.java.net. RI's version is JAX-WS RI 2.2.3-b01-. --><definitions xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:wsp="http://www.w3.org/ns/ws-policy" xmlns:wsp1_2="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://impl.ws.application.dataswap.css.com/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.xmlsoap.org/wsdl/" targetNamespace="http://impl.ws.application.dataswap.css.com/" name="DzzUploadAndDownbusinessServiceImplService">
<types></types>
<message name="upload">
<part name="arg0" type="xsd:string"></part>
</message>
<message name="uploadResponse">
<part name="return" type="xsd:string"></part>
</message>
<message name="uploadEncrypted">
<part name="arg0" type="xsd:string"></part>
</message>
<message name="uploadEncryptedResponse">
<part name="return" type="xsd:string"></part>
</message>
<message name="download">
<part name="arg0" type="xsd:string"></part>
</message>
<message name="downloadResponse">
<part name="return" type="xsd:string"></part>
</message>
<message name="downloadEncrypted">
<part name="arg0" type="xsd:string"></part>
</message>
<message name="downloadEncryptedResponse">
<part name="return" type="xsd:string"></part>
</message>
<portType name="DzzUploadAndDownbusinessServiceImpl">
<operation name="upload">
<input wsam:Action="http://impl.ws.application.dataswap.css.com/DzzUploadAndDownbusinessServiceImpl/uploadRequest" message="tns:upload"></input>
<output wsam:Action="http://impl.ws.application.dataswap.css.com/DzzUploadAndDownbusinessServiceImpl/uploadResponse" message="tns:uploadResponse"></output>
</operation>
<operation name="uploadEncrypted">
<input wsam:Action="http://impl.ws.application.dataswap.css.com/DzzUploadAndDownbusinessServiceImpl/uploadEncryptedRequest" message="tns:uploadEncrypted"></input>
<output wsam:Action="http://impl.ws.application.dataswap.css.com/DzzUploadAndDownbusinessServiceImpl/uploadEncryptedResponse" message="tns:uploadEncryptedResponse"></output>
</operation>
<operation name="download">
<input wsam:Action="http://impl.ws.application.dataswap.css.com/DzzUploadAndDownbusinessServiceImpl/downloadRequest" message="tns:download"></input>
<output wsam:Action="http://impl.ws.application.dataswap.css.com/DzzUploadAndDownbusinessServiceImpl/downloadResponse" message="tns:downloadResponse"></output>
</operation>
<operation name="downloadEncrypted">
<input wsam:Action="http://impl.ws.application.dataswap.css.com/DzzUploadAndDownbusinessServiceImpl/downloadEncryptedRequest" message="tns:downloadEncrypted"></input>
<output wsam:Action="http://impl.ws.application.dataswap.css.com/DzzUploadAndDownbusinessServiceImpl/downloadEncryptedResponse" message="tns:downloadEncryptedResponse"></output>
</operation>
</portType>
<binding name="DzzUploadAndDownbusinessServiceImplPortBinding" type="tns:DzzUploadAndDownbusinessServiceImpl">
<soap:binding transport="http://schemas.xmlsoap.org/soap/http" style="rpc"></soap:binding>
<operation name="upload">
<soap:operation soapAction=""></soap:operation>
<input>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"></soap:body>
</input>
<output>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"></soap:body>
</output>
</operation>
<operation name="uploadEncrypted">
<soap:operation soapAction=""></soap:operation>
<input>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"></soap:body>
</input>
<output>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"></soap:body>
</output>
</operation>
<operation name="download">
<soap:operation soapAction=""></soap:operation>
<input>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"></soap:body>
</input>
<output>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"></soap:body>
</output>
</operation>
<operation name="downloadEncrypted">
<soap:operation soapAction=""></soap:operation>
<input>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"></soap:body>
</input>
<output>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"></soap:body>
</output>
</operation>
</binding>
<service name="DzzUploadAndDownbusinessServiceImplService">
<port name="DzzUploadAndDownbusinessServiceImplPort" binding="tns:DzzUploadAndDownbusinessServiceImplPortBinding">
<!--<soap:address location="http://*************:8899/dataswap/services/dzzUploadAndDown"></soap:address>-->
<!--    上面是测试环境，下面是生产环境-->
<soap:address location="http://*************:8898/dataswap/services/dzzUploadAndDown"></soap:address>
</port>
</service>
</definitions>