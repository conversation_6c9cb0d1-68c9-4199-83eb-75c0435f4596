<!--  Published by JAX-WS RI at http://jax-ws.dev.java.net. RI's version is JAX-WS RI 2.2.3-b01-.  -->
<!--  Generated by JAX-WS RI at http://jax-ws.dev.java.net. RI's version is JAX-WS RI 2.2.3-b01-.  -->
<definitions xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:wsp="http://www.w3.org/ns/ws-policy" xmlns:wsp1_2="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://impl.ws.application.dataswap.css.com/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.xmlsoap.org/wsdl/" targetNamespace="http://impl.ws.application.dataswap.css.com/" name="HeartBeatServiceImplService">
<types/>
<message name="getTime">
<part name="arg0" type="xsd:int"/>
</message>
<message name="getTimeResponse">
<part name="return" type="xsd:dateTime"/>
</message>
<message name="verifySign">
<part name="arg0" type="xsd:string"/>
</message>
<message name="verifySignResponse">
<part name="return" type="xsd:string"/>
</message>
<message name="handleSQL">
<part name="arg0" type="xsd:string"/>
</message>
<message name="handleSQLResponse">
<part name="return" type="xsd:string"/>
</message>
<message name="checkStatus">
<part name="arg0" type="xsd:string"/>
</message>
<message name="checkStatusResponse">
<part name="return" type="xsd:string"/>
</message>
<message name="isAlive"/>
<message name="isAliveResponse">
<part name="return" type="xsd:string"/>
</message>
<portType name="HeartBeatServiceImpl">
<operation name="getTime">
<input wsam:Action="http://impl.ws.application.dataswap.css.com/HeartBeatServiceImpl/getTimeRequest" message="tns:getTime"/>
<output wsam:Action="http://impl.ws.application.dataswap.css.com/HeartBeatServiceImpl/getTimeResponse" message="tns:getTimeResponse"/>
</operation>
<operation name="verifySign">
<input wsam:Action="http://impl.ws.application.dataswap.css.com/HeartBeatServiceImpl/verifySignRequest" message="tns:verifySign"/>
<output wsam:Action="http://impl.ws.application.dataswap.css.com/HeartBeatServiceImpl/verifySignResponse" message="tns:verifySignResponse"/>
</operation>
<operation name="handleSQL">
<input wsam:Action="http://impl.ws.application.dataswap.css.com/HeartBeatServiceImpl/handleSQLRequest" message="tns:handleSQL"/>
<output wsam:Action="http://impl.ws.application.dataswap.css.com/HeartBeatServiceImpl/handleSQLResponse" message="tns:handleSQLResponse"/>
</operation>
<operation name="checkStatus">
<input wsam:Action="http://impl.ws.application.dataswap.css.com/HeartBeatServiceImpl/checkStatusRequest" message="tns:checkStatus"/>
<output wsam:Action="http://impl.ws.application.dataswap.css.com/HeartBeatServiceImpl/checkStatusResponse" message="tns:checkStatusResponse"/>
</operation>
<operation name="isAlive">
<input wsam:Action="http://impl.ws.application.dataswap.css.com/HeartBeatServiceImpl/isAliveRequest" message="tns:isAlive"/>
<output wsam:Action="http://impl.ws.application.dataswap.css.com/HeartBeatServiceImpl/isAliveResponse" message="tns:isAliveResponse"/>
</operation>
</portType>
<binding name="HeartBeatServiceImplPortBinding" type="tns:HeartBeatServiceImpl">
<soap:binding transport="http://schemas.xmlsoap.org/soap/http" style="rpc"/>
<operation name="getTime">
<soap:operation soapAction=""/>
<input>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"/>
</input>
<output>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"/>
</output>
</operation>
<operation name="verifySign">
<soap:operation soapAction=""/>
<input>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"/>
</input>
<output>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"/>
</output>
</operation>
<operation name="handleSQL">
<soap:operation soapAction=""/>
<input>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"/>
</input>
<output>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"/>
</output>
</operation>
<operation name="checkStatus">
<soap:operation soapAction=""/>
<input>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"/>
</input>
<output>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"/>
</output>
</operation>
<operation name="isAlive">
<soap:operation soapAction=""/>
<input>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"/>
</input>
<output>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"/>
</output>
</operation>
</binding>
<service name="HeartBeatServiceImplService">
<port name="HeartBeatServiceImplPort" binding="tns:HeartBeatServiceImplPortBinding">
<!--<soap:address location="http://*************:8899/dataswap/services/heartBeat"/>-->
<!--    上面是测试环境，下面是生产环境-->
<soap:address location="http://*************:8898/dataswap/services/heartBeat"/>
</port>
</service>
</definitions>