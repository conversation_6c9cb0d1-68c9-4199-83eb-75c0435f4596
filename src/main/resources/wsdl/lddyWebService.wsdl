<!--  Published by JAX-WS RI at http://jax-ws.dev.java.net. RI's version is JAX-WS RI 2.2.3-b01-.  -->
<!--  Generated by JAX-WS RI at http://jax-ws.dev.java.net. RI's version is JAX-WS RI 2.2.3-b01-.  -->
<definitions xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:wsp="http://www.w3.org/ns/ws-policy" xmlns:wsp1_2="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://impl.ws.application.dataswap.css.com/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.xmlsoap.org/wsdl/" targetNamespace="http://impl.ws.application.dataswap.css.com/" name="LddyWebServiceImplService">
<types/>
<message name="confirm">
<part name="arg0" type="xsd:string"/>
</message>
<message name="confirmResponse">
<part name="return" type="xsd:string"/>
</message>
<message name="queryLddyByUniquekey">
<part name="arg0" type="xsd:string"/>
</message>
<message name="queryLddyByUniquekeyResponse">
<part name="return" type="xsd:string"/>
</message>
<message name="download">
<part name="arg0" type="xsd:string"/>
</message>
<message name="downloadResponse">
<part name="return" type="xsd:string"/>
</message>
<message name="errorMsg">
<part name="arg0" type="xsd:string"/>
<part name="arg1" type="xsd:string"/>
</message>
<message name="errorMsgResponse">
<part name="return" type="xsd:string"/>
</message>
<message name="upload">
<part name="arg0" type="xsd:string"/>
</message>
<message name="uploadResponse">
<part name="return" type="xsd:string"/>
</message>
<portType name="LddyWebServiceImpl">
<operation name="confirm">
<input wsam:Action="http://impl.ws.application.dataswap.css.com/LddyWebServiceImpl/confirmRequest" message="tns:confirm"/>
<output wsam:Action="http://impl.ws.application.dataswap.css.com/LddyWebServiceImpl/confirmResponse" message="tns:confirmResponse"/>
</operation>
<operation name="queryLddyByUniquekey">
<input wsam:Action="http://impl.ws.application.dataswap.css.com/LddyWebServiceImpl/queryLddyByUniquekeyRequest" message="tns:queryLddyByUniquekey"/>
<output wsam:Action="http://impl.ws.application.dataswap.css.com/LddyWebServiceImpl/queryLddyByUniquekeyResponse" message="tns:queryLddyByUniquekeyResponse"/>
</operation>
<operation name="download">
<input wsam:Action="http://impl.ws.application.dataswap.css.com/LddyWebServiceImpl/downloadRequest" message="tns:download"/>
<output wsam:Action="http://impl.ws.application.dataswap.css.com/LddyWebServiceImpl/downloadResponse" message="tns:downloadResponse"/>
</operation>
<operation name="errorMsg" parameterOrder="arg0 arg1">
<input wsam:Action="http://impl.ws.application.dataswap.css.com/LddyWebServiceImpl/errorMsgRequest" message="tns:errorMsg"/>
<output wsam:Action="http://impl.ws.application.dataswap.css.com/LddyWebServiceImpl/errorMsgResponse" message="tns:errorMsgResponse"/>
</operation>
<operation name="upload">
<input wsam:Action="http://impl.ws.application.dataswap.css.com/LddyWebServiceImpl/uploadRequest" message="tns:upload"/>
<output wsam:Action="http://impl.ws.application.dataswap.css.com/LddyWebServiceImpl/uploadResponse" message="tns:uploadResponse"/>
</operation>
</portType>
<binding name="LddyWebServiceImplPortBinding" type="tns:LddyWebServiceImpl">
<soap:binding transport="http://schemas.xmlsoap.org/soap/http" style="rpc"/>
<operation name="confirm">
<soap:operation soapAction=""/>
<input>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"/>
</input>
<output>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"/>
</output>
</operation>
<operation name="queryLddyByUniquekey">
<soap:operation soapAction=""/>
<input>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"/>
</input>
<output>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"/>
</output>
</operation>
<operation name="download">
<soap:operation soapAction=""/>
<input>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"/>
</input>
<output>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"/>
</output>
</operation>
<operation name="errorMsg">
<soap:operation soapAction=""/>
<input>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"/>
</input>
<output>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"/>
</output>
</operation>
<operation name="upload">
<soap:operation soapAction=""/>
<input>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"/>
</input>
<output>
<soap:body use="literal" namespace="http://impl.ws.application.dataswap.css.com/"/>
</output>
</operation>
</binding>
<service name="LddyWebServiceImplService">
<port name="LddyWebServiceImplPort" binding="tns:LddyWebServiceImplPortBinding">
<!--<soap:address location="http://*************:8899/dataswap/services/lddyWebService"/>-->
<!--    上面是测试环境，下面是生产环境-->
<soap:address location="http://*************:8898/dataswap/services/lddyWebService"/>
</port>
</service>
</definitions>