<assembly xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.3"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.3 http://maven.apache.org/xsd/assembly-1.1.3.xsd">
    <id>package</id>
    <formats>
        <format>zip</format>
    </formats>
    <fileSets>
        <fileSet>
            <directory>target/classes</directory>
            <outputDirectory>config</outputDirectory>
            <includes>
                <include>application.yml</include>
                <include>*.json</include>
                <include>logback-spring.xml</include>
                <include>redisson.yml</include>
                <include>templates/**</include>
                <include>public/**</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>target/</directory>
            <outputDirectory>lib</outputDirectory>
            <includes>
                <include>${project.build.finalName}.jar</include>
            </includes>
        </fileSet>
        <!-- 自定义 shell 脚本存放路径 -->
        <fileSet>
            <directory>shell/</directory>
            <outputDirectory>${file.separator}</outputDirectory>
            <includes>
                <include>*.sh</include>
            </includes>
        </fileSet>
    </fileSets>
</assembly>