package com.webservice.dzz;

import javax.xml.namespace.QName;
import javax.xml.ws.*;
import java.net.URL;

/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 */
@WebServiceClient(name = "DzzUploadAndDownbusinessServiceImplService", targetNamespace = "http://impl.ws.application.dataswap.css.com/",
        wsdlLocation = "C:\\Users\\<USER>\\Desktop\\wsdl\\dzzUploadAndDown.wsdl")
public class DzzUploadAndDownbusinessServiceImplService
        extends Service {

    private final static URL DZZUPLOADANDDOWNBUSINESSSERVICEIMPLSERVICE_WSDL_LOCATION;
    private final static WebServiceException DZZUPLOADANDDOWNBUSINESSSERVICEIMPLSERVICE_EXCEPTION;
    private final static QName DZZUPLOADANDDOWNBUSINESSSERVICEIMPLSERVICE_QNAME = new QName("http://impl.ws.application.dataswap.css.com/", "DzzUploadAndDownbusinessServiceImplService");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = DzzUploadAndDownbusinessServiceImplService.class.getResource("/wsdl/dzzUploadAndDown.wsdl");
        } catch (Exception ex) {
            e = new WebServiceException(ex);
        }
        DZZUPLOADANDDOWNBUSINESSSERVICEIMPLSERVICE_WSDL_LOCATION = url;
        DZZUPLOADANDDOWNBUSINESSSERVICEIMPLSERVICE_EXCEPTION = e;
    }

    public DzzUploadAndDownbusinessServiceImplService() {
        super(__getWsdlLocation(), DZZUPLOADANDDOWNBUSINESSSERVICEIMPLSERVICE_QNAME);
    }

    private static URL __getWsdlLocation() {
        if (DZZUPLOADANDDOWNBUSINESSSERVICEIMPLSERVICE_EXCEPTION != null) {
            throw DZZUPLOADANDDOWNBUSINESSSERVICEIMPLSERVICE_EXCEPTION;
        }
        return DZZUPLOADANDDOWNBUSINESSSERVICEIMPLSERVICE_WSDL_LOCATION;
    }

    public DzzUploadAndDownbusinessServiceImplService(WebServiceFeature... features) {
        super(__getWsdlLocation(), DZZUPLOADANDDOWNBUSINESSSERVICEIMPLSERVICE_QNAME, features);
    }

    public DzzUploadAndDownbusinessServiceImplService(URL wsdlLocation) {
        super(wsdlLocation, DZZUPLOADANDDOWNBUSINESSSERVICEIMPLSERVICE_QNAME);
    }

    public DzzUploadAndDownbusinessServiceImplService(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, DZZUPLOADANDDOWNBUSINESSSERVICEIMPLSERVICE_QNAME, features);
    }

    public DzzUploadAndDownbusinessServiceImplService(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public DzzUploadAndDownbusinessServiceImplService(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    /**
     * @return returns DzzUploadAndDownbusinessServiceImpl
     */
    @WebEndpoint(name = "DzzUploadAndDownbusinessServiceImplPort")
    public DzzUploadAndDownbusinessServiceImpl getDzzUploadAndDownbusinessServiceImplPort() {
        return super.getPort(new QName("http://impl.ws.application.dataswap.css.com/", "DzzUploadAndDownbusinessServiceImplPort"), DzzUploadAndDownbusinessServiceImpl.class);
    }

    /**
     * @param features A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return returns DzzUploadAndDownbusinessServiceImpl
     */
    @WebEndpoint(name = "DzzUploadAndDownbusinessServiceImplPort")
    public DzzUploadAndDownbusinessServiceImpl getDzzUploadAndDownbusinessServiceImplPort(WebServiceFeature... features) {
        return super.getPort(new QName("http://impl.ws.application.dataswap.css.com/", "DzzUploadAndDownbusinessServiceImplPort"), DzzUploadAndDownbusinessServiceImpl.class, features);
    }

}
