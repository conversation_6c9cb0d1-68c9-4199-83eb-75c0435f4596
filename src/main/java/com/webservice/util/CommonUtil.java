package com.webservice.util;

/**
 * <AUTHOR>
 * @Date 2021/12/14 17:11
 * @Version 1.0
 */
public class CommonUtil {

    /**
     * 通过身份证号码获取出生日期
     *
     * @param certificateNo
     * @return 返回的出生日期格式：1990-01-01
     */
    public static String getBir(String certificateNo) {
        String birthday = "";
        char[] number = certificateNo.toCharArray();
        boolean flag = true;
        if (number.length == 15) {
            for (char c : number) {
                if (!flag) {
                    return "";
                }
                flag = Character.isDigit(c);
            }
        } else if (number.length == 18) {
            for (int x = 0; x < number.length - 1; x++) {
                if (!flag) {
                    return "";
                }
                flag = Character.isDigit(number[x]);
            }
        }
        if (flag && certificateNo.length() == 15) {
            birthday = "19" + certificateNo.substring(6, 8) + "-"
                    + certificateNo.substring(8, 10) + "-"
                    + certificateNo.substring(10, 12);
        } else if (flag && certificateNo.length() == 18) {
            birthday = certificateNo.substring(6, 10) + "-"
                    + certificateNo.substring(10, 12) + "-"
                    + certificateNo.substring(12, 14);
        }
        return birthday;
    }
}
