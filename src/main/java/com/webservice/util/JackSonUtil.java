package com.webservice.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2021/11/24 19:52
 */
public class JackSonUtil {
    public static final Logger LOGGER = LoggerFactory.getLogger(JackSonUtil.class);
    public static final ObjectMapper JSON = new ObjectMapper();

    static {
        JSON.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        JSON.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public static String toJson(Object obj) {
        if (Objects.isNull(obj)) {
            return null;
        }
        try {
            return JSON.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            LOGGER.error("toJson 转换异常", e);
            return "";
        }
    }

    public static <T> T toObject(String data, Class<T> cla) {
        if (Objects.isNull(data)) {
            return null;
        }
        try {
            return JSON.readValue(data, cla);
        } catch (JsonProcessingException e) {
            LOGGER.error("toJson 转换异常", e);
            return null;
        }
    }

    /**
     * json字符串转成list
     *
     * @param jsonString
     * @param cls
     * @return
     */
    public static <T> List<T> jsonToList(String jsonString, Class<T> cls) {
        try {
            JSON.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
            return JSON.readValue(jsonString, getCollectionType(cls));
        } catch (JsonProcessingException e) {
            String className = cls.getSimpleName();
            LOGGER.error(" parse json [{}] to class [{}] error：{}", jsonString, className, e);
        }
        return null;
    }

    /**
     * 获取泛型的Collection Type
     *
     * @param elementClasses 实体bean
     * @return JavaType Java类型
     */
    private static JavaType getCollectionType(Class<?>... elementClasses) {
        return JSON.getTypeFactory().constructParametricType(List.class, elementClasses);
    }
}
