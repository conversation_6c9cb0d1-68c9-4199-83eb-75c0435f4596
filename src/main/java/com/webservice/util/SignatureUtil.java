package com.webservice.util;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * 签名工具类
 *
 * <AUTHOR>
 * @since 2021/11/24 19:17
 */
public class SignatureUtil {
    /**
     * 默认加密方式
     */
    public static final SignatureTypeEnum DEFAULT_TYPE = SignatureTypeEnum.WST;
    public static String DEFAULT_CHARSET = StandardCharsets.UTF_8.toString();

    /**
     * 加密
     *
     * @return 加密后字符串
     */
    public static String encrypt(String data) throws Exception {
        if (Objects.equals(DEFAULT_TYPE, SignatureTypeEnum.WST)) {
            return encryptWst(data);
        } else {
            return encryptGe(data);
        }
    }

    /**
     * 卫士通加密
     *
     * @return 加密后字符串
     */
    private static String encryptWst(String data) {
        return data;
    }

    private static String wstVerifyStr(String data) {
        return data;
    }

    /**
     * 格尔加密
     *
     * @return 加密后字符串
     */
    private static String encryptGe(String data) {
        return null;
    }

    /**
     * 解密
     *
     * @return 解密后字符串
     */
    public static String decrypt(String data) {
        return null;
    }
}
