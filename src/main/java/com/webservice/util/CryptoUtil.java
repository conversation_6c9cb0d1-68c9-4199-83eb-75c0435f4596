package com.webservice.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.koalii.hsmapi.trade.service.HsmApiService;
import com.koalii.hsmapi.trade.service.HsmApiTradeException;
import com.koalii.svs.client.Svs2ClientCupHelper;
import com.koalii.svs.client.Svs2ClientHelper;
import com.koalii.svs.util.HexStringUtil;
import com.koalii.util.log.LogUtil;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

public class CryptoUtil {
    /**
     *jmj证书
     */
    public static final String CERT_ID = "7";
    /**
     * 签名
     * @param jsonData
     * @param keyIndexStr
     * @return
     * @throws Exception
     */
    public static Map<String, String> signData(String jsonData, String keyIndexStr) throws Exception {
        String base64Data = new String(com.koalii.kgsp.bc.util.encoders.Base64.encode(jsonData.getBytes()));
        base64Data = URLEncoder.encode(base64Data, "UTF-8");
        int keyIndex = Integer.parseInt(keyIndexStr);
        LogUtil.info("签名参数------>jsonData："+jsonData+"，base64Data：" + base64Data + "，keyIndex：" + keyIndex);
        HsmApiService hsmApiService = new HsmApiService();
        byte[] signData = null;
        try {
            hsmApiService.initServers("*************:5000", 300);
            signData = hsmApiService.signData(Svs2ClientHelper.DIGEST_ALGO_SHA1, keyIndex, "", base64Data.getBytes());
        } catch (HsmApiTradeException e){
            LogUtil.info("签名异常：" + e.getErrorCode());
            throw new Exception(e);
        }
        if (signData.length > 0) {
            String signData64 = new String(com.koalii.kgsp.bc.util.encoders.Base64.encode(signData));
            LogUtil.info("签名后转base64的值------>signData64：" + signData64);
            String signData64Encode = URLEncoder.encode(signData64, "UTF-8");
            LogUtil.info("签名后转base64后endCode的值------>signData64Encode：" + signData64Encode);
            Map<String, String> map = new HashMap<>();
            map.put("data", base64Data);
            map.put("signData", signData64Encode);
            return map;
        } else {
            return null;
        }
    }
    /**
     * 获取格尔客户端
     * @return
     */
    private static Svs2ClientHelper getClient() {
        Svs2ClientHelper svs2ClientCupHelper = Svs2ClientCupHelper.getInstance();
        svs2ClientCupHelper.initServers("*************:5000", 300);
        return svs2ClientCupHelper;
    }

    /**
     * 获取随机码以及随机码sm2密文
     *
     * @return
     * @throws Exception
     */
    public static String[] getSm2Keys(String certId) {
        String originKey = "";
        String encryptKey = "";
        try {
            Svs2ClientHelper svs2ClientCupHelper = getClient();
            Svs2ClientHelper.Cipher cipher = svs2ClientCupHelper.newCipher(Svs2ClientHelper.SM4_CBC);
            originKey = Base64.getEncoder().encodeToString(cipher.key);
            //数字信封 - SM2加密 随机秘钥
            encryptKey = sm2Encrypt(cipher.key, certId);
        } catch (Exception e) {
            LogUtil.error("getSm2Keys"+e);
            throw new RuntimeException("格尔获取随机码对失败！");
        }
        LogUtil.info("随机码：" + originKey);
        LogUtil.info("随机码SM2加密：" + encryptKey);
        return new String[] { originKey, encryptKey };
    }

    private static Svs2ClientHelper.Cipher getSM4Cipher(Svs2ClientHelper svs2ClientCupHelper, String secretKey) {
        Svs2ClientHelper.Cipher sm4Cipher = svs2ClientCupHelper.newCipher(Base64.getDecoder().decode(secretKey), Svs2ClientCupHelper.SM4_CBC, Svs2ClientCupHelper.PADDING_PKCS7);
        return sm4Cipher;
    }

    /**
     * SM4对称加密
     * @param originText
     * @param secretKey
     * @return
     */
    public static String sm4Encrypt(String originText, String secretKey) {
        if (StrUtil.isEmpty(originText)) {
            return originText;
        }
        Svs2ClientHelper svs2ClientCupHelper = getClient();
        Svs2ClientHelper.Cipher sm4Cipher = getSM4Cipher(svs2ClientCupHelper, secretKey);
        Svs2ClientHelper.SvsResultData svsResultData = svs2ClientCupHelper.cipherEncrypt(sm4Cipher, originText.getBytes(StandardCharsets.UTF_8));
        String cipherText = svsResultData.m_b64EncData;
        return cipherText;
    }

    /**
     * SM4对称解密
     * @param cipherText
     * @param secretKey
     * @return
     * @throws Exception
     */
    public static String sm4Decrypt(String cipherText, String secretKey) throws Exception {
        if (StrUtil.isEmpty(cipherText)) {
            return cipherText;
        }
        Svs2ClientHelper svs2ClientCupHelper = getClient();
        Svs2ClientHelper.Cipher sm4Cipher = getSM4Cipher(svs2ClientCupHelper, secretKey);
        Svs2ClientHelper.SvsResultData svsResultData = svs2ClientCupHelper.cipherDecrypt(sm4Cipher, cipherText, sm4Cipher.name);
        String originText = presentOriginData(svsResultData.m_originData);
        return originText;
    }

    /**
     * SM2加密
     *
     * @param originText
     * @param certId
     * @return
     */
    public static String sm2Encrypt(String originText, String certId) throws Exception {
        if (StrUtil.isEmpty(originText)) {
            return originText;
        }
        return sm2Encrypt(originText.getBytes(StandardCharsets.UTF_8), certId);
    }

    public static String sm2Encrypt(byte[] originByte, String certId) {
        if (originByte == null) {
            return null;
        }
        Svs2ClientHelper svs2ClientCupHelper = getClient();
        Svs2ClientHelper.SvsResultData svsResultData = svs2ClientCupHelper.publicKeyEncryptEx(originByte, certId);
        return svsResultData.m_b64EvpData;
    }

    /**
     * SM2解密
     *
     * @param cipherText
     * @param certId
     * @return
     */
    public static String sm2Decrypt(String cipherText, String certId) throws Exception {
        if (StrUtil.isEmpty(cipherText)) {
            return cipherText;
        }
        Svs2ClientHelper svs2ClientCupHelper = getClient();
        byte[] originData = svs2ClientCupHelper.privateKeyDecryptEx(cipherText, certId);
        return presentOriginData2(originData);
    }

    private static String presentOriginData(byte[] originData) {
        if (originData == null || originData.length == 0) {
            return null;
        }
        return new String(originData, StandardCharsets.UTF_8);
    }

    private static String presentOriginData2(byte[] originData) {
        if (originData == null || originData.length == 0) {
            return null;
        }
        return new String(Base64.getEncoder().encode(originData), StandardCharsets.UTF_8);
    }
    /**
     * 获取介绍信唯一码
     * @return
     */
    public static String getJSX(String value) {
        int n = 0;
        StringBuilder a = new StringBuilder();
        for (int i = 0; i <value.length(); i++) {
            a.append(value.charAt(i)+",");
        }
        String rs = a.substring(0,a.length()-1);
        String[] split = rs.split(",");
        for (String s1 : split) {
            n += Integer.parseInt(s1);
        }
        int i = n % 10;
        return value+i;
    }

    /**
     * 解析组织关系全量信息
     * @param info
     * @param secretKey
     * @return
     * @throws Exception
     */
    public static String getRealInfo(String info, String secretKey) throws Exception {
        byte[] secretKeyByte = HexStringUtil.toByteArray(secretKey);
        String randomEnStr = Base64.getEncoder().encodeToString(secretKeyByte);
        String randomStr= CryptoUtil.sm2Decrypt(randomEnStr, "1");
        LogUtil.info("随机码randomStr：" + randomStr);
        byte[] infoByte = HexStringUtil.toByteArray(info);
        String infoEnStr = Base64.getEncoder().encodeToString(infoByte);
        String infoStr = CryptoUtil.sm4Decrypt(infoEnStr, randomStr);
        return infoStr;
    }
    public static String getQianMingData(String data, String signData, String accessId){
        JSONObject jo = new JSONObject();
        jo.put("Data", data);
        jo.put("SignedData", signData);
        jo.put("yqsf", accessId);
        return jo.toString();
    }

    public static String cnToUnicode(String cn) {
        final char[] chars = cn.toCharArray();
        StringBuilder returnStr = new StringBuilder();
        for (char aChar : chars) {
            returnStr.append("\\u").append(Integer.toString(aChar, 16));
        }
        return returnStr.toString();
    }

}
