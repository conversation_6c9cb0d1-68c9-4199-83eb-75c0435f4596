package com.webservice.esb;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.ws.Action;

/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 */
@WebService(name = "ESBUploadAnQuerybusinessServiceImpl", targetNamespace = "http://impl.ws.application.dataswap.css.com/")
@SOAPBinding(style = SOAPBinding.Style.RPC)
public interface ESBUploadAnQuerybusinessServiceImpl {

    /**
     * @param arg0
     * @return returns java.lang.String
     */
    @WebMethod
    @WebResult(partName = "return")
    @Action(input = "http://impl.ws.application.dataswap.css.com/ESBUploadAnQuerybusinessServiceImpl/queryRequest", output = "http://impl.ws.application.dataswap.css.com/ESBUploadAnQuerybusinessServiceImpl/queryResponse")
    String query(
            @WebParam(name = "arg0", partName = "arg0")
                    String arg0);

    /**
     * @param arg0
     * @return returns java.lang.String
     */
    @WebMethod
    @WebResult(partName = "return")
    @Action(input = "http://impl.ws.application.dataswap.css.com/ESBUploadAnQuerybusinessServiceImpl/uploadRequest", output = "http://impl.ws.application.dataswap.css.com/ESBUploadAnQuerybusinessServiceImpl/uploadResponse")
    String upload(
            @WebParam(name = "arg0", partName = "arg0")
                    String arg0);

}
