package com.webservice.esb;

import javax.xml.namespace.QName;
import javax.xml.ws.*;
import java.net.URL;

/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 */
@WebServiceClient(name = "ESBUploadAnQuerybusinessServiceImplService", targetNamespace = "http://impl.ws.application.dataswap.css.com/",
        wsdlLocation = "file:/C:/Users/<USER>/Desktop/wsld/uploadAndQueryESB.wsdl")
public class ESBUploadAnQuerybusinessServiceImplService
        extends Service {

    private final static URL ESBUPLOADANQUERYBUSINESSSERVICEIMPLSERVICE_WSDL_LOCATION;
    private final static WebServiceException ESBUPLOADANQUERYBUSINESSSERVICEIMPLSERVICE_EXCEPTION;
    private final static QName ESBUPLOADANQUERYBUSINESSSERVICEIMPLSERVICE_QNAME = new QName("http://impl.ws.application.dataswap.css.com/", "ESBUploadAnQuerybusinessServiceImplService");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = ESBUploadAnQuerybusinessServiceImplService.class.getResource("/wsdl/uploadAndQueryESB.wsdl");
        } catch (Exception ex) {
            e = new WebServiceException(ex);
        }
        ESBUPLOADANQUERYBUSINESSSERVICEIMPLSERVICE_WSDL_LOCATION = url;
        ESBUPLOADANQUERYBUSINESSSERVICEIMPLSERVICE_EXCEPTION = e;
    }

    public ESBUploadAnQuerybusinessServiceImplService() {
        super(__getWsdlLocation(), ESBUPLOADANQUERYBUSINESSSERVICEIMPLSERVICE_QNAME);
    }

    private static URL __getWsdlLocation() {
        if (ESBUPLOADANQUERYBUSINESSSERVICEIMPLSERVICE_EXCEPTION != null) {
            throw ESBUPLOADANQUERYBUSINESSSERVICEIMPLSERVICE_EXCEPTION;
        }
        return ESBUPLOADANQUERYBUSINESSSERVICEIMPLSERVICE_WSDL_LOCATION;
    }

    public ESBUploadAnQuerybusinessServiceImplService(WebServiceFeature... features) {
        super(__getWsdlLocation(), ESBUPLOADANQUERYBUSINESSSERVICEIMPLSERVICE_QNAME, features);
    }

    public ESBUploadAnQuerybusinessServiceImplService(URL wsdlLocation) {
        super(wsdlLocation, ESBUPLOADANQUERYBUSINESSSERVICEIMPLSERVICE_QNAME);
    }

    public ESBUploadAnQuerybusinessServiceImplService(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, ESBUPLOADANQUERYBUSINESSSERVICEIMPLSERVICE_QNAME, features);
    }

    public ESBUploadAnQuerybusinessServiceImplService(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public ESBUploadAnQuerybusinessServiceImplService(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    /**
     * @return returns ESBUploadAnQuerybusinessServiceImpl
     */
    @WebEndpoint(name = "ESBUploadAnQuerybusinessServiceImplPort")
    public ESBUploadAnQuerybusinessServiceImpl getESBUploadAnQuerybusinessServiceImplPort() {
        return super.getPort(new QName("http://impl.ws.application.dataswap.css.com/", "ESBUploadAnQuerybusinessServiceImplPort"), ESBUploadAnQuerybusinessServiceImpl.class);
    }

    /**
     * @param features A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return returns ESBUploadAnQuerybusinessServiceImpl
     */
    @WebEndpoint(name = "ESBUploadAnQuerybusinessServiceImplPort")
    public ESBUploadAnQuerybusinessServiceImpl getESBUploadAnQuerybusinessServiceImplPort(WebServiceFeature... features) {
        return super.getPort(new QName("http://impl.ws.application.dataswap.css.com/", "ESBUploadAnQuerybusinessServiceImplPort"), ESBUploadAnQuerybusinessServiceImpl.class, features);
    }

}
