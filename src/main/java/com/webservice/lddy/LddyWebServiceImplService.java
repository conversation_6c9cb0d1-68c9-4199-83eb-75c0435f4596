package com.webservice.lddy;

import javax.xml.namespace.QName;
import javax.xml.ws.*;
import java.net.URL;

/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 */
@WebServiceClient(name = "LddyWebServiceImplService", targetNamespace = "http://impl.ws.application.dataswap.css.com/",
        wsdlLocation = "file:/C:/Users/<USER>/Desktop/wsld/lddyWebService.wsdl")
public class LddyWebServiceImplService
        extends Service {

    private final static URL LDDYWEBSERVICEIMPLSERVICE_WSDL_LOCATION;
    private final static WebServiceException LDDYWEBSERVICEIMPLSERVICE_EXCEPTION;
    private final static QName LDDYWEBSERVICEIMPLSERVICE_QNAME = new QName("http://impl.ws.application.dataswap.css.com/", "LddyWebServiceImplService");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            // url = new URL("file:/C:/Users/<USER>/Desktop/wsld/lddyWebService.wsdl");
            url = LddyWebServiceImplService.class.getResource("/wsdl/lddyWebService.wsdl");
        } catch (Exception ex) {
            e = new WebServiceException(ex);
        }
        LDDYWEBSERVICEIMPLSERVICE_WSDL_LOCATION = url;
        LDDYWEBSERVICEIMPLSERVICE_EXCEPTION = e;
    }

    public LddyWebServiceImplService() {
        super(__getWsdlLocation(), LDDYWEBSERVICEIMPLSERVICE_QNAME);
    }

    private static URL __getWsdlLocation() {
        if (LDDYWEBSERVICEIMPLSERVICE_EXCEPTION != null) {
            throw LDDYWEBSERVICEIMPLSERVICE_EXCEPTION;
        }
        return LDDYWEBSERVICEIMPLSERVICE_WSDL_LOCATION;
    }

    public LddyWebServiceImplService(WebServiceFeature... features) {
        super(__getWsdlLocation(), LDDYWEBSERVICEIMPLSERVICE_QNAME, features);
    }

    public LddyWebServiceImplService(URL wsdlLocation) {
        super(wsdlLocation, LDDYWEBSERVICEIMPLSERVICE_QNAME);
    }

    public LddyWebServiceImplService(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, LDDYWEBSERVICEIMPLSERVICE_QNAME, features);
    }

    public LddyWebServiceImplService(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public LddyWebServiceImplService(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    /**
     * @return returns LddyWebServiceImpl
     */
    @WebEndpoint(name = "LddyWebServiceImplPort")
    public LddyWebServiceImpl getLddyWebServiceImplPort() {
        return super.getPort(new QName("http://impl.ws.application.dataswap.css.com/", "LddyWebServiceImplPort"), LddyWebServiceImpl.class);
    }

    /**
     * @param features A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return returns LddyWebServiceImpl
     */
    @WebEndpoint(name = "LddyWebServiceImplPort")
    public LddyWebServiceImpl getLddyWebServiceImplPort(WebServiceFeature... features) {
        return super.getPort(new QName("http://impl.ws.application.dataswap.css.com/", "LddyWebServiceImplPort"), LddyWebServiceImpl.class, features);
    }

}
