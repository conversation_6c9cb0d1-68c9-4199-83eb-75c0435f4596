package com.webservice.lddy;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.ws.Action;

/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 */
@WebService(name = "LddyWebServiceImpl", targetNamespace = "http://impl.ws.application.dataswap.css.com/")
@SOAPBinding(style = SOAPBinding.Style.RPC)
public interface LddyWebServiceImpl {

    /**
     * @param arg0
     * @return returns java.lang.String
     */
    @WebMethod
    @WebResult(partName = "return")
    @Action(input = "http://impl.ws.application.dataswap.css.com/LddyWebServiceImpl/confirmRequest", output = "http://impl.ws.application.dataswap.css.com/LddyWebServiceImpl/confirmResponse")
    String confirm(
            @WebParam(name = "arg0", partName = "arg0")
                    String arg0);

    /**
     * @param arg0
     * @return returns java.lang.String
     */
    @WebMethod
    @WebResult(partName = "return")
    @Action(input = "http://impl.ws.application.dataswap.css.com/LddyWebServiceImpl/queryLddyByUniquekeyRequest", output = "http://impl.ws.application.dataswap.css.com/LddyWebServiceImpl/queryLddyByUniquekeyResponse")
    String queryLddyByUniquekey(
            @WebParam(name = "arg0", partName = "arg0")
                    String arg0);

    /**
     * @param arg0
     * @return returns java.lang.String
     */
    @WebMethod
    @WebResult(partName = "return")
    @Action(input = "http://impl.ws.application.dataswap.css.com/LddyWebServiceImpl/downloadRequest", output = "http://impl.ws.application.dataswap.css.com/LddyWebServiceImpl/downloadResponse")
    String download(
            @WebParam(name = "arg0", partName = "arg0")
                    String arg0);

    /**
     * @param arg1
     * @param arg0
     * @return returns java.lang.String
     */
    @WebMethod
    @WebResult(partName = "return")
    @Action(input = "http://impl.ws.application.dataswap.css.com/LddyWebServiceImpl/errorMsgRequest", output = "http://impl.ws.application.dataswap.css.com/LddyWebServiceImpl/errorMsgResponse")
    String errorMsg(
            @WebParam(name = "arg0", partName = "arg0")
                    String arg0,
            @WebParam(name = "arg1", partName = "arg1")
                    String arg1);

    /**
     * @param arg0
     * @return returns java.lang.String
     */
    @WebMethod
    @WebResult(partName = "return")
    @Action(input = "http://impl.ws.application.dataswap.css.com/LddyWebServiceImpl/uploadRequest", output = "http://impl.ws.application.dataswap.css.com/LddyWebServiceImpl/uploadResponse")
    String upload(
            @WebParam(name = "arg0", partName = "arg0")
                    String arg0);

}
