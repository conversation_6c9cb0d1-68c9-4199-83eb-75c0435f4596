package com.webservice.tz;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.datatype.XMLGregorianCalendar;
import javax.xml.ws.Action;

/**
 * This class was generated by the JAX-WS RI. JAX-WS RI 2.1.3-hudson-390-
 * Generated source version: 2.0
 * 
 */
@WebService(name = "HeartBeatServiceImpl", targetNamespace = "http://impl.ws.application.dataswap.css.com/")
@SOAPBinding(style = SOAPBinding.Style.RPC)
public interface HeartBeatServiceImpl {

	/**
	 * 
	 * @param arg0
	 * @return returns javax.xml.datatype.XMLGregorianCalendar
	 */
	@WebMethod
	@WebResult(partName = "return")
	@Action(input = "http://impl.ws.application.dataswap.css.com/HeartBeatServiceImpl/getTimeRequest",
			output = "http://impl.ws.application.dataswap.css.com/HeartBeatServiceImpl/getTimeResponse")
	public XMLGregorianCalendar getTime(
            @WebParam(name = "arg0", partName = "arg0") int arg0);

	/**
	 * 
	 * @param arg0
	 * @return returns java.lang.String
	 */
	@WebMethod
	@WebResult(partName = "return")
	@Action(input = "http://impl.ws.application.dataswap.css.com/HeartBeatServiceImpl/verifySignRequest",
			output = "http://impl.ws.application.dataswap.css.com/HeartBeatServiceImpl/verifySignResponse")
	public String verifySign(
            @WebParam(name = "arg0", partName = "arg0") String arg0);

	/**
	 * 
	 * @param arg0
	 * @return returns java.lang.String
	 */
	@WebMethod
	@WebResult(partName = "return")
	@Action(input = "http://impl.ws.application.dataswap.css.com/HeartBeatServiceImpl/handleSQLRequest",
			output = "http://impl.ws.application.dataswap.css.com/HeartBeatServiceImpl/handleSQLResponse")
	public String handleSQL(
            @WebParam(name = "arg0", partName = "arg0") String arg0);

	/**
	 * 
	 * @param arg0
	 * @return returns java.lang.String
	 */
	@WebMethod
	@WebResult(partName = "return")
	@Action(input = "http://impl.ws.application.dataswap.css.com/HeartBeatServiceImpl/checkStatusRequest",
			output = "http://impl.ws.application.dataswap.css.com/HeartBeatServiceImpl/checkStatusResponse")
	public String checkStatus(
            @WebParam(name = "arg0", partName = "arg0") String arg0);

	/**
	 * 
	 * @return returns java.lang.String
	 */
	@WebMethod
	@WebResult(partName = "return")
	@Action(input = "http://impl.ws.application.dataswap.css.com/HeartBeatServiceImpl/isAliveRequest",
			output = "http://impl.ws.application.dataswap.css.com/HeartBeatServiceImpl/isAliveResponse")
	public String isAlive();

}
