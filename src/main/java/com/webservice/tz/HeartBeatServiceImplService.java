package com.webservice.tz;

import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.*;

/**
 * This class was generated by the JAX-WS RI. JAX-WS RI 2.1.3-hudson-390-
 * Generated source version: 2.0
 * <p>
 * An example of how this class may be used:
 * 
 * <pre>
 * HeartBeatServiceImplService service = new HeartBeatServiceImplService();
 * HeartBeatServiceImpl portType = service.getHeartBeatServiceImplPort();
 * portType.getTime(...);
 * </pre>
 * 
 * </p>
 * 
 */
@WebServiceClient(name = "HeartBeatServiceImplService", targetNamespace = "http://impl.ws.application.dataswap.css.com/",
		wsdlLocation = "file:/C:/Users/<USER>/Desktop/heartBeat.wsdl")
public class HeartBeatServiceImplService extends Service {

	private final static URL HEARTBEATSERVICEIMPLSERVICE_WSDL_LOCATION;
	private final static WebServiceException HEARTBEATWEBSERVICEIMPLSERVICE_EXCEPTION;
	private final static QName HEARTBEATWEBSERVICEIMPLSERVICE_QNAME = new QName("http://impl.ws.application.dataswap.css.com/",
			"HeartBeatServiceImplService");

	static {
		URL url = null;
		WebServiceException e = null;
		try {
			url = HeartBeatServiceImplService.class.getResource("/wsdl/heartBeat.wsdl");
		} catch (Exception ex) {
			e = new WebServiceException(ex);
		}
		HEARTBEATSERVICEIMPLSERVICE_WSDL_LOCATION = url;
		HEARTBEATWEBSERVICEIMPLSERVICE_EXCEPTION = e;
	}

	public HeartBeatServiceImplService() {
		super(__getWsdlLocation(), HEARTBEATWEBSERVICEIMPLSERVICE_QNAME);
	}

	private static URL __getWsdlLocation() {
		if (HEARTBEATWEBSERVICEIMPLSERVICE_EXCEPTION != null) {
			throw HEARTBEATWEBSERVICEIMPLSERVICE_EXCEPTION;
		}
		return HEARTBEATSERVICEIMPLSERVICE_WSDL_LOCATION;
	}

	public HeartBeatServiceImplService(WebServiceFeature... features) {
		super(__getWsdlLocation(), HEARTBEATWEBSERVICEIMPLSERVICE_QNAME, features);
	}

	public HeartBeatServiceImplService(URL wsdlLocation) {
		super(wsdlLocation, HEARTBEATWEBSERVICEIMPLSERVICE_QNAME);
	}

	public HeartBeatServiceImplService(URL wsdlLocation, WebServiceFeature... features) {
		super(wsdlLocation, HEARTBEATWEBSERVICEIMPLSERVICE_QNAME, features);
	}

	public HeartBeatServiceImplService(URL wsdlLocation, QName serviceName) {
		super(wsdlLocation, serviceName);
	}

	public HeartBeatServiceImplService(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
		super(wsdlLocation, serviceName, features);
	}

	/**
	 * @return returns LddyWebServiceImpl
	 */
	@WebEndpoint(name = "HeartBeatServiceImplPort")
	public HeartBeatServiceImpl getHeartBeatServiceImplPort() {
		return super.getPort(new QName("http://impl.ws.application.dataswap.css.com/", "HeartBeatServiceImplPort"), HeartBeatServiceImpl.class);
	}

	/**
	 * @param features A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
	 * @return returns LddyWebServiceImpl
	 */
	@WebEndpoint(name = "HeartBeatServiceImplPort")
	public HeartBeatServiceImpl getHeartBeatServiceImplPort(WebServiceFeature... features) {
		return super.getPort(new QName("http://impl.ws.application.dataswap.css.com/", "HeartBeatServiceImplPort"), HeartBeatServiceImpl.class, features);
	}
}
