package com.zenith.front.service.operationplatformuser;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.common.OutMessage;
import com.zenith.front.entity.dto.DevOpsDto;
import com.zenith.front.entity.dto.LoginDto;
import com.zenith.front.entity.dto.OperationPlatformUserDTO;
import com.zenith.front.entity.dto.OperationPlatformUserListDTO;
import com.zenith.front.entity.model.OperationPlatformUser;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-28
 */
public interface IOperationPlatformUserService extends IService<OperationPlatformUser> {

    OutMessage login(LoginDto data);

    OutMessage getList(OperationPlatformUserListDTO data);

    OutMessage add(OperationPlatformUserDTO data);

    OutMessage node(DevOpsDto data);
}
