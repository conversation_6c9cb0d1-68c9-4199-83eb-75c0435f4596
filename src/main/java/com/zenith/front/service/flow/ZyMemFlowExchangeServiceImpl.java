package com.zenith.front.service.flow;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.webservice.lddy.LddyWebServiceImpl;
import com.webservice.lddy.LddyWebServiceImplService;
import com.webservice.util.CryptoUtil;
import com.zenith.front.common.OutMessage;
import com.zenith.front.common.Status;
import com.zenith.front.constant.CommonConstant;
import com.zenith.front.entity.model.*;
import com.zenith.front.entity.vo.OrgExchangeFlowVO;
import com.zenith.front.mapper.OrgExchangeAreaMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

import java.util.*;

import static com.webservice.util.CryptoUtil.cnToUnicode;
import static com.webservice.util.CryptoUtil.getQianMingData;

/**
 * 流动党员交换区对接
 *
 * <AUTHOR>
 * @since 2025/2/12 16:55
 */
@Service
@Slf4j
public class ZyMemFlowExchangeServiceImpl implements ZyMemFlowExchangeService {
    @Autowired
    private MemFlow1Service memFlow1Service;
    @Autowired
    private IMemFlowSignService memFlowSignService;
    @Autowired
    private IMemFlowMessageService memFlowMessageService;
    @Autowired
    private MemFlowEventsService memFlowEventsService;
    @Autowired
    private ExchangeLogService exchangeLogService;
    @Autowired
    private OrgExchangeAreaMapper orgExchangeAreaMapper;
    @Autowired
    private OrgFlowService orgFlowService;

    /**
     * 上传流动党员流入地主动报道的流动提醒信息
     * dataType为6：流入方流动提醒信息上传
     * dataType为30：流动提醒状态变更
     * dataType为34：流动提醒是否确认
     */
    @Override
    public void uploadZdbd(MemFlowSign memFlowSign) {
        // todo: tanmw 2025/2/12 17:01  差流入方区划代码
        String flowUqCode = null;
        String code = null;
        JSONObject mainObject = new JSONObject();
        mainObject.put("dataType", "6");
        mainObject.put("accessID", CommonConstant.XZQH_GUIZHOU);
        mainObject.put("uniqueKey", flowUqCode);
        String[] sm2Keys = CryptoUtil.getSm2Keys(CryptoUtil.CERT_ID);
        mainObject.put("secretKey", sm2Keys[1]);//随机码,sm2加密后的
        JSONObject zdbdJson = new JSONObject();

        zdbdJson.put("LDTX0001", "");// 接收提醒根节点代码
        zdbdJson.put("LDTX0002", CommonConstant.XZQH_GUIZHOU);// 发送提醒根节点代码
        zdbdJson.put("LDTX0003", memFlowSign.getName());// 姓名
        zdbdJson.put("LDTX0004", memFlowSign.getIdcard());// 身份证号码
        zdbdJson.put("LDTX0005", memFlowSign.getOrgFlowCode());// 流出地的党组织ID
        zdbdJson.put("LDTX0006", memFlowSign.getApplyOrgFlowCode());// 流入地党组织ID
        zdbdJson.put("LDTX0007", memFlowSign.getApplyOrgFlowName());// 流入地党组织名称
        zdbdJson.put("LDTX0008", memFlowSign.getConnection());// 联系电话
        // zdbdJson.put("LDTX0009", "");// 党组织联系人
        // zdbdJson.put("LDTX0010", "");// 党组织联系电话
        zdbdJson.put("LDTX0011", "2");// 流动提醒处理状态 1.办理中 2.已登记 3.已退回
        zdbdJson.put("LRDLXR", memFlowSign.getFlowConnection());// 流入地联系人
        zdbdJson.put("LRDLXFS", memFlowSign.getFlowConnectionName());// 流入地联系方式
        zdbdJson.put("LCDCK", "0");// 流出地是否查看提醒 0 否
        zdbdJson.put("CYSJHM", memFlowSign.getFlowConnection());// 提醒常用手机号码
        // 字典表需要转换
        zdbdJson.put("LDYY_DM", memFlowSign.getD146Code());// 党员流动原因代码 01：外出务工经商  02：外出居住  03：待业或未落实工作单位   04：未就业的毕业学生   05：自主择业的退伍军人 06：离退休人员党员异地居住  09：其他
        zdbdJson.put("LDYYXQ", memFlowSign.getFlowReasonDetail());// 其他流动原因详情
        zdbdJson.put("WCRQ", "");// 外出日期 例如 2024-10-10
        if (Objects.nonNull(memFlowSign.getOutDate())) {
            zdbdJson.put("WCRQ", DateUtil.formatDate(memFlowSign.getOutDate()));
        }
        zdbdJson.put("WCDDBCSM", memFlowSign.getOutInstructions());// 外出日期补充说明
        zdbdJson.put("DFJZRQLCD", "");// 党费缴至日期流出地 例如 2024-10-10
        if (Objects.nonNull(memFlowSign.getGhanaDate())) {
            zdbdJson.put("DFJZRQLCD", DateUtil.formatDate(memFlowSign.getGhanaDate()));
        }
        zdbdJson.put("JS_ORGID", memFlowSign.getApplyOrgFlowCode());// 接收支部

        JSONArray jsonArray = new JSONArray();
        jsonArray.add(zdbdJson);
        JSONObject infoJson = new JSONObject();
        infoJson.put("LDTX", jsonArray);
        String infoJsonString = infoJson.toJSONString();
        mainObject.put("info", CryptoUtil.sm4Encrypt(infoJsonString, sm2Keys[0]));
        // 记录未加密的数据结构
        mainObject.put("infoUpload", infoJsonString);
        log.info("uploadZdbd-mainObject请求数据:{}", mainObject.toJSONString());
        memFlow1Service.deailUploadData(code, flowUqCode, mainObject);
    }

    /**
     * info示例：
     * [
     * {
     * "LDTX": {
     * "LDTX0008": "13831888888",
     * "LDTX0009": "流动",
     * "LDYY_DM": "1000000001",
     * "WCRQ": "2024-11-25",
     * "LDTX0006": "7a812705518b4251930eb3db69a10fa0",
     * "LDTX0007": "中共义乌市流动党支部流出地成立省外",
     * "LDTX0004": "116115199009298383",
     * "LDTX0005": "7aa27e269e544f7b89b42e1ef6dd12fe",
     * "LDYYXQ": "",
     * "WCDDBCSM": "123",
     * "JS_ORGID": "7a812705518b4251930eb3db69a10fa0",
     * "CYSJHM": "",
     * "LDTX0002": "033000000001",
     * "LDTX0003": "李保存",
     * "LDTX0011": "1",
     * "ID": "95c3622d98e14c6eb15b6dbbbe2b32b7",
     * "LDTX0001": "033000000001",
     * "LRDLXR": "流入地联系人4",
     * "LCDCK": "0",
     * "LDTX0010": "13211111111",
     * "LRDLXFS": "13231231210",
     * "DFJZRQLCD": "2024-10" //党费缴至日期流出地
     * }
     * }
     * ]
     */
    @Override
    public void downloadZdbd() {
        try {
            log.info("开始执行流入地主动报道的的流动党员下载接口，开始时间：：" + new Date());
            JSONObject mainObject = new JSONObject();
            mainObject.put("dataType", "6");
            mainObject.put("accessID", CommonConstant.XZQH_GUIZHOU);
            mainObject.put("type", "1");
            log.info("downloadZdbd-mainObject请求数据:{}", mainObject.toJSONString());
            String lddyData = memFlow1Service.downloadLDDY(mainObject);
            JSONObject dataJson = JSONObject.parseObject(lddyData);
            List<MemFlowSign> memFlowSignList = new ArrayList<>();
            // 解密请求数据
            if (StrUtil.isNotEmpty(dataJson.getString("secretKey")) && StrUtil.isNotEmpty(dataJson.getString("info"))) {
                String realInfo = CryptoUtil.getRealInfo(dataJson.getString("info"), dataJson.getString("secretKey"));
                if (!realInfo.equals("[]")) {
                    JSONArray jsonArray = JSONArray.parseArray(realInfo);
                    List<String> flowUqCodeList = new ArrayList<>();
                    for (Object object : jsonArray) {
                        JSONObject data = (JSONObject) object;
                        log.info("jsonArray-object:{}", data.toJSONString());
                        String uniqueKey = data.getString("uniqueKey");
                        flowUqCodeList.add(uniqueKey);
                        MemFlowSign memFlowSign = new MemFlowSign();
                        memFlowSign.setCode(IdUtil.fastSimpleUUID());
                        // 流动提醒数据
                        JSONObject ldtxJson = data.getJSONObject("LDTX");
                        ldtxJson.getString("LDTX0001");// 接收提醒根节点代码
                        ldtxJson.getString("LDTX0002");// 发送提醒根节点代码
                        memFlowSign.setName(ldtxJson.getString("LDTX0003"));// 姓名
                        memFlowSign.setIdcard(ldtxJson.getString("LDTX0004"));// 身份证号码
                        memFlowSign.setOrgFlowCode(ldtxJson.getString("LDTX0005"));// 流出地的党组织ID
                        memFlowSign.setApplyOrgFlowCode(ldtxJson.getString("LDTX0006"));// 流入地党组织ID
                        memFlowSign.setApplyOrgFlowName(ldtxJson.getString("LDTX0007"));// 流入地党组织名称
                        memFlowSign.setConnection(ldtxJson.getString("LDTX0008"));// 联系电话
                        ldtxJson.getString("LDTX0009");// 党组织联系人
                        ldtxJson.getString("LDTX0010");// 党组织联系电话
                        ldtxJson.getString("LDTX0011");// 流动提醒处理状态 1.办理中 2.已登记 3.已退回
                        memFlowSign.setFlowConnection(ldtxJson.getString("LRDLXR"));// 流入地联系人
                        memFlowSign.setFlowConnection(ldtxJson.getString("LRDLXFS"));// 流入地联系方式
                        ldtxJson.getString("LCDCK");// 流出地是否查看提醒 0 否
                        memFlowSign.setFlowConnection(ldtxJson.getString("CYSJHM"));// 提醒常用手机号码
                        // 字典表需要转换
                        memFlowSign.setD146Code(ldtxJson.getString("LDYY_DM"));// 党员流动原因代码 01：外出务工经商  02：外出居住  03：待业或未落实工作单位   04：未就业的毕业学生   05：自主择业的退伍军人 06：离退休人员党员异地居住  09：其他
                        memFlowSign.setFlowReasonDetail(ldtxJson.getString("LDYYXQ"));// 其他流动原因详情
                        String wcrq = ldtxJson.getString("WCRQ");// 外出日期 例如 2024-10-10
                        if (StrUtil.isNotBlank(wcrq)) {
                            memFlowSign.setOutDate(DateUtil.parseDate(wcrq));
                        }
                        memFlowSign.setOutInstructions(ldtxJson.getString("WCDDBCSM"));// 外出日期补充说明
                        String dfjzrqlcd = ldtxJson.getString("DFJZRQLCD");// 党费缴至日期流出地 例如 2024-10-10
                        if (StrUtil.isNotBlank(dfjzrqlcd)) {
                            memFlowSign.setGhanaDate(DateUtil.parseDate(dfjzrqlcd));
                        }
                        memFlowSign.setApplyOrgFlowCode(ldtxJson.getString("JS_ORGID"));// 接收支部
                        // 0-本节点  1-省内非本节点 2-中组部获取的省外数据
                        memFlowSign.setDataType("2");
                        memFlowSignList.add(memFlowSign);
                    }
                    memFlowSignService.saveBatch(memFlowSignList);
                    this.confirm("6", "1", flowUqCodeList);
                } else {
                    log.info("开始执行流入地主动报道的的流动党员下载接口，请求数据未查询到数据：：" + realInfo);
                }
            }
        } catch (Exception e) {
            log.info("开始执行流入地主动报道的的流动党员下载接口，请求数据出现异常：：" + e.getMessage());
        }

    }

    @Override
    public void uploadZdbdStatus(String flowCode, String uniqueKey, String status, String LDTX0001) {
        JSONObject mainObject = new JSONObject();
        mainObject.put("dataType", "30");
        mainObject.put("accessID", CommonConstant.XZQH_GUIZHOU);
        mainObject.put("uniqueKey", uniqueKey);
        String[] sm2Keys = CryptoUtil.getSm2Keys(CryptoUtil.CERT_ID);
        mainObject.put("secretKey", sm2Keys[1]);//随机码,sm2加密后的
        JSONObject zdbdJson = new JSONObject();

        zdbdJson.put("LDTX0001", LDTX0001);// 接收提醒根节点代码
        zdbdJson.put("LDTX0003", status);// 接收提醒根节点代码
        JSONObject infoJson = new JSONObject();
        infoJson.put("LDTX", zdbdJson);
        String infoJsonString = infoJson.toJSONString();
        mainObject.put("info", CryptoUtil.sm4Encrypt(infoJsonString, sm2Keys[0]));
        // 记录未加密的数据结构
        mainObject.put("infoUpload", infoJsonString);
        log.info("uploadZdbdStatus-mainObject请求数据:{}", mainObject.toJSONString());
        memFlow1Service.deailUploadData(flowCode, uniqueKey, mainObject);
    }

    @Override
    public void downloadZdbdStatus() {
        try {
            log.info("开始执行流动提醒状态变更下载下载接口，开始时间：：" + new Date());
            JSONObject mainObject = new JSONObject();
            mainObject.put("dataType", "30");
            mainObject.put("type", "2");
            mainObject.put("accessID", CommonConstant.XZQH_GUIZHOU);
            log.info("downloadZdbdStatus-mainObject请求数据:{}", mainObject.toJSONString());
            String lddyData = memFlow1Service.downloadLDDY(mainObject);
            JSONObject dataJson = JSONObject.parseObject(lddyData);
            List<String> flowUqCodeList = new ArrayList<>();
            // 解密请求数据
            if (StrUtil.isNotEmpty(dataJson.getString("secretKey")) && StrUtil.isNotEmpty(dataJson.getString("info"))) {
                String realInfo = CryptoUtil.getRealInfo(dataJson.getString("info"), dataJson.getString("secretKey"));
                if (!realInfo.equals("[]")) {
                    JSONArray jsonArray = JSONArray.parseArray(realInfo);
                    for (Object object : jsonArray) {
                        JSONObject data = (JSONObject) object;
                        log.info("jsonArray-object:{}", data.toJSONString());
                        // 流动提醒数据
                        JSONObject ldtxJson = data.getJSONObject("LDTX");
                        String id = ldtxJson.getString("ID");// 接收提醒根节点代码
                        // 流出地状态变更状态 1.办理中 2.已登记 3.已退回-党员不存在 4.已退回-党员组织关系转接中 5.已退回-党员信息流转中
                        String ldtx0003 = ldtxJson.getString("LDTX0003");
                        flowUqCodeList.add(id);
                        // todo: tanmw 2025/2/14 13:59  需要获取到数据后看下是怎么和上传数据关联的，返回的id字段不清楚是上传的那个字段

                    }
                    this.confirm("30", "2", flowUqCodeList);
                } else {
                    log.info("开始执行流动提醒状态变更下载下载接口，请求数据未查询到数据：：" + realInfo);
                }
            }
        } catch (Exception e) {
            log.info("开始执行流动提醒状态变更下载下载接口，请求数据出现异常：：" + e.getMessage());
        }

    }

    @Override
    public void uploadZdbdRead(String flowCode, String uniqueKey, String LDTX0001) {
        JSONObject mainObject = new JSONObject();
        mainObject.put("dataType", "34");
        mainObject.put("accessID", CommonConstant.XZQH_GUIZHOU);
        mainObject.put("uniqueKey", uniqueKey);
        String[] sm2Keys = CryptoUtil.getSm2Keys(CryptoUtil.CERT_ID);
        mainObject.put("secretKey", sm2Keys[1]);//随机码,sm2加密后的
        JSONObject zdbdJson = new JSONObject();

        zdbdJson.put("LDTX0001", LDTX0001);// 接收提醒根节点代码
        zdbdJson.put("LCDCK", "1");// 接收提醒根节点代码
        JSONObject infoJson = new JSONObject();
        infoJson.put("LDTX", zdbdJson);
        String infoJsonString = infoJson.toJSONString();
        // mainObject.put("info", infoJson);
        mainObject.put("info", CryptoUtil.sm4Encrypt(infoJsonString, sm2Keys[0]));
        // 记录未加密的数据结构
        mainObject.put("infoUpload", infoJsonString);
        log.info("uploadZdbdRead-mainObject请求数据:{}", mainObject.toJSONString());
        memFlow1Service.deailUploadData(flowCode, uniqueKey, mainObject);
    }

    @Override
    public void downloadZdbdRead() {
        try {
            log.info("开始执行流动提醒流出方是否查看下载接口，开始时间：：" + new Date());
            JSONObject mainObject = new JSONObject();
            mainObject.put("dataType", "34");
            mainObject.put("type", "2");
            mainObject.put("accessID", CommonConstant.XZQH_GUIZHOU);
            log.info("downloadZdbdStatus-mainObject请求数据:{}", mainObject.toJSONString());
            String lddyData = memFlow1Service.downloadLDDY(mainObject);
            JSONObject dataJson = JSONObject.parseObject(lddyData);
            List<String> flowUqCodeList = new ArrayList<>();
            // 解密请求数据
            if (StrUtil.isNotEmpty(dataJson.getString("secretKey")) && StrUtil.isNotEmpty(dataJson.getString("info"))) {
                String realInfo = CryptoUtil.getRealInfo(dataJson.getString("info"), dataJson.getString("secretKey"));
                if (!realInfo.equals("[]")) {
                    JSONArray jsonArray = JSONArray.parseArray(realInfo);
                    for (Object object : jsonArray) {
                        JSONObject data = (JSONObject) object;
                        log.info("jsonArray-object:{}", data.toJSONString());
                        // 流动提醒数据
                        JSONObject ldtxJson = data.getJSONObject("LDTX");
                        String id = ldtxJson.getString("ID");// 接收提醒根节点代码
                        // 流出地是否查看提醒 0 否,1-是
                        String ldtx0003 = ldtxJson.getString("LCDCK");
                        flowUqCodeList.add(id);
                        // todo: tanmw 2025/2/14 13:59  需要获取到数据后看下是怎么和上传数据关联的，返回的id字段不清楚是上传的那个字段

                    }
                    this.confirm("34", "2", flowUqCodeList);
                } else {
                    log.info("开始执行流动提醒流出方是否查看下载接口，请求数据未查询到数据：：" + realInfo);
                }
            }
        } catch (Exception e) {
            log.info("开始执行流动提醒流出方是否查看下载接口，请求数据出现异常：：" + e.getMessage());
        }

    }

    @Override
    public void uploadMessage(MemFlowMessage memFlowMessage) {
        String memFlowCode = memFlowMessage.getMemFlowCode();
        MemFlow1 memFlow = memFlow1Service.findByCode(memFlowCode);
        String code = memFlow.getCode();
        String flowUqCode = memFlow.getFlowUqCode();
        String lddyId = memFlow.getLddyIdZzb();
        if (StrUtil.isBlank(lddyId)) {
            lddyId = memFlow.getCode();
        }
        JSONObject mainObject = new JSONObject();
        mainObject.put("dataType", "23");
        mainObject.put("accessID", CommonConstant.XZQH_GUIZHOU);
        String[] sm2Keys = CryptoUtil.getSm2Keys(CryptoUtil.CERT_ID);
        mainObject.put("secretKey", sm2Keys[1]);//随机码,sm2加密后的
        JSONObject zdbdJson = new JSONObject();
        zdbdJson.put("ID", memFlowMessage.getId());// 32位唯一码
        zdbdJson.put("LDDY_ID", lddyId);// 流动记录表的ID,32位唯一码
        zdbdJson.put("LDMS0001", memFlowMessage.getDirection());// 消息走向 1 流出方发送 2 流入方发送
        zdbdJson.put("LDMS0002", memFlowMessage.getOutFlowCode());// 流出方具有审批预备党员权限的组织32位唯一码
        zdbdJson.put("LDMS0003", memFlowMessage.getIntoFlowCode());// 流入方具有审批预备党员权限的组织32位唯一码
        zdbdJson.put("LDMS0004", memFlowMessage.getMessage());// 发送消息的内容
        zdbdJson.put("LDMS0005", memFlowMessage.getOutFlowName());// 流出方具有审批预备党员权限的组织名称
        zdbdJson.put("LDMS0006", memFlowMessage.getIntoFlowName());// 流入方具有审批预备党员权限的组织名称
        zdbdJson.put("LDMS0007", memFlowMessage.getOutFlowNode());// 流出方接入业务数据交换区各省部级单位唯一编码
        zdbdJson.put("LDMS0008", memFlowMessage.getIntoFlowNode());// 流入方接入业务数据交换区各省部级单位唯一编码

        JSONArray jsonArray = new JSONArray();
        jsonArray.add(zdbdJson);
        String infoJsonString = jsonArray.toJSONString();
        mainObject.put("info", CryptoUtil.sm4Encrypt(infoJsonString, sm2Keys[0]));
        mainObject.put("infoUpload", infoJsonString);
        // mainObject.put("info", jsonArray);
        // mainObject.put("info", jsonArray);
        log.info("uploadMessage-mainObject请求数据:{}", mainObject.toJSONString());
        this.uploadLddyData(code, flowUqCode, mainObject, 1000);
    }

    @Override
    public void downloadMessage(String type) {
        try {
            String dataType = "26";
            log.info("开始执行流动党员消息下载接口，开始时间：：" + new Date());
            JSONObject pushJson = new JSONObject();
            pushJson.put("type", type);
            pushJson.put("accessID", CommonConstant.XZQH_GUIZHOU);
            pushJson.put("dataType", dataType);
            for (int i = 1; i < 10000; i++) {
                int pageno = i;
                int pagesize = 20;
                pushJson.put("pagesize", pagesize);
                pushJson.put("pageno", pageno);
                String lddyData = memFlow1Service.downloadLDDY(pushJson);
                String logId = this.saveDownloadExchangeLogMemFlowOther("ccp_mem_flow_message", lddyData);
                JSONObject dataJson = JSONObject.parseObject(lddyData);
                //解密请求数据
                if (StrUtil.isNotEmpty(dataJson.getString("secretKey")) && StrUtil.isNotEmpty(dataJson.getString("info"))) {
                    String realInfo = CryptoUtil.getRealInfo(dataJson.getString("info"), dataJson.getString("secretKey"));
                    // log.info("开始执行流动党员消息下载接口,获取到结果数据:{}", realInfo);
                    this.updateDownloadExchangeLog(logId, realInfo);
                    if (!realInfo.equals("[]")) {
                        JSONArray jsonArray = JSONArray.parseArray(realInfo);
                        List<String> lddyIdList = new ArrayList<>();
                        List<MemFlowMessage> memFlowMessageList = new ArrayList<>();
                        for (Object obj : jsonArray) {
                            MemFlowMessage memFlowMessage = new MemFlowMessage();
                            JSONObject jsonObject = (JSONObject) obj;
                            memFlowMessage.setId(jsonObject.getString("ID"));
                            String lddyId = jsonObject.getString("LDDY_ID");
                            MemFlow1 byLddyId = memFlow1Service.findByLddyId(lddyId);
                            if (Objects.nonNull(byLddyId)) {
                                memFlowMessage.setMemFlowCode(byLddyId.getCode());
                            } else {
                                byLddyId = memFlow1Service.findByCode(lddyId);
                                if (Objects.nonNull(byLddyId)) {
                                    memFlowMessage.setMemFlowCode(byLddyId.getCode());
                                }
                            }
                            memFlowMessage.setDirection(jsonObject.getString("LDMS0001"));
                            memFlowMessage.setOutFlowCode(jsonObject.getString("LDMS0002"));
                            memFlowMessage.setIntoFlowCode(jsonObject.getString("LDMS0003"));
                            memFlowMessage.setMessage(jsonObject.getString("LDMS0004"));
                            memFlowMessage.setOutFlowName(jsonObject.getString("LDMS0005"));
                            memFlowMessage.setIntoFlowName(jsonObject.getString("LDMS0006"));
                            memFlowMessage.setOutFlowNode(jsonObject.getString("LDMS0007"));
                            memFlowMessage.setIntoFlowNode(jsonObject.getString("LDMS0008"));
                            memFlowMessage.setSourceType(2);
                            memFlowMessage.setSendTime(DateUtil.formatDateTime(new Date()));
                            memFlowMessage.setCreateTime(new Date());
                            memFlowMessage.setSendName(memFlowMessage.getOutFlowName());
                            memFlowMessage.setSendCode(memFlowMessage.getOutFlowCode());
                            // memFlowMessage.setRemark(2);
                            String memFlowCode = memFlowMessage.getMemFlowCode();
                            if (StrUtil.isNotEmpty(memFlowCode)) {
                                lddyIdList.add(memFlowCode);
                            }
                            memFlowMessageList.add(memFlowMessage);
                        }
                        if (CollUtil.isNotEmpty(memFlowMessageList)) {
                            memFlowMessageService.saveOrUpdateBatch(memFlowMessageList);
                            this.confirm(dataType, type, lddyIdList);
                        }
                    } else {
                        log.info("开始执行流动党员消息下载接口，请求数据未查询到数据：：" + realInfo);
                        break;
                    }
                } else {
                    break;
                }
            }
        } catch (Exception e) {
            log.info("开始执行流动党员消息下载接口，请求数据出现异常：：", e);
        }
    }

    @Override
    public void uploadFwhdsj(MemFlowEvents memFlowEvents, String type, String optType) {
        String memFlowCode = memFlowEvents.getMemFlowCode();
        MemFlow1 memFlow = memFlow1Service.findByCode(memFlowCode);
        String code = memFlow.getCode();
        String flowUqCode = memFlow.getFlowUqCode();
        String lddyId = memFlow.getLddyIdZzb();
        if (StrUtil.isBlank(lddyId)) {
            lddyId = memFlow.getCode();
        }
        JSONObject mainObject = new JSONObject();
        mainObject.put("dataType", "27");
        mainObject.put("accessID", CommonConstant.XZQH_GUIZHOU);
        String[] sm2Keys = CryptoUtil.getSm2Keys(CryptoUtil.CERT_ID);
        String randomStr = sm2Keys[0];
        String secretKey = sm2Keys[1];
        mainObject.put("secretKey", secretKey);//随机码,sm2加密后的
        JSONObject zdbdJson = new JSONObject();
        zdbdJson.put("ID", memFlowEvents.getId());// 32位唯一码
        zdbdJson.put("LDDJ0001", flowUqCode);// 流动党员登记的唯一编码。
        zdbdJson.put("LDHD0001", memFlowEvents.getEventType());// 1 组织生活（三会一课、主题党日）2组织生活会 3民主评议党员 4志愿服务 5其他
        zdbdJson.put("LDHD0002", memFlowEvents.getEventName());// 流动党员参加的活动名称或志愿服务名称。
        zdbdJson.put("LDHD0003", DateUtil.formatDateTime(memFlowEvents.getEventDate()));// 填写日期
        zdbdJson.put("LDHD0004", memFlowEvents.getEventDetails());// 流动党员参加活动情况的简要说明。
        zdbdJson.put("LDHD0007", type);// 消息走向 1 流出方发送 2 流入方发送
        zdbdJson.put("LDHD0008", memFlowEvents.getOutflowNodeCode());// 流出方根节点代码
        zdbdJson.put("LDHD0009", memFlowEvents.getInflowNodeCode());// 流入方根节点代码
        zdbdJson.put("LDDY_ID", lddyId);// 流动党员id
        zdbdJson.put("LDHD0011", DateUtil.formatDateTime(memFlowEvents.getCreateTime()));// 创建时间
        zdbdJson.put("LDHD0013", optType);// 1 新增 2修改 3 删除

        JSONArray jsonArray = new JSONArray();
        jsonArray.add(zdbdJson);

        String infoJsonString = jsonArray.toJSONString();
        String sm2Encrypt = CryptoUtil.sm4Encrypt(infoJsonString, randomStr);
        mainObject.put("infoUpload", infoJsonString);
        mainObject.put("info", sm2Encrypt);
        log.info("uploadFwhdsj-mainObject请求数据:{}", mainObject.toJSONString());
        this.uploadLddyData(code, flowUqCode, mainObject, 1001);
    }

    @Override
    public void downloadFwhdsj(String type) {
        try {
            String dataType = "28";
            log.info("开始执行流动党员服务活动下载接口，开始时间：：" + new Date());
            JSONObject pushJson = new JSONObject();
            pushJson.put("type", type);
            pushJson.put("accessID", CommonConstant.XZQH_GUIZHOU);
            pushJson.put("dataType", dataType);
            for (int i = 1; i < 10000; i++) {
                int pageno = i;
                int pagesize = 20;
                pushJson.put("pagesize", pagesize);
                pushJson.put("pageno", pageno);
                String lddyData = memFlow1Service.downloadLDDY(pushJson);
                String logId = this.saveDownloadExchangeLogMemFlowOther("mem_flow_events", lddyData);
                JSONObject dataJson = JSONObject.parseObject(lddyData);
                //解密请求数据
                if (StrUtil.isNotEmpty(dataJson.getString("secretKey")) && StrUtil.isNotEmpty(dataJson.getString("info"))) {
                    String realInfo = CryptoUtil.getRealInfo(dataJson.getString("info"), dataJson.getString("secretKey"));
                    this.updateDownloadExchangeLog(logId, realInfo);
                    // log.info("开始执行流动党员服务活动下载接口,获取到结果数据:{}", realInfo);
                    if (!realInfo.equals("[]")) {
                        JSONArray jsonArray = JSONArray.parseArray(realInfo);
                        List<String> lddyIdList = new ArrayList<>();
                        List<MemFlowEvents> memFlowMessageList = new ArrayList<>();
                        for (Object obj : jsonArray) {
                            MemFlowEvents memFlowMessage = new MemFlowEvents();
                            JSONObject jsonObject = (JSONObject) obj;

                            memFlowMessage.setId(jsonObject.getString("ID"));
                            String flowUqCode = jsonObject.getString("LDDJ0001");
                            if (StrUtil.isBlank(flowUqCode)) {
                                continue;
                            }
                            MemFlow1 byLddyId = memFlow1Service.findByUqCode(flowUqCode);
                            if (Objects.nonNull(byLddyId)) {
                                memFlowMessage.setMemFlowCode(byLddyId.getCode());
                            }
                            memFlowMessage.setEventType(jsonObject.getString("LDHD0001"));
                            memFlowMessage.setEventName(jsonObject.getString("LDHD0002"));
                            String ldhd0003 = jsonObject.getString("LDHD0003");
                            if (StrUtil.isNotBlank(ldhd0003)) {
                                memFlowMessage.setEventDate(DateUtil.parseDateTime(ldhd0003));
                            }
                            memFlowMessage.setEventDetails(jsonObject.getString("LDHD0004"));
                            memFlowMessage.setOutflowNodeCode(jsonObject.getString("LDHD0008"));
                            memFlowMessage.setInflowNodeCode(jsonObject.getString("LDHD0009"));
                            String ldhd0011 = jsonObject.getString("LDHD0011");
                            if (StrUtil.isNotBlank(ldhd0003)) {
                                memFlowMessage.setCreateTime(DateUtil.parseDateTime(ldhd0011));
                            }
                            // 1 新增 2修改 3 删除
                            String ldhd0013 = jsonObject.getString("LDHD0013");
                            if (StrUtil.equals(ldhd0013, "3")) {
                                memFlowMessage.setDeleteTime(new Date());
                            }
                            memFlowMessage.setSourceType(2);
                            String memFlowCode = memFlowMessage.getMemFlowCode();
                            if (StrUtil.isNotEmpty(memFlowCode)) {
                                lddyIdList.add(memFlowCode);
                            }
                            // lddyIdList.add(memFlowCode);
                            memFlowMessageList.add(memFlowMessage);
                        }
                        if (CollUtil.isNotEmpty(memFlowMessageList)) {
                            memFlowEventsService.saveOrUpdateBatch(memFlowMessageList);
                            this.confirm(dataType, type, lddyIdList);
                        }
                    } else {
                        log.info("开始执行流动党员服务活动下载接口，请求数据未查询到数据:{}", realInfo);
                        break;
                    }
                } else {
                    break;
                }
            }
        } catch (Exception e) {
            log.info("开始执行流动党员服务活动下载接口，请求数据出现异常：：", e);
        }
    }

    /**
     * 上传 下载确认信息
     *
     * @param dataType
     * @param type
     * @param flowUqCodeList
     * @throws Exception
     */
    @Override
    public String confirm(String dataType, String type, List<String> flowUqCodeList) throws Exception {
        String resultMsg = null;
        if (CollUtil.isEmpty(flowUqCodeList) || flowUqCodeList.isEmpty()) {
            return resultMsg;
        }
        // log.info("确认的集合：{}", JSONUtil.toJsonStr(flowUqCodeList));
        JSONObject mainObject = new JSONObject();
        mainObject.put("dataType", dataType);
        mainObject.put("type", type);
        mainObject.put("accessID", CommonConstant.XZQH_GUIZHOU);
        // JSONArray jsonArray = new JSONArray();
        // jsonArray.addAll(flowUqCodeList);

        if (flowUqCodeList.size() > CommonConstant.ZERO_INT) {
            JSONArray jsonArray = new JSONArray();
            flowUqCodeList.forEach(uq -> {
                JSONObject object = new JSONObject();
                object.put("uniqueKey", uq);
                jsonArray.add(object);
            });
            mainObject.put("info", jsonArray);
            // log.info("开始执行流动党员下载接口:dataType{},type:{}，进行中组部消息确认，请求数据：{}", dataType, type, jsonArray.toJSONString());
            resultMsg = memFlow1Service.uploadLDDY(mainObject, true);
            log.info("开始执行流动党员下载接口:dataType{},type:{}，进行中组部消息确认，反馈数据：{}", dataType, type, resultMsg);
        }
        return resultMsg;
    }

    @Override
    public String saveDownloadExchangeLogMemFlowOther(String typeName, String allResult) throws Exception {
        String logId = IdUtil.fastSimpleUUID();
        try {
            ExchangeLog exchangeLog = new ExchangeLog();
            exchangeLog.setId(logId);
            exchangeLog.setTypeName(typeName);
            exchangeLog.setBizId(typeName);
            exchangeLog.setIsSuccess(1);
            exchangeLog.setCreateTime(new Date());
            exchangeLog.setResultData(allResult);
            exchangeLogService.save(exchangeLog);
        } catch (Exception e) {
            log.error("保存下载流动党员其他数据异常", e);
        }
        return logId;
    }

    @Override
    public void updateDownloadExchangeLog(String logId, String info) {
        try {
            ExchangeLog exchangeLog = exchangeLogService.getById(logId);
            if (Objects.isNull(exchangeLog)) {
                return;
            }
            exchangeLog.setResultParseData(info);
            exchangeLogService.updateById(exchangeLog);
        } catch (Exception e) {
            log.error("保存下载流动党员其他数据异常", e);
        }
    }

    /**
     * 推送流动党员相关数据
     *
     * @param flowCode
     * @param flowUqCode
     * @param pushJson
     */
    public void uploadLddyData(String flowCode, String flowUqCode, JSONObject pushJson, Integer type) {
        try {
            String infoUpload = pushJson.getString("infoUpload");
            pushJson.remove("infoUpload");
            String resultJson = memFlow1Service.uploadLDDY(pushJson, false);
            String str = "";
            if (type == 1000) {
                str = "发送消息";
            } else if (type == 1001) {
                str = "发送服务活动数据";
            } else if (type == 1006) {
                str = "发送联系方式";
            }
            log.info("{}请求结果:{}",str, resultJson);
            pushJson.put("info", infoUpload);
            memFlow1Service.deailWithUploadData(resultJson, flowCode, flowUqCode, type, pushJson.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
            memFlow1Service.catchDeailData(flowCode, flowUqCode, type, pushJson.toJSONString());
        }
    }

    @Override
    public void downloadLxfs() {
        try {
            String dataType = "24";
            log.info("开始执行流动党员下载需要上传联系方式的记录，开始时间：：" + new Date());
            JSONObject pushJson = new JSONObject();
            pushJson.put("type", "1");
            pushJson.put("accessID", CommonConstant.XZQH_GUIZHOU);
            pushJson.put("dataType", dataType);
            for (int i = 1; i < 10000; i++) {
                int pageno = i;
                int pagesize = 20;
                pushJson.put("pagesize", pagesize);
                pushJson.put("pageno", pageno);
                String lddyData = memFlow1Service.downloadLDDY(pushJson);
                String logId = this.saveDownloadExchangeLogMemFlowOther("mem_flow_lxfs", lddyData);
                JSONObject dataJson = JSONObject.parseObject(lddyData);
                //解密请求数据
                if (StrUtil.isNotEmpty(dataJson.getString("secretKey")) && StrUtil.isNotEmpty(dataJson.getString("info"))) {
                    String realInfo = CryptoUtil.getRealInfo(dataJson.getString("info"), dataJson.getString("secretKey"));
                    this.updateDownloadExchangeLog(logId, realInfo);
                    // log.info("开始执行流动党员服务活动下载接口,获取到结果数据:{}", realInfo);
                    if (!realInfo.equals("[]")) {
                        JSONArray jsonArray = JSONArray.parseArray(realInfo);
                        List<String> lddyIdList = new ArrayList<>();
                        for (Object obj : jsonArray) {
                            JSONObject jsonObject = (JSONObject) obj;
                            JSONObject info = new JSONObject();
                            String id = jsonObject.getString("ID");
                            info.put("ID", id);
                            lddyIdList.add(id);
                            info.put("LDDY_ID", jsonObject.getString("LDDY_ID"));
                            info.put("LDLX0001", jsonObject.getString("LDLX0001"));
                            info.put("LDLX0002", jsonObject.getString("LDLX0002"));
                            String spOrgid = jsonObject.getString("SP_ORGID");
                            info.put("SP_ORGID", spOrgid);
                            OrgExchangeArea orgByCode = orgExchangeAreaMapper.findOrgByCode(spOrgid);
                            if (Objects.nonNull(orgByCode)) {
                                String LDLX0004 = orgByCode.getContacter();
                                info.put("LDLX0004", LDLX0004);
                                String LDLX0003 = orgByCode.getContactPhone();
                                info.put("LDLX0003", LDLX0003);
                            }else {
                                OrgExchangeFlowVO orgFlow = orgFlowService.findByCode(spOrgid);
                                if (Objects.nonNull(orgFlow)) {
                                    String LDLX0004 = orgFlow.getContacter();
                                    info.put("LDLX0004", LDLX0004);
                                    String LDLX0003 = orgFlow.getContactPhone();
                                    info.put("LDLX0003", LDLX0003);
                                }
                            }

                            this.uploadLxfs(info);
                        }
                        if (CollUtil.isNotEmpty(lddyIdList)) {
                            this.confirm(dataType, "2", lddyIdList);
                        }
                    } else {
                        log.info("开始执行流动党员下载需要上传联系方式的记录，请求数据未查询到数据:{}", realInfo);
                        break;
                    }
                } else {
                    break;
                }
            }
        } catch (Exception e) {
            log.info("开始执行流动党员下载需要上传联系方式的记录，请求数据出现异常：：", e);
        }
    }

    @Override
    public void downloadToMyLxfs() {
        try {
            String dataType = "25";
            log.info("开始执行流动党员获取对方上传联系方式下载接口，开始时间：：" + new Date());
            JSONObject pushJson = new JSONObject();
            pushJson.put("type", "1");
            pushJson.put("accessID", CommonConstant.XZQH_GUIZHOU);
            pushJson.put("dataType", dataType);
            for (int i = 1; i < 10000; i++) {
                int pageno = i;
                int pagesize = 20;
                pushJson.put("pagesize", pagesize);
                pushJson.put("pageno", pageno);
                String lddyData = memFlow1Service.downloadLDDY(pushJson);
                String logId = this.saveDownloadExchangeLogMemFlowOther("mem_flow_lxfs_to_my", lddyData);
                JSONObject dataJson = JSONObject.parseObject(lddyData);
                //解密请求数据
                if (StrUtil.isNotEmpty(dataJson.getString("secretKey")) && StrUtil.isNotEmpty(dataJson.getString("info"))) {
                    String realInfo = CryptoUtil.getRealInfo(dataJson.getString("info"), dataJson.getString("secretKey"));
                    this.updateDownloadExchangeLog(logId, realInfo);
                    // log.info("开始执行流动党员服务活动下载接口,获取到结果数据:{}", realInfo);
                    if (!realInfo.equals("[]")) {
                        JSONArray jsonArray = JSONArray.parseArray(realInfo);
                        List<String> lddyIdList = new ArrayList<>();
                        for (Object obj : jsonArray) {
                            JSONObject jsonObject = (JSONObject) obj;
                            String lddyId = jsonObject.getString("LDDY_ID");
                            String id = jsonObject.getString("ID");
                            lddyIdList.add(id);
                            // 党组织联系电话
                            String ldlx0003 = jsonObject.getString("LDLX0003");
                            // 党组织联系人
                            String ldlx0004 = jsonObject.getString("LDLX0004");
                            MemFlow1 byLddyId = memFlow1Service.findByLddyId(lddyId);
                            if (Objects.nonNull(byLddyId)) {
                                byLddyId.setOutOrgContact(ldlx0004);
                                byLddyId.setOutOrgContactPhone(ldlx0003);
                                memFlow1Service.updateById(byLddyId);
                            } else {
                                byLddyId = memFlow1Service.findByCode(lddyId);
                                if (Objects.nonNull(byLddyId)) {
                                    byLddyId.setOutOrgContact(ldlx0004);
                                    byLddyId.setOutOrgContactPhone(ldlx0003);
                                    memFlow1Service.updateById(byLddyId);
                                }
                            }
                        }
                        if (CollUtil.isNotEmpty(lddyIdList)) {
                            this.confirm(dataType, "1", lddyIdList);
                        }
                    } else {
                        log.info("开始执行流动党员获取对方上传联系方式下载接口，请求数据未查询到数据:{}", realInfo);
                        break;
                    }
                } else {
                    break;
                }
            }
        } catch (Exception e) {
            log.info("开始执行流动党员获取对方上传联系方式下载接口，请求数据出现异常：：", e);
        }
    }

    private void uploadLxfs(JSONObject infoJson) {
        try {
            JSONObject pushJson = new JSONObject();
            pushJson.put("dataType", "22");
            pushJson.put("accessID", CommonConstant.XZQH_GUIZHOU);

            String[] sm2Keys = CryptoUtil.getSm2Keys(CryptoUtil.CERT_ID);
            String randomStr = sm2Keys[0];
            String secretKey = sm2Keys[1];

            JSONArray jsonArray = new JSONArray();
            jsonArray.add(infoJson);
            String lddyId = infoJson.getString("LDDY_ID");
            String jsonString = jsonArray.toJSONString();
            pushJson.put("infoUpload", jsonString);
            String sm2Encrypt = CryptoUtil.sm4Encrypt(jsonString, randomStr);
            pushJson.put("info", sm2Encrypt);
            pushJson.put("secretKey", secretKey);


            log.info("uploadLxfs请求数据:{}", pushJson.toJSONString());
            this.uploadLddyData(lddyId, lddyId, pushJson, 1006);
        } catch (Exception e) {
            log.error("上传获取联系方式异常", e);
        }
    }
}
