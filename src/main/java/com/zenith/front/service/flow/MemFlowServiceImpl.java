package com.zenith.front.service.flow;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.webservice.lddy.LddyWebServiceImpl;
import com.webservice.lddy.LddyWebServiceImplService;
import com.webservice.util.CryptoTwoUtil;
import com.webservice.util.CryptoUtil;
import com.zenith.front.common.OutMessage;
import com.zenith.front.common.Status;
import com.zenith.front.constant.CommonConstant;
import com.zenith.front.entity.model.*;
import com.zenith.front.mapper.*;
import com.zenith.front.service.relationshiptransfer.ITransferOrgService;
import com.zenith.front.untils.FlowUntil;
import com.zenith.front.untils.StrKit;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;
import springfox.documentation.spring.web.json.Json;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.webservice.util.CryptoUtil.cnToUnicode;
import static com.webservice.util.CryptoUtil.getQianMingData;
import static com.zenith.front.service.flow.MemFlowMessageServiceImpl.splitList;

/**
 * <AUTHOR>
 * @description 针对表【ccp_mem_flow】的数据库操作Service实现
 * @createDate 2021-11-14 14:58:57
 */
@Service
@Slf4j
public class MemFlowServiceImpl extends ServiceImpl<MemFlowMapper, MemFlow> implements MemFlowService {

    @Resource
    private DevOpsMapper opsMapper;
    @Resource
    private FlowMonthStatisticsMapper flowMonthStatisticsMapper;
    @Autowired
    private IMemFlowSignService iMemFlowSignService;
    @Autowired
    private MemFlow1Service memFlow1Service;
    @Autowired
    private MemFlow1Mapper memFlow1Mapper;
    @Autowired
    private ZyMemFlowExchangeService zyMemFlowExchangeService;

    @Resource
    private IMemFlowSignService memFlowSignService;

    @Resource
    private OrgExchangeAreaMapper orgExchangeAreaMapper;
    @Resource
    private FlowContentMapper flowContentMapper;
    @Autowired
    private FlowUploadMapper flowUploadMapper;
    @Autowired
    private TransferOrgMapper transferOrgMapper;

    @Autowired
    private TransferOrgFlowMapper transferOrgFlowMapper;
    /**
     * 获取序号
     */
    @Autowired
    private StringRedisTemplate redisTemplate;

    @Override
    public OutMessage<Object> syncSave(MemFlow memFlow) {
        boolean save = save(memFlow);
        return new OutMessage<>(save ? Status.SUCCESS : Status.FAIL);
    }

    @Override
    public OutMessage<Object> pull(List<String> orgCode, List<String> excludeOrgCode) {
        LambdaQueryWrapper<MemFlow> queryWrapper = new LambdaQueryWrapper<MemFlow>()
                .and(
                        wrapper ->
                                wrapper.isNull(MemFlow::getHasSync)
                                        .or()
                                        .ne(MemFlow::getHasSync, CommonConstant.ONE_INT))
                .and(
                        wrapper ->
                                IntStream.range(0, orgCode.size()).forEach(i ->
                                {
                                    wrapper.likeRight(MemFlow::getOutflowOrgOrgCode, orgCode.get(i));
                                    if (i < orgCode.size() - 1) {
                                        wrapper.or();
                                    }
                                })
                );
        List<MemFlow> memFlowList = list(queryWrapper);
        if (CollUtil.isEmpty(memFlowList)) {
            return new OutMessage<>(Status.SUCCESS, memFlowList);
        }
        if (CollUtil.isEmpty(excludeOrgCode)) {
            changeIdentity(memFlowList);
            return new OutMessage<>(Status.SUCCESS, memFlowList);
        }
        List<Long> excludeMemFlowList = new ArrayList<>();
        memFlowList.forEach(memFlow -> excludeOrgCode.stream().filter(s -> StrUtil.startWith(memFlow.getOutflowOrgOrgCode(), s)).map(s -> memFlow.getId()).forEach(excludeMemFlowList::add));
        List<MemFlow> result = memFlowList.stream().filter(ob -> !excludeMemFlowList.contains(ob.getId())).collect(Collectors.toList());
        changeIdentity(memFlowList);
        return new OutMessage<>(Status.SUCCESS, result);
    }

    @Override
    public OutMessage monthStatistics(JSONObject jsonObject) {
        FlowMonthStatistics uploadFlowMonthObj = JSON.toJavaObject(jsonObject, FlowMonthStatistics.class);
        uploadFlowMonthObj.setID(StrKit.getRandomUUID());
        String zjtj0037 = uploadFlowMonthObj.getLDTJ0037();
        DevOps devOps = opsMapper.selectOne(new LambdaQueryWrapper<DevOps>().eq(DevOps::getNodeKey, zjtj0037));
        if (ObjectUtil.isNotNull(devOps)) {
            uploadFlowMonthObj.setLDTJ0003(devOps.getMonthDatCode());
            uploadFlowMonthObj.setLDTJ0004(devOps.getMonthDatName());

        } else {
            uploadFlowMonthObj.setLDTJ0003("暂无顶层节点");
            uploadFlowMonthObj.setLDTJ0004("暂无顶层节点");
        }
        //在本级党组织展示时使用的排序号
        uploadFlowMonthObj.setLDTJ0034(CommonConstant.ONE_INT);
        uploadFlowMonthObj.setLDTJ0040(CommonConstant.ONE_INT);
        uploadFlowMonthObj.setLDTJ0041(CommonConstant.ONE_INT);
        uploadFlowMonthObj.setCreateTime(new Date());
        uploadFlowMonthObj.setLDTJ0033(new Date());

        //判断当前月份是否存在月度数据
        Date ldtj0002 = uploadFlowMonthObj.getLDTJ0002();
        LambdaQueryWrapper<FlowMonthStatistics> flowMonthStatisticsLambdaQueryWrapper = new LambdaQueryWrapper<FlowMonthStatistics>().eq(FlowMonthStatistics::getLDTJ0037, zjtj0037);
        flowMonthStatisticsLambdaQueryWrapper.isNull(FlowMonthStatistics::getDeleteTime);
        flowMonthStatisticsLambdaQueryWrapper.last(" and to_char(out_time,'yyyy-mm')='" + ldtj0002 + "'");
        FlowMonthStatistics flowMonthStatistics = flowMonthStatisticsMapper.selectOne(flowMonthStatisticsLambdaQueryWrapper);
        if (ObjectUtil.isNotNull(flowMonthStatistics)) {
            flowMonthStatistics.setDeleteTime(new Date());
            flowMonthStatisticsMapper.updateById(flowMonthStatistics);
        }
        //存储数据库
        int insert = flowMonthStatisticsMapper.insert(uploadFlowMonthObj);
        return new OutMessage(insert > CommonConstant.ZERO_INT ? Status.SUCCESS : Status.FAIL);
    }

    @Override
    public OutMessage activeFlow(JSONObject json) throws Exception {

        return null;
    }

    @Override
    public OutMessage activeTx(Object o) {
        LddyWebServiceImplService lddyWebServiceImplService = new LddyWebServiceImplService();
        LddyWebServiceImpl lddyWebServiceImplPort = lddyWebServiceImplService.getLddyWebServiceImplPort();
        cn.hutool.json.JSONObject jsonObject = new cn.hutool.json.JSONObject();
        jsonObject.put("dataType", "30");
        jsonObject.put("accessID", "052000000001");
        jsonObject.put("uniqueKey", "052000000001");
        // TODO: 2025/2/21 提醒数据结构
        //{"accessID":"035000000001","dataType":"30","uniqueKey":"a2fbed2e4f6736a034c01e2bb264dab8","info":"{\"LDTX\":{\"LDTX0003\":\"3\",\"LDTX0001\":\"011100020786\"}}"}

        return null;
    }

    @Override
    public boolean uploadRemind(MemFlowSign memFlowSign) {
        String code = memFlowSign.getCode();
        // String serialNumber = this.getSerialNumber();
        // String serialNumber = this.getSerialNumber();
        // String uniqueKey = CryptoUtil.getJSX("052000012223" + DateUtil.format(new Date(), "yyyyMMdd") + serialNumber);
        String uniqueKey = IdUtil.fastSimpleUUID();
        JSONObject uploadJson = new JSONObject();
        uploadJson.put("LDTX0001", StringUtil.isNotBlank(memFlowSign.getReceiveNodeCode()) ? memFlowSign.getReceiveNodeCode() : "");//接收提醒根节点代码
        uploadJson.put("LDTX0002", "052000000001");//发送提醒根节点代码
        uploadJson.put("LDTX0003", StringUtil.isNotBlank(memFlowSign.getName()) ? memFlowSign.getName() : "");//姓名
        uploadJson.put("LDTX0004", StringUtil.isNotBlank(memFlowSign.getIdcard()) ? memFlowSign.getIdcard() : "");//身份证号码
        uploadJson.put("LDTX0005", StringUtil.isNotBlank(memFlowSign.getOrgFlowCode()) ? memFlowSign.getOrgFlowCode() : "");//流出地的党组织ID
        // TODO: 2025/2/20 也是 D01ID，并且是交换区上面有得才行(这里要补逻辑)
        String applyOrgFlowCode = memFlowSign.getApplyOrgFlowCode();
        OrgExchangeArea approveOrg = this.findApproveOrg(memFlowSign.getApplyOrgFlowCode());
        uploadJson.put("LDTX0006", StringUtil.isNotBlank(approveOrg.getCode()) ? applyOrgFlowCode : "");//流入地党组织ID
        // TODO: 2025/2/20 这个名称可能会超长（这里要补逻辑）
        uploadJson.put("LDTX0007", StringUtil.isNotBlank(memFlowSign.getApplyOrgFlowName()) ? memFlowSign.getApplyOrgFlowName() : "");//流入地党组织名称
        uploadJson.put("uniqueKey", uniqueKey);//流动提醒ID
        uploadJson.put("LDTX0008", StringUtil.isNotBlank(memFlowSign.getFrequentlyPhone()) ? memFlowSign.getFrequentlyPhone() : "");//联系电话
        uploadJson.put("LDTX0009", StringUtil.isNotBlank(memFlowSign.getOrgConnectionName()) ? memFlowSign.getOrgConnectionName() : "");//党组织联系人
        uploadJson.put("LDTX0010", StringUtil.isNotBlank(memFlowSign.getOrgConnection()) ? memFlowSign.getOrgConnection() : "");//党组织联系电话
        // TODO: 2025/2/20 这个值，初始上传就得是2
        uploadJson.put("LDTX0011", "1");//流动登记状态  1.办理中 2.已登记 3.已退回-党员不存在 4.已退回-党员组织关系转接中 5.已退回-党员信息流转中
        uploadJson.put("LRDLXR", StringUtil.isNotBlank(memFlowSign.getFlowConnectionName()) ? memFlowSign.getFlowConnectionName() : "");//流入地联系人
        uploadJson.put("LRDLXFS", StringUtil.isNotBlank(memFlowSign.getFlowConnection()) ? memFlowSign.getFlowConnection() : "");//流入地联系方式
        uploadJson.put("LCDCK", "0");//流出地是否查看提醒 0 否
        uploadJson.put("CYSJHM", StringUtil.isNotBlank(memFlowSign.getFrequentlyPhone()) ? memFlowSign.getFrequentlyPhone() : "");//提醒常用手机号码
        String d146Code = memFlowSign.getD146Code();
        if (d146Code.equals("09")) {
            uploadJson.put("LDYYXQ", StringUtil.isNotBlank(memFlowSign.getOutInstructions()) ? memFlowSign.getOutInstructions() : "");//其他流动原因详情
        } else {
            uploadJson.put("LDYYXQ", "");//其他流动原因详情
        }
        uploadJson.put("LDYY_DM", StringUtil.isNotBlank(d146Code) ? d146Code : "");//党员流动原因代码 01：外出务工经商  02：外出居住  03：待业或未落实工作单位   04：未就业的毕业学生   05：自主择业的退伍军人 06：离退休人员党员异地居住  09：其他
        uploadJson.put("WCRQ", ObjectUtil.isNotNull(memFlowSign.getOutDate()) ? DateUtil.format(memFlowSign.getOutDate(), "yyyy-MM-dd") : "");//外出日期 例如 2024-10-10
        uploadJson.put("WCDDBCSM", StringUtil.isNotBlank(memFlowSign.getOutInstructions()) ? memFlowSign.getOutInstructions() : "暂无说明");//外出日期补充说明
        // TODO: 2025/2/20 这个日期只能是年月
        uploadJson.put("DFJZRQLCD", ObjectUtil.isNotNull(memFlowSign.getGhanaDate()) ? DateUtil.format(memFlowSign.getGhanaDate(), "yyyy-MM") : "");//党费缴至日期流出地 例如 2024-10-10
        // TODO: 2025/2/20 张娜：流动党员流动提醒里，JS_ORGID里，必须是流入方上传到交换区的具有审批预备党员权限组织或者流入方成立流动党组织D01ID，流出地已经下载了的。
        uploadJson.put("JS_ORGID", StringUtil.isNotBlank(applyOrgFlowCode) ? applyOrgFlowCode : "");//接收支部ID
        //人员类别 1 离退休人员
        //2 未就业高校毕业生
        //3 新就业群体
        //31 快递员 32 外卖员 33 网约车司机 34 货车司机 35 网络主播 4 其他
        String flowMemTypeCode = memFlowSign.getFlowMemTypeCode();
        uploadJson.put("JYQTDM", StringUtil.isNotBlank(flowMemTypeCode) ? flowMemTypeCode : "");
        // 人员类别其他详情
        uploadJson.put("JYQTQTXQ", StringUtil.isNotBlank(memFlowSign.getFlowMemTypeRemark())?memFlowSign.getFlowMemTypeRemark():"");
        // 新就业群体其他详情
        uploadJson.put("XJYQTQTXQ", StringUtil.isNotBlank(memFlowSign.getFlowMemTypeNewRemark())?memFlowSign.getFlowMemTypeNewRemark():"");
        // 流入地是否农民工
        uploadJson.put("LRDSFNMG", StrUtil.equals(memFlowSign.getLrdIsFarmer(), "1") ? "1" : "0");

        JSONObject jsonData = new JSONObject();
        jsonData.put("accessID", CommonConstant.XZQH_GUIZHOU);//本省根节点
        jsonData.put("dataType", "6");//传输的类型

        System.out.println("uniqueKey=======>" + uniqueKey);
        jsonData.put("uniqueKey", uniqueKey);//本流动信息唯一码
        String[] sm2Keys = CryptoUtil.getSm2Keys(CryptoUtil.CERT_ID);
        jsonData.put("secretKey", sm2Keys[1]);//随机码,sm2加密后的
        JSONObject infoObject = new JSONObject();
        infoObject.put("LDTX", uploadJson);
        String infoJsonString = infoObject.toJSONString();
        // jsonData.put("info",infoObject);
        // 加密
        jsonData.put("info", CryptoUtil.sm4Encrypt(infoJsonString, sm2Keys[0]));

        log.info("上传流动提醒结构数据：{}", infoJsonString);
        try {
            String resultJson = memFlow1Service.uploadLDDY(jsonData, false);
            log.info("上传流动提醒结果数据:{}", resultJson);
            jsonData.put("info", infoJsonString);
            memFlow1Service.deailWithUploadData(resultJson, code, uniqueKey, 1002, jsonData.toJSONString());
            LambdaUpdateWrapper<MemFlowSign> updateWrapper = new UpdateWrapper<MemFlowSign>().lambda().set(MemFlowSign::getFlowUpCode, uniqueKey).eq(MemFlowSign::getCode, code);
            iMemFlowSignService.update(updateWrapper);
        } catch (Exception e) {
            log.error("上传流动提醒失败", e);
            jsonData.put("info", infoJsonString);
            memFlow1Service.catchDeailData(code, uniqueKey, 1002, jsonData.toJSONString());
            MemFlowSign updateMemFlow = new MemFlowSign();
            updateMemFlow.setCode(code);
            updateMemFlow.setFlowUpCode(uniqueKey);
            iMemFlowSignService.updateById(updateMemFlow);
        }
        return true;
    }

    @Override
    public boolean downloadRemindCheck() throws Exception {
        JSONObject jsonObject = new JSONObject();
        String dataType = "30";
        String type = "2";
        jsonObject.put("dataType", dataType);
        //请求（本地）系统接入标识
        jsonObject.put("type", type);
        //系统接入标识
        jsonObject.put("accessID", CommonConstant.XZQH_GUIZHOU);
        String result = memFlow1Service.downloadLDDY(jsonObject);
        JSONObject dataJson = JSONObject.parseObject(result);
        String realInfo = CryptoUtil.getRealInfo(dataJson.getString("info"), dataJson.getString("secretKey"));
        String contentId = this.saveDownloadContent("定时任务下载流动提醒审核数据：downloadRemindCheck", realInfo);
        log.info("开始执行流动党员下载流动提醒变更，请求数据查询到数据：：" + realInfo);
        if (!realInfo.equals("[]")) {
            JSONArray jsonArray = JSONArray.parseArray(realInfo);
            List<Map<String, String>> memFlowSignListMap = new ArrayList<>();
            List<String> flowUpCodeList = new ArrayList<>();
            Map<String, String> map = new HashMap<>();
            for (Object obj : jsonArray) {
                Map<String, String> memFlowSignMap = new HashMap<>();
                JSONObject objJson = (JSONObject) obj;
                JSONObject ldtxReturnJson = objJson.getJSONObject("LDTX");
                String ldtx0003 = ldtxReturnJson.getString("LDTX0003");
                String id = ldtxReturnJson.getString("ID");
                memFlowSignMap.put("flow_up_code", id);
                memFlowSignMap.put("LDTX0003", ldtx0003);// 流出地状态变更状态 1.办理中 2.已登记 3.已退回-党员不存在 4.已退回-党员组织关系转接中 5.已退回-党员信息流转中
                memFlowSignListMap.add(memFlowSignMap);
                flowUpCodeList.add(id);
                map.put(id, ldtx0003);
            }
            if (CollUtil.isNotEmpty(memFlowSignListMap)) {
                // // todo: tanmw 2025/2/25 10:26  处理我方流入登记的审批结果入库
                List<MemFlowSign> list = memFlowSignService.list(new LambdaQueryWrapper<MemFlowSign>().in(MemFlowSign::getFlowUpCode, flowUpCodeList));
                if (CollUtil.isNotEmpty(list)) {
                    list.forEach(s -> {
                        String status = map.get(s.getFlowUpCode());
                        //状态
                        String auditStatus = StringUtil.equals(CommonConstant.ONE, status) ? "0" : StringUtil.equals(CommonConstant.TWO, status) ? "1" : "2";
                        //拒绝理由
                        String refuse = StringUtil.equals(CommonConstant.TWO, auditStatus) ? StringUtil.equals(CommonConstant.THREE, status) ? "1" :
                                StringUtil.equals(CommonConstant.FOUR, status) ? "2" : "3" : null;
                        s.setStatus(auditStatus);
                        s.setRefuse(refuse);
                        s.setAuditTime(ObjectUtil.isNotNull(s.getAuditTime()) ? s.getAuditTime() : new Date());
                    });
                    memFlowSignService.updateBatchById(list);
                }
            }
            if (CollUtil.isNotEmpty(flowUpCodeList)) {
                zyMemFlowExchangeService.confirm(dataType, type, flowUpCodeList);
                this.updateDownloadContent(contentId, flowUpCodeList, null);
            }
            // 保存下载数据
        } else {
            log.info("开始执行流动党员下载流动提醒变更，请求数据未查询到数据：：" + realInfo);
        }
        return false;
    }

    @Override
    public boolean downloadOtherRemind() throws Exception {
        JSONObject jsonObject = new JSONObject();
        String dataType = "6";
        String type = "1";
        jsonObject.put("dataType", dataType);
        //请求（本地）系统接入标识
        jsonObject.put("type", type);
        //系统接入标识
        jsonObject.put("accessID", CommonConstant.XZQH_GUIZHOU);

        String result = memFlow1Service.downloadLDDY(jsonObject);
        JSONObject dataJson = JSONObject.parseObject(result);
        String realInfo = CryptoUtil.getRealInfo(dataJson.getString("info"), dataJson.getString("secretKey"));
        String contentId = this.saveDownloadContent("定时任务下载省外流动提醒数据：downloadOtherRemind", realInfo);
        log.info("开始执行下载外省流动提醒数据，请求数据查询到数据：：" + realInfo);
        if (!realInfo.equals("[]")) {
            JSONArray jsonArray = JSONArray.parseArray(realInfo);
            List<MemFlowSign> memFlowSignList = new ArrayList<>();
            List<String> flowUpCodeList = new ArrayList<>();
            Map<String, String> uniqueKeyNodeMap = new HashMap<>();
            for (Object obj : jsonArray) {
                JSONObject objJson = (JSONObject) obj;
                JSONObject ldtxReturnJson = objJson.getJSONObject("LDTX");
                MemFlowSign memFlowSign = new MemFlowSign();
                String id = ldtxReturnJson.getString("ID");// 流动提醒ID
                memFlowSign.setFlowUpCode(id);
                // todo: tanmw 2025/2/25 11:26  json数据结构参照05 文档 5.23
                memFlowSign.setId(id);
                memFlowSign.setCode(id);
                flowUpCodeList.add(id);
                memFlowSign.setName(ldtxReturnJson.getString("LDTX0003"));
                memFlowSign.setIdcard(ldtxReturnJson.getString("LDTX0004"));
                memFlowSign.setOrgFlowCode(ldtxReturnJson.getString("LDTX0006"));
                List<OrgExchangeArea> one = orgExchangeAreaMapper.selectList(new LambdaQueryWrapper<OrgExchangeArea>().eq(OrgExchangeArea::getCode, memFlowSign.getOrgFlowCode()));
                memFlowSign.setOrgFlowLevelCode(CollUtil.isNotEmpty(one) ? one.get(0).getOrgCode() : "");
                memFlowSign.setOrgFlowName(ldtxReturnJson.getString("LDTX0007"));
                String ldtx0005 = ldtxReturnJson.getString("LDTX0005");
                if (StrUtil.isNotEmpty(ldtx0005)) {
                    //这里要注意可能是流动党员党组织，也可能是普通支部
                    OrgExchangeArea orgByCode = orgExchangeAreaMapper.findOrgByCode(ldtx0005);
                    if (ObjectUtil.isNotNull(orgByCode)) {
                        String orgCode = orgByCode.getOrgCode();
                        memFlowSign.setApplyOrgFlowLevelCode(orgCode);
                        memFlowSign.setApplyOrgFlowName(orgByCode.getName());
                    } else {
                        memFlowSign.setApplyOrgFlowLevelCode("未查询到层级码");
                        memFlowSign.setApplyOrgFlowName("未查询到组织名称");
                    }

                }
                memFlowSign.setApplyOrgFlowCode(ldtx0005);
                memFlowSign.setFlowConnectionName(ldtxReturnJson.getString("LRDLXR"));
                memFlowSign.setFlowConnection(ldtxReturnJson.getString("LRDLXFS"));
                memFlowSign.setFrequentlyPhone(ldtxReturnJson.getString("CYSJHM"));
                memFlowSign.setD146Code(ldtxReturnJson.getString("LDYY_DM"));
                String wcrq = ldtxReturnJson.getString("WCRQ");
                if (StrUtil.isNotEmpty(wcrq)) {
                    memFlowSign.setOutDate(DateUtil.parse(wcrq, "yyyy-MM-dd"));
                } else {
                    memFlowSign.setOutDate(new Date());
                }
                memFlowSign.setOutInstructions(ldtxReturnJson.getString("LDYYXQ"));
                String dfjzrqlcd = ldtxReturnJson.getString("DFJZRQLCD");
                if (!StrUtil.isEmpty(dfjzrqlcd)) {
                    memFlowSign.setGhanaDate(DateUtil.parse(dfjzrqlcd, "yyyy-MM-dd"));
                } else {
                    memFlowSign.setGhanaDate(new Date());
                }
                memFlowSign.setDataType("2");
                memFlowSign.setCreateTime(new Date());
                memFlowSign.setOrgConnection(ldtxReturnJson.getString("LDTX0010"));
                memFlowSign.setOrgConnectionName(ldtxReturnJson.getString("LDTX0009"));
                memFlowSign.setSendNodeCode(ldtxReturnJson.getString("LDTX0002"));
                memFlowSign.setReceiveNodeCode(ldtxReturnJson.getString("LDTX0001"));
                memFlowSign.setFlowViewWarn("0");
                memFlowSign.setStatus("0");
                memFlowSign.setRemark(ldtxReturnJson.toJSONString());

                memFlowSign.setFlowMemTypeCode(ldtxReturnJson.getString("JYQTDM"));
                memFlowSign.setLrdIsFarmer(ldtxReturnJson.getString("LRDSFNMG"));
                memFlowSign.setFlowMemTypeRemark(ldtxReturnJson.getString("JYQTQTXQ"));
                memFlowSign.setFlowMemTypeNewRemark(ldtxReturnJson.getString("XJYQTQTXQ"));

                memFlowSignList.add(memFlowSign);
                uniqueKeyNodeMap.put(id, memFlowSign.getReceiveNodeCode());
            }
            if (CollUtil.isNotEmpty(memFlowSignList)) {
                iMemFlowSignService.saveOrUpdateBatch(memFlowSignList);
            }
            zyMemFlowExchangeService.confirm(dataType, type, flowUpCodeList);
            // 查看上传
            this.uploadRemindRead(uniqueKeyNodeMap);
            this.updateDownloadContent(contentId, flowUpCodeList, null);
        } else {
            log.info("开始执行下载外省流动提醒数据，请求数据未查询到数据：：" + realInfo);
        }
        return false;
    }

    @Override
    public boolean uploadOtherRemindCheck(String flowCode, String flowUpCode, String LDTX0001, String LDTX0003) throws Exception {
        JSONObject jsonData = new JSONObject();
        jsonData.put("accessID", CommonConstant.XZQH_GUIZHOU);//本省根节点
        jsonData.put("dataType", "30");//传输的类型
        jsonData.put("uniqueKey", flowUpCode);//本流动信息唯一码
        String[] sm2Keys = CryptoUtil.getSm2Keys(CryptoUtil.CERT_ID);
        jsonData.put("secretKey", sm2Keys[1]);//随机码,sm2加密后的
        JSONObject infoObject = new JSONObject();
        JSONObject uploadData = new JSONObject();
        uploadData.put("LDTX0001", LDTX0001);
        uploadData.put("LDTX0003", LDTX0003);
        infoObject.put("LDTX", uploadData);
        String infoJsonString = infoObject.toJSONString();
        // 加密
        jsonData.put("info", CryptoUtil.sm4Encrypt(infoJsonString, sm2Keys[0]));

        log.info("上传流动提醒结构数据：{}", infoJsonString);
        try {
            String resultJson = memFlow1Service.uploadLDDY(jsonData, false);
            log.info("上传流动提醒结果数据:{}", resultJson);
            jsonData.put("info", infoJsonString);
            memFlow1Service.deailWithUploadData(resultJson, flowCode, flowUpCode, 1003, jsonData.toJSONString());
        } catch (Exception e) {
            log.error("上传流动提醒失败", e);
            jsonData.put("info", infoJsonString);
            memFlow1Service.catchDeailData(flowCode, flowUpCode, 1003, jsonData.toJSONString());
        }
        return true;
    }

    @Override
    public void uploadRemindRead(Map<String, String> uniqueKeyNodeMap) {
        log.info("上传流动提醒是否查看：{}", JSONUtil.toJsonStr(uniqueKeyNodeMap));
        uniqueKeyNodeMap.forEach((k, v) -> {
            JSONObject jsonData = new JSONObject();
            jsonData.put("accessID", CommonConstant.XZQH_GUIZHOU);//本省根节点
            jsonData.put("dataType", "34");//传输的类型
            jsonData.put("uniqueKey", k);//本流动信息唯一码
            String[] sm2Keys = CryptoUtil.getSm2Keys(CryptoUtil.CERT_ID);
            jsonData.put("secretKey", sm2Keys[1]);//随机码,sm2加密后的
            JSONObject infoObject = new JSONObject();
            JSONObject uploadData = new JSONObject();
            uploadData.put("LDTX0001", v);
            uploadData.put("LCDCK", "1");
            infoObject.put("LDTX", uploadData);
            String infoJsonString = infoObject.toJSONString();
            // 加密
            jsonData.put("info", CryptoUtil.sm4Encrypt(infoJsonString, sm2Keys[0]));
            log.info("上传流动提醒是否查看：{}", infoJsonString);
            try {
                String resultJson = memFlow1Service.uploadLDDY(jsonData, false);
                log.info("上传流动提醒是否查看:{}", resultJson);
                jsonData.put("info", infoJsonString);
                memFlow1Service.deailWithUploadData(resultJson, k, k, 1004, jsonData.toJSONString());
            } catch (Exception e) {
                log.error("上传流动提醒是否查看失败", e);
                jsonData.put("info", infoJsonString);
                memFlow1Service.catchDeailData(k, k, 1004, jsonData.toJSONString());
            }
        });
    }

    @Override
    public void updateDownloadContent(String contentId, List<String> flowUpCodeList, List<String> flowCodeList) {
        try {
            FlowContent flowContent = flowContentMapper.selectById(contentId);
            if (Objects.isNull(flowContent)) {
                return;
            }
            if (CollUtil.isNotEmpty(flowCodeList)) {
                flowContent.setFlowCode(JSONUtil.toJsonStr(flowCodeList));
            }
            if (CollUtil.isNotEmpty(flowUpCodeList)) {
                flowContent.setFlowUqCode(JSONUtil.toJsonStr(flowUpCodeList));
            }
            flowContentMapper.updateById(flowContent);
        } catch (Exception e) {
            log.error("保存下载交换区数据错误", e);
        }
    }

    @Override
    public String saveDownloadContent(String dataType, String content) {
        String contentId = StrKit.getRandomUUID();
        try {
            FlowContent flowContent = new FlowContent();
            // String contentId = StrKit.getRandomUUID();
            flowContent.setId(contentId);
            flowContent.setContent(content);
            flowContent.setCreateTime(new Date());
            flowContent.setDataType(dataType);
            flowContentMapper.insert(flowContent);
        } catch (Exception e) {
            log.error("保存下载交换区数据错误", e);
        }
        return contentId;
    }

    /**
     * 改变同步方式
     *
     * @param memFlowList
     */
    private void changeIdentity(List<MemFlow> memFlowList) {
        memFlowList.forEach(memFlow -> memFlow.setHasSync(CommonConstant.ONE_INT));
        updateBatchById(memFlowList);
    }

    /**
     * 递归寻找具有预备党员审批权限的党组hi
     */
    public OrgExchangeArea findApproveOrg(String code) {
        OrgExchangeArea orgByCode = orgExchangeAreaMapper.findOrgByCode(code);
        Integer isApprovalMem = orgByCode.getIsApprovalMem();
        if (isApprovalMem.equals(CommonConstant.ONE_INT)) {
            return orgByCode;
        } else {
            return findApproveOrg(orgByCode.getParentCode());
        }
    }

    //流动提醒数据111
    public void liudongtix() throws Exception {
        LddyWebServiceImplService lddyWebServiceImplService = new LddyWebServiceImplService();
        LddyWebServiceImpl lddyWebServiceImplPort = lddyWebServiceImplService.getLddyWebServiceImplPort();
        cn.hutool.json.JSONObject jsonObject = new cn.hutool.json.JSONObject();
        jsonObject.put("dataType", "6");
        //请求（本地）系统接入标识
        jsonObject.put("type", "1");
        //系统接入标识
        jsonObject.put("accessID", "052000000001");
        Map<String, String> stringMap = CryptoUtil.signData(jsonObject.toString(), "1");
        String signData = stringMap.get("signData");
        String data = stringMap.get("data");
        String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
        String unicode = cnToUnicode(qianMingData);
        String resultStr = null;
        resultStr = lddyWebServiceImplPort.download(unicode);
        String s = new String(Base64Utils.decode(resultStr.getBytes()), StandardCharsets.UTF_8);
        JSONObject dataJson = JSONObject.parseObject(s);
        if (StrUtil.isNotEmpty(dataJson.getString("secretKey")) && StrUtil.isNotEmpty(dataJson.getString("info"))) {
            String realInfo = CryptoUtil.getRealInfo(dataJson.getString("info"), dataJson.getString("secretKey"));
            System.out.println("流动提醒接收数据====>" + realInfo);
        }
        //[{"LDTX":{"LDTX0008":"153212123","LDTX0009":"","LDYY_DM":"","WCRQ":"2025-01-14","LDTX0006":"********************************","LDTX0007":"中共重庆市大渡口区九宫庙街道锦霞社区委员会","LDTX0004":"510213194404172812","LDTX0005":"278C38935DA442C1BA7DD252625247A3","LDYYXQ":"","WCDDBCSM":"","JS_ORGID":"278C38935DA442C1BA7DD252625247A3","CYSJHM":"","DFJZRQLCD":"2025-01-01","LDTX0002":"050026075658","LDTX0003":"测试","LDTX0011":"2","ID":"1234567891012121","LDTX0001":"052000000001","LRDLXR":"","LCDCK":"0","LDTX0010":"","LRDLXFS":""}},{"LDTX":{"LDTX0008":"15110071072","LDTX0006":"f8db37c804ed47699bb42b76eb65f96b","LDTX0007":"江干党委","LDTX0004":"420527200211231117","LDTX0005":"857e2bc16877432ebd07248d77531b37","LDTX0002":"033000000001","LDTX0003":"Zzz","ID":"99957aaa35d54b2e9084c37bcb3314b1","LDTX0001":"052000000001","LCDCK":"0"}},{"LDTX":{"LDTX0008":"","LDTX0009":"元和街道","LDYY_DM":"01","WCRQ":"2025-02-20","LDTX0006":"a171a9e73d5e491fbddb4bc334e1968a","LDTX0007":"中共苏州市相城区元和街道委员会","LDTX0004":"522225199309118473","LDTX0005":"500c04c0a6ef4db7b3b56e5782cad70f","LDYYXQ":"","WCDDBCSM":"联调测试","JS_ORGID":"094d0da0c5634be4b6e9c84d0e08befb","CYSJHM":"13012320661","LDTX0002":"032000000001","LDTX0003":"安勇军","LDTX0011":"1","ID":"b03e0fbeca7343389c183a85bfc5824d","LDTX0001":"052000000001","LRDLXR":"111","LCDCK":"0","LDTX0010":"13211111111","LRDLXFS":"13211111111"}},{"LDTX":{"LDTX0008":"","LDTX0009":"李亮","LDYY_DM":"01","WCRQ":"2024-12-18","LDTX0006":"3692730213524835009","LDTX0007":"莲都区党委四","LDTX0004":"130424199209232123","LDTX0005":"fe866db0c6394fe0bd695143b955a8f6","LDYYXQ":"","WCDDBCSM":"123","JS_ORGID":"360B4754866648BC847086F801D8B90D","CYSJHM":"13241563520","DFJZRQLCD":"2024-12-01","LDTX0002":"037000000001","LDTX0003":"cc","LDTX0011":"1","ID":"d8314e81c38c4caab7344a54b4b56111","LDTX0001":"052000000001","LRDLXR":"流入地联系人4","LCDCK":"0","LDTX0010":"0851-83227189","LRDLXFS":"13831888888"}},{"LDTX":{"LDTX0008":"","LDTX0009":"李亮","LDYY_DM":"01","WCRQ":"2024-12-18","LDTX0006":"3692730213524835009","LDTX0007":"莲都区党委四","LDTX0004":"130424199209232123","LDTX0005":"fe866db0c6394fe0bd695143b955a8f6","LDYYXQ":"","WCDDBCSM":"123","JS_ORGID":"360B4754866648BC847086F801D8B90D","CYSJHM":"13241563520","DFJZRQLCD":"2024-12-01","LDTX0002":"037000000001","LDTX0003":"cc","LDTX0011":"1","ID":"d8314e81c38c4caab7344a54b4b56333","LDTX0001":"052000000001","LRDLXR":"流入地联系人4","LCDCK":"0","LDTX0010":"0851-83227189","LRDLXFS":"13831888888"}}]
    }

    //流出提醒222
    public void liuchutixing() throws Exception {
        String serialNumber = this.getSerialNumber();
        String uniqueKey = CryptoUtil.getJSX("052000012223" + DateUtil.format(new Date(), "yyyyMMdd") + serialNumber);
        JSONObject uploadJson = new JSONObject();
        uploadJson.put("LDTX0001", "032000000001");//接收提醒根节点代码
        uploadJson.put("LDTX0002", "052000000001");//发送提醒根节点代码
        uploadJson.put("LDTX0003", "杨海");//姓名
        uploadJson.put("LDTX0004", "522228198203061215");//身份证号码
        uploadJson.put("LDTX0005", "a171a9e73d5e491fbddb4bc334e1968a");//流出地的党组织ID
        // TODO: 2025/2/20 也是 D01ID，并且是交换区上面有得才行
        uploadJson.put("LDTX0006", "23BE6A52D7F745D391EA39D721859FFA");//流入地党组织ID
        // TODO: 2025/2/20 这个名称可能会超长
        uploadJson.put("LDTX0007", "贵州支部委员会");//流入地党组织名称
        uploadJson.put("uniqueKey", uniqueKey);//流动提醒ID
        uploadJson.put("LDTX0008", "13111111111");//联系电话
        uploadJson.put("LDTX0009", "测试");//党组织联系人
        uploadJson.put("LDTX0010", "13811111110");//党组织联系电话
        // TODO: 2025/2/20 这个值，初始上传就得是2
        uploadJson.put("LDTX0011", "1");//流动登记状态  1.办理中 2.已登记 3.已退回-党员不存在 4.已退回-党员组织关系转接中 5.已退回-党员信息流转中
        uploadJson.put("LRDLXR", "何军");//流入地联系人
        uploadJson.put("LRDLXFS", "18848500161");//流入地联系方式
        uploadJson.put("LCDCK", "0");//流出地是否查看提醒 0 否
        uploadJson.put("CYSJHM", "18848500161");//提醒常用手机号码
        uploadJson.put("LDYY_DM", "09");//党员流动原因代码 01：外出务工经商  02：外出居住  03：待业或未落实工作单位   04：未就业的毕业学生   05：自主择业的退伍军人 06：离退休人员党员异地居住  09：其他
        uploadJson.put("LDYYXQ", "外出");//其他流动原因详情
        uploadJson.put("WCRQ", "2025-02-19");//外出日期 例如 2024-10-10
        uploadJson.put("WCDDBCSM", "外出");//外出日期补充说明
        // TODO: 2025/2/20 这个日期只能是年月
        uploadJson.put("DFJZRQLCD", "2024-10");//党费缴至日期流出地 例如 2024-10-10
        // TODO: 2025/2/20 张娜：流动党员流动提醒里，JS_ORGID里，必须是流入方上传到交换区的具有审批预备党员权限组织或者流入方成立流动党组织D01ID，流出地已经下载了的。
        uploadJson.put("JS_ORGID", "23BE6A52D7F745D391EA39D721859FFA");//接收支部ID

        JSONObject jsonData = new JSONObject();
        jsonData.put("accessID", CommonConstant.XZQH_GUIZHOU);//本省根节点
        jsonData.put("dataType", "6");//传输的类型

        System.out.println("uniqueKey=======>" + uniqueKey);
        jsonData.put("uniqueKey", uniqueKey);//本流动信息唯一码
        String[] sm2Keys = CryptoUtil.getSm2Keys(CryptoUtil.CERT_ID);
        jsonData.put("secretKey", sm2Keys[1]);//随机码,sm2加密后的
        JSONObject infoObject = new JSONObject();
        infoObject.put("LDTX", uploadJson);
        jsonData.put("info", infoObject);
        System.out.println("上传的数据结构===>" + jsonData.toJSONString());
        // 加密
        jsonData.put("info", CryptoUtil.sm4Encrypt(infoObject.toJSONString(), sm2Keys[0]));
        LddyWebServiceImplService lddyWebServiceImplService = new LddyWebServiceImplService();
        LddyWebServiceImpl lddyWebServiceImplPort = lddyWebServiceImplService.getLddyWebServiceImplPort();
        Map<String, String> stringMap = CryptoUtil.signData(jsonData.toString(), "1");
        String signData = stringMap.get("signData");
        String data = stringMap.get("data");
        String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
        String unicode = cnToUnicode(qianMingData);
        String resultStr = lddyWebServiceImplPort.upload(unicode);

        String s = new String(Base64Utils.decode(resultStr.getBytes()), StandardCharsets.UTF_8);

        System.out.println("上传流出登记状态信息===>" + s);
    }

    public String getSerialNumber() {
        StringBuilder sb = new StringBuilder();
        Long incrementStr = redisTemplate.opsForValue().increment(DateUtil.format(new Date(), "yyyyMMdd"), 6);
        String serialNumber = incrementStr.toString();
        if (CommonConstant.ONE.equals(serialNumber)) {
            Calendar calendar = Calendar.getInstance();
            //得到前一天
            calendar.add(Calendar.DATE, -1);
            Date date = calendar.getTime();
            redisTemplate.delete(DateUtil.format(date, "yyyyMMdd"));
        }
        //不足4位补0
        if (serialNumber.length() <= 4) {
            sb.append(String.format("%04d", incrementStr));
        } else {
            sb.append(incrementStr);
        }
        return sb.toString();
    }

    @Override
    public void receiveOneByCodeAndFlowUqCode(String flowCode, String flowUqCode) {
        MemFlow1 memFlow1 = memFlow1Service.findByCode(flowCode);
        if (Objects.isNull(memFlow1)) {
            return;
        }
        LambdaQueryWrapper<FlowUploadData> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(FlowUploadData::getFlowCode, flowCode);
        lambdaQueryWrapper.eq(FlowUploadData::getFlowUqCode, flowUqCode);
        lambdaQueryWrapper.eq(FlowUploadData::getIsSuccess, 0);
        lambdaQueryWrapper.eq(FlowUploadData::getBackCode, "100");
        lambdaQueryWrapper.orderByDesc(FlowUploadData::getCreateTime);
        lambdaQueryWrapper.last("limit 1");
        FlowUploadData flowUploadData = flowUploadMapper.selectOne(lambdaQueryWrapper);
        if (Objects.isNull(flowUploadData)) {
            return;
        }
        LambdaQueryWrapper<FlowContent> flowContentLambdaQueryWrapper = new LambdaQueryWrapper<>();
        flowContentLambdaQueryWrapper.like(FlowContent::getDataType, "根据根节点批量接收信息下载接口PullMemFlowStatusReceiveTask");
        flowContentLambdaQueryWrapper.like(FlowContent::getContent, flowUqCode);
        flowContentLambdaQueryWrapper.last("limit 1");
        FlowContent flowContent = flowContentMapper.selectOne(flowContentLambdaQueryWrapper);
        if (Objects.isNull(flowContent)) {
            return;
        }
        String content = flowContent.getContent();
        JSONArray jsonArray = JSONArray.parseArray(content);
        for (Object object : jsonArray) {
            JSONObject jsonObject = (JSONObject) object;
            String uniqueKey = jsonObject.getString("uniqueKey");
            if (uniqueKey.equals(flowUqCode)) {
                JSONObject lddj = jsonObject.getJSONObject("LDDJ");
                this.updateMemFlowByFlowUqCode(memFlow1, flowUqCode, lddj);
                break;
            }
        }
    }

    /**
     * select * from mem_flow where code in(SELECT mem_flow.code
     * FROM mem_flow
     * WHERE mem_flow.is_province = '0'
     * AND mem_flow.flow_step = '1'
     * and mem_flow.flow_uq_code like '052%'
     * AND EXISTS (
     * SELECT 1
     * FROM flow_content
     * WHERE flow_content.date_type LIKE '%根据根节点批量接收信息下载接口PullMemFlowStatusReceiveTask%'
     * AND flow_content.flow_uq_code LIKE CONCAT('%', mem_flow.flow_uq_code, '%')
     * )
     * ORDER BY mem_flow.create_time DESC)
     *
     * @param flowCodeList
     */
    @Override
    public void receiveOneByCodeAndFlowUqCodeList(List<String> flowCodeList) {
        if (CollUtil.isEmpty(flowCodeList)) {
            return;
        }
        for (String flowCode : flowCodeList) {
            MemFlow1 memFlow1 = memFlow1Service.getById(flowCode);
            try {
                this.receiveOneByCodeAndFlowUqCodeTwo(memFlow1.getCode(), memFlow1.getFlowUqCode());
            } catch (Exception e) {
                log.error("开始处理异常数据接收单个人员异常", e);
            }
        }
    }

    @Override
    public void uploadByCodeList(List<String> flowCodeList) {
        if (CollUtil.isEmpty(flowCodeList)) {
            return;
        }
        for (String flowCode : flowCodeList) {
            MemFlow1 memFlow1 = memFlow1Service.getById(flowCode);
            String flowUqCode = memFlow1.getFlowUqCode();
            // 首先判断是否是否存在
            boolean exist = this.checkExchangeExist(flowUqCode);
            log.info("流动党员{}-{}-{}-是否在交换区存在：{}", memFlow1.getMemName(), memFlow1.getCode(), memFlow1.getFlowUqCode(), exist);
            if (exist) {
                continue;
            }
            MemFlow1 otherMemFLow = memFlow1Service.findOtherUqCode(flowUqCode, flowCode);
            if (Objects.nonNull(otherMemFLow)) {
                // 重新生成唯一码
                flowUqCode = this.reCreateFlowUqCode(flowUqCode);
                memFlow1.setFlowUqCode(flowUqCode);
                memFlow1.setRemark("{\"msg\":\"唯一码重复：" + otherMemFLow.getFlowUqCode() + "，数据未上传成功！\",\"code\":\"9997\"}");
                memFlow1Service.updateById(memFlow1);
            }


            JSONObject uploadMemFlowData = memFlow1Service.createUploadMemFlowData(memFlow1);

            log.info("上传流动党员结构数据：{}", uploadMemFlowData.toJSONString());
            String infoUpload = uploadMemFlowData.getString("infoUpload");
            try {
                // TODO: 2022/11/18 反馈状态码不是成功，需要注意重新推送问题
                uploadMemFlowData.remove("infoUpload");
                String resultJson = memFlow1Service.uploadLDDY(uploadMemFlowData, false);
                log.info("上传流动党员结果数据:{}-{}-{}", memFlow1.getMemName(), flowUqCode, resultJson);
                uploadMemFlowData.put("info", infoUpload);
                memFlow1Service.deailWithUploadData(resultJson, flowCode, flowUqCode, CommonConstant.ONE_INT, uploadMemFlowData.toJSONString());
                LambdaUpdateWrapper<MemFlow1> updateWrapper = new UpdateWrapper<MemFlow1>().lambda().set(MemFlow1::getFlowUqCode, flowUqCode).set(MemFlow1::getRemark, resultJson)
                        .eq(MemFlow1::getCode, flowCode);
                memFlow1Service.update(updateWrapper);
            } catch (Exception e) {
                log.error("修复流动党员唯一标识为空未上传问题异常" + memFlow1.getMemName() + "-" + memFlow1.getFlowUqCode(), e);
                uploadMemFlowData.put("info", infoUpload);
                memFlow1Service.catchDeailData(flowCode, flowUqCode, CommonConstant.ONE_INT, uploadMemFlowData.toJSONString());
                MemFlow1 updateMemFlow = new MemFlow1();
                updateMemFlow.setCode(flowCode);
                updateMemFlow.setFlowUqCode(flowUqCode);
                memFlow1Service.updateById(updateMemFlow);
            }

        }
    }

    public String reCreateFlowUqCode(String flowUqCode) {
        log.info("定时任务修复生成唯一码重复{}，重新计算唯一码", flowUqCode);
        // 查找最大的flowCode
        String yyyyMMdd = flowUqCode.substring(0, 20);
        String nextUniqueCode = memFlow1Service.generateNextUniqueCodeByDate(yyyyMMdd);
        log.info("生成唯一码重复，重新计算唯一码:{}", nextUniqueCode);
        return nextUniqueCode;
    }

    private boolean checkExchangeExist(String flowUqCode) {
        try {
            JSONObject jsonData = new JSONObject();
            jsonData.put("accessID", CommonConstant.XZQH_GUIZHOU);//本省根节点
            jsonData.put("dataType", "2");
            jsonData.put("type", "1");
            jsonData.put("uniqueKey", flowUqCode);
            String resultJson = memFlow1Service.downloadLDDY(jsonData);
            log.info("检查交换区是否存在流动记录{},结果数据：{}", flowUqCode, resultJson);
            JSONObject jsonObject = JSONObject.parseObject(resultJson);
            String code = jsonObject.getString("code");
            if (StrUtil.equals(code, "00")) {
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("检查交换区是否存在流动记录异常", e);
            return false;
        }
    }


    @Override
    public void receiveOneByCodeAndFlowUqCodeTwo(String flowCode, String flowUqCode) {
        MemFlow1 memFlow1 = memFlow1Service.findByCode(flowCode);
        if (Objects.isNull(memFlow1)) {
            return;
        }
        LambdaQueryWrapper<FlowContent> flowContentLambdaQueryWrapper = new LambdaQueryWrapper<>();
        flowContentLambdaQueryWrapper.like(FlowContent::getDataType, "根据根节点批量接收信息下载接口PullMemFlowStatusReceiveTask");
        flowContentLambdaQueryWrapper.like(FlowContent::getContent, flowUqCode);
        flowContentLambdaQueryWrapper.last("limit 1");
        FlowContent flowContent = flowContentMapper.selectOne(flowContentLambdaQueryWrapper);
        if (Objects.isNull(flowContent)) {
            return;
        }
        String content = flowContent.getContent();
        JSONArray jsonArray = JSONArray.parseArray(content);
        for (Object object : jsonArray) {
            JSONObject jsonObject = (JSONObject) object;
            String uniqueKey = jsonObject.getString("uniqueKey");
            if (uniqueKey.equals(flowUqCode)) {
                JSONObject lddj = jsonObject.getJSONObject("LDDJ");
                this.updateMemFlowByFlowUqCode(memFlow1, flowUqCode, lddj);
                break;
            }
        }
    }

    private boolean updateMemFlowByFlowUqCode(MemFlow1 memFlow, String flowUqCode, JSONObject jsonObject) {
        try {
            String lddj0015 = jsonObject.getString("LDDJ0015");
            memFlow.setInOrgPhone(StrUtil.isBlank(lddj0015) ? "" : lddj0015);
            jsonObject.getString("LDDJ0014");//流入地党支部书记
            String lddj0024 = jsonObject.getString("LDDJ0024");
            memFlow.setInOrgCode(StrUtil.isBlank(lddj0024) ? "" : lddj0024);
            String lddj0013 = jsonObject.getString("LDDJ0013");
            memFlow.setInMemD09Code(StrUtil.isBlank(lddj0013) ? "" : lddj0013);
            memFlow.setInMemD09Name(StrUtil.isBlank(lddj0013) ? "" : lddj0013);
            String lddj0017 = jsonObject.getString("LDDJ0017");
            //10:已登记 20 已接收 30 被退回 40 已流回 50 被终止 70 已撤销
            // 1已流出（未纳入管理） 2已纳入支部管理 3流出被退回 4流出终止 5流动完成 6撤销流出
            if (lddj0017.equals(CommonConstant.TWENTY)) {
                memFlow.setFlowStep(CommonConstant.TWO);
                Date lddj0027 = jsonObject.getDate("LDDJ0027");
                memFlow.setInReceivingTime(ObjectUtil.isNull(lddj0027) ? new Date() : lddj0027);
            }
            if (lddj0017.equals(CommonConstant.THIRTY)) {
                memFlow.setFlowStep(CommonConstant.THREE);
            }
            if (lddj0017.equals(CommonConstant.FOURTY)) {
                memFlow.setFlowStep(CommonConstant.FIVE);
                memFlow.setHasFlowBack(CommonConstant.ONE);
                Date lddj0028 = jsonObject.getDate("LDDJ0028");
                memFlow.setFlowBackTime(ObjectUtil.isNull(lddj0028) ? new Date() : lddj0028);
            }
            if (lddj0017.equals(CommonConstant.FIVETY)) {
                memFlow.setFlowStep(CommonConstant.FOUR);
            }
            String ldbj0002 = jsonObject.getString("LDBJ0002");
            if (StrUtil.isNotBlank(ldbj0002)) {
                memFlow.setInOrgLifeCode(ldbj0002);
                if (ldbj0002.equals(CommonConstant.ONE)) {
                    memFlow.setInOrgLife("正常参加线下组织生活");
                }
                if (ldbj0002.equals(CommonConstant.TWO)) {
                    memFlow.setInOrgLife("正常参加线上组织生活，半年参加一次线下组织生活");
                }
                if (ldbj0002.equals(CommonConstant.THREE)) {
                    memFlow.setInOrgLife("未正常参加组织生活");
                }
            }
            String ldbj0003 = jsonObject.getString("LDBJ0003");
            memFlow.setInFeedback(StrUtil.isBlank(ldbj0003) ? "" : ldbj0003);
            String ldbj0001 = jsonObject.getString("LDBJ0001");
            memFlow.setPartyExpensesInTime(StrUtil.isBlank(ldbj0001) ? new Date() : new SimpleDateFormat("yyyy-mm").parse(ldbj0001));
            String lddj0012 = jsonObject.getString("LDDJ0012");
            memFlow.setInOrgName(StrUtil.isBlank(lddj0012) ? "" : lddj0012);
            memFlow.setFlowIn(CommonConstant.ONE);
            memFlow.setUpdateTime(new Date());
            return memFlow1Service.updateById(memFlow);
        } catch (Exception e) {
            log.error("执行==根据根节点批量接收信息下载接口,处理数据异常，{}", flowUqCode, e);
        }
        return false;
    }

    @Override
    public void uploadAccept(List<String> flowCodeList) {
        if (CollUtil.isEmpty(flowCodeList)) {
            return;
        }
        for (String flowCode : flowCodeList) {
            MemFlow1 dbMemFlowNow = memFlow1Service.getById(flowCode);

            JSONObject pushReceiveData = new JSONObject();
            pushReceiveData.put("dataType", CommonConstant.THREE);
            pushReceiveData.put("accessID", CommonConstant.XZQH_GUIZHOU);
            pushReceiveData.put("uniqueKey", dbMemFlowNow.getFlowUqCode());
            String[] sm2Keys = CryptoUtil.getSm2Keys(CryptoUtil.CERT_ID);
            pushReceiveData.put("secretKey", sm2Keys[1]);//随机码,sm2加密后的
            JSONObject LDJSObject = new JSONObject();
            LDJSObject.put("LDDJ0012", StrUtil.isEmpty(dbMemFlowNow.getInOrgName()) ? "" : dbMemFlowNow.getInOrgName());//流入地党支部名称
            LDJSObject.put("LDDJ0013", StrUtil.isEmpty(dbMemFlowNow.getInMemD09Code()) ? "" : dbMemFlowNow.getInMemD09Code());//现从事行业
            OrgExchangeArea orgData = orgExchangeAreaMapper.findOrgByCode(dbMemFlowNow.getInOrgCode());
            if (ObjectUtil.isNotNull(orgData)) {
                LDJSObject.put("LDDJ0014", StrUtil.isNotBlank(orgData.getContacter()) ? orgData.getContacter() : "书记");//流入地党支部书记
            } else {
                LDJSObject.put("LDDJ0014", "书记");//流入地党支部书记
            }

            LDJSObject.put("LDDJ0015", StrUtil.isEmpty(dbMemFlowNow.getInOrgPhone()) ? "联系方式" : dbMemFlowNow.getInOrgPhone());//流入地党支部联系方式
            LDJSObject.put("LDDJ0017", CommonConstant.TWENTY);//流动状态
            LDJSObject.put("LDDJ0024", StrUtil.isEmpty(dbMemFlowNow.getInOrgCode()) ? "" : dbMemFlowNow.getInOrgCode());//流入地党支部标志
            LDJSObject.put("LDDJ0027", ObjectUtil.isEmpty(dbMemFlowNow.getInReceivingTime()) ? "" : DateUtil.format(dbMemFlowNow.getInReceivingTime(), "yyyy-MM-dd"));//流动党员接收日期
            JSONObject infoReceiveJSON = new JSONObject();
            infoReceiveJSON.put("LDJS", LDJSObject);
            // 添加办理过程
            JSONArray blgcJsonArray = memFlow1Service.createMemFlowBlgc(dbMemFlowNow, LDJSObject.getString("LDDJ0017"));
            infoReceiveJSON.put("BLGC", blgcJsonArray);
            String infoJsonString = infoReceiveJSON.toJSONString();
            log.info("info:{}", infoJsonString);
            pushReceiveData.put("info", CryptoUtil.sm4Encrypt(infoJsonString, sm2Keys[0]));
            // 记录未加密的数据结构
            pushReceiveData.put("infoUpload", infoJsonString);
            log.info("补传接收信息：{}", pushReceiveData);
            memFlow1Service.deailUploadData(dbMemFlowNow.getCode(), dbMemFlowNow.getFlowUqCode(), pushReceiveData);
        }
    }

    @Override
    public void checkStatus(String flowCode) {
        try {
//            log.info("单个检查数据状态:{}",flowCode);
            if (StrUtil.isBlank(flowCode)) {
                return;
            }

            MemFlow1 dbMemFlow = memFlow1Service.findByCode(flowCode);
            if (Objects.isNull(dbMemFlow) || StrUtil.isBlank(dbMemFlow.getFlowUqCode()) || StrUtil.startWith(dbMemFlow.getFlowUqCode(), "05200") || StrUtil.equalsAny(dbMemFlow.getFlowStep(), "3", "4", "5", "6")) {
                return;
            }

            String code = dbMemFlow.getCode();
            String flowUqCode = dbMemFlow.getFlowUqCode();
            Map<Integer, List<String>> checkMapNew = new HashMap<>();
            boolean aVoid2Boolen = this.aVoid2(flowUqCode, code);
            // 信息不存在不需要回传交换区
            if (aVoid2Boolen) {
                return;
            }
            boolean aVoid5Boolen = this.aVoid5(flowUqCode, code);
            //流出方结束流动
            if (aVoid5Boolen) {
                FlowUntil.putList(checkMapNew, flowUqCode, CommonConstant.FIVE_INT);
                //请求回传中组部数据
                try {
                    this.pushMap(checkMapNew);
                } catch (Exception e) {
                    log.error("开始执行流入的流动党员状态检查接口，请求回传中组部数据异常：{}：", flowCode, e);
                }
                return;
            }
            //编辑信息查询
            boolean aVoid8Boolen = this.aVoid8(flowUqCode, code);
            if (aVoid8Boolen) {
                FlowUntil.putList(checkMapNew, flowUqCode, CommonConstant.EIGHT_INT);
                //请求回传中组部数据
                try {
                    this.pushMap(checkMapNew);
                } catch (Exception e) {
                    log.error("开始执行流入的流动党员状态检查接口，请求回传中组部数据异常：{}：", flowCode, e);
                }
            }
        } catch (Exception e) {
            log.error("开始执行流入的流动党员状态检查接口，单个检查数据状态数据异常:{}：", flowCode, e);
        }
    }

    /**
     * dataType为2：检测流动信息是否存在，流出方撤销、终止流程信息，交换区修改信息状态，流入方如果查不到流动信息，则将本地流动信息终止；
     *
     * @param flowUqCode
     * @param flowCode
     * @throws Exception
     */
    public boolean aVoid2(String flowUqCode, String flowCode) throws Exception {
        JSONObject pushJson = new JSONObject();
        pushJson.put("type", CommonConstant.TWO);
        pushJson.put("accessID", CommonConstant.XZQH_GUIZHOU);
        pushJson.put("dataType", CommonConstant.TWO);
        pushJson.put("uniqueKey", flowUqCode);
        // String lddyData = memFlow1Service.downloadLDDY(pushJson);
        String lddyData = this.downloadLDDY(pushJson);
        JSONObject dataJson = JSONObject.parseObject(lddyData);
        JSONObject jsonObject = JSONObject.parseObject(lddyData);
        String code = jsonObject.getString("code");
        if (code.equals(CommonConstant.TWENTY_ONE)) {
            this.saveFlowContent(flowUqCode, flowCode, lddyData, "流入的流动党员状态检查aVoid2");
            MemFlow1 memFlow1 = new MemFlow1();
            memFlow1.setCode(flowCode);
            memFlow1.setFlowStep(CommonConstant.SIX);
            memFlow1.setCancelTime(new Date());
            log.info("dataType为2：检测流动信息是否存在，流出方撤销、终止流程信息，交换区修改信息状态，流入方如果查不到流动信息，则将本地流动信息终止：" + flowUqCode + "：" + dataJson);
            return memFlow1Service.updateById(memFlow1);
        }
        return false;
    }

    /**
     * dataType为5：流出方结束流动，由流入方下载信息；
     *
     * @param flowUqCode
     * @param flowCode
     * @throws Exception
     */
    public boolean aVoid5(String flowUqCode, String flowCode) throws Exception {
        JSONObject pushJson = new JSONObject();
        pushJson.put("type", CommonConstant.TWO);
        pushJson.put("accessID", CommonConstant.XZQH_GUIZHOU);
        pushJson.put("dataType", CommonConstant.FIVE);
        pushJson.put("uniqueKey", flowUqCode);
        // String lddyData = memFlow1Service.downloadLDDY(pushJson);
        String lddyData = this.downloadLDDY(pushJson);
        JSONObject dataJson = JSONObject.parseObject(lddyData);
        //解密请求数据
        if (StrUtil.isNotEmpty(dataJson.getString("secretKey")) && StrUtil.isNotEmpty(dataJson.getString("info"))) {
            String realInfo = CryptoUtil.getRealInfo(dataJson.getString("info"), dataJson.getString("secretKey"));
            this.saveFlowContent(flowUqCode, flowCode, realInfo, "流入的流动党员状态检查aVoid5");
            log.info("dataType为5：流出方结束流动，由流入方下载信息：" + flowUqCode + "：" + realInfo);
            JSONObject jsonObject = JSONObject.parseObject(realInfo);
            JSONObject lddj = jsonObject.getJSONObject("LDDJ");
            Date lddj0028 = lddj.getDate("LDDJ0028");
            MemFlow1 memFlow1 = new MemFlow1();
            memFlow1.setCode(flowCode);
            memFlow1.setFlowStep(CommonConstant.FIVE);
            memFlow1.setHasFlowBack(CommonConstant.ONE);
            memFlow1.setFlowBackTime(lddj0028);
            return memFlow1Service.updateById(memFlow1);
        }
        return false;
    }

    /**
     * 向中组部进行消息确认
     */
    public void pushMap(Map<Integer, List<String>> checkMap) throws Exception {
        ThreadUtil.execute(() -> {
            for (Map.Entry<Integer, List<String>> mapEntry : checkMap.entrySet()) {
                Integer key = mapEntry.getKey();
                List<String> value = mapEntry.getValue();
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("type", CommonConstant.TWO);
                jsonObject.put("accessID", CommonConstant.XZQH_GUIZHOU);
                //流动党员登记下载确认
                if (key.equals(CommonConstant.ONE_INT) && value.size() > CommonConstant.ZERO_INT) {
                    jsonObject.put("dataType", CommonConstant.ONE);
                }
                //检测流动信息是否存在
                if (key.equals(CommonConstant.TWO_INT) && value.size() > CommonConstant.ZERO_INT) {
                    jsonObject.put("dataType", CommonConstant.TWO);
                }
                //流出方结束流动
                if (key.equals(CommonConstant.FIVE_INT) && value.size() > CommonConstant.ZERO_INT) {
                    jsonObject.put("dataType", CommonConstant.FIVE);
                }
                //编辑信息查询
                if (key.equals(CommonConstant.EIGHT_INT) && value.size() > CommonConstant.ZERO_INT) {
                    jsonObject.put("dataType", CommonConstant.EIGHT);
                }
                if (value.size() > CommonConstant.ZERO_INT) {
                    JSONArray jsonArray = new JSONArray();
                    value.forEach(uq -> {
                        JSONObject object = new JSONObject();
                        object.put("uniqueKey", uq);
                        jsonArray.add(object);
                    });
                    jsonObject.put("info", jsonArray);
                    // log.info("开始执行新流入的流动党员下载接口，进行中组部消息确认，请求数据：："+jsonObject.toJSONString());
                    String returnMessage = null;
                    try {
                        returnMessage = memFlow1Service.uploadLDDY(jsonObject, true);
                    } catch (Exception e) {
                        log.error("执行省外数据状态确认异常", e);
                    }
                    log.info("执行省外数据状态下载，进行中组部消息确认，反馈数据：：" + returnMessage);
                }
            }
        });
    }

    /**
     * 下载编辑信息
     *
     * @param uq
     * @throws Exception
     * @remark {"LDBJ":{"LDDJ0015":"134235689411","LDDJ0014":"332","LDDJ0013":"","LDDJ0024":"0178344FD2D2000C295FCBFA6E3E1C0C","LDBJ0001":"","LDDJ0012":"第一党支部"}}
     */
    public boolean aVoid8(String uq, String code) throws Exception {
        JSONObject pushJson = new JSONObject();
        pushJson.put("type", CommonConstant.TWO);
        pushJson.put("accessID", CommonConstant.XZQH_GUIZHOU);
        pushJson.put("dataType", CommonConstant.EIGHT);
        pushJson.put("uniqueKey", uq);
        // String lddyData = memFlow1Service.downloadLDDY(pushJson);
        String lddyData = this.downloadLDDY(pushJson);
        JSONObject dataJson = JSONObject.parseObject(lddyData);
        //解密请求数据
        if (StrUtil.isNotEmpty(dataJson.getString("secretKey")) && StrUtil.isNotEmpty(dataJson.getString("info"))) {
            String realInfo = CryptoUtil.getRealInfo(dataJson.getString("info"), dataJson.getString("secretKey"));
            this.saveFlowContent(uq, code, realInfo, "流入的流动党员状态检查aVoid8");
            JSONObject jsonObject = JSONObject.parseObject(realInfo);
            if (ObjectUtil.isNotEmpty(jsonObject)) {
                log.info("dataType为8：流动信息修改，查询待流出方或流入方下载的数据；（type为1流出方下载；type为2流入方下载；），type=8：" + uq + "：" + jsonObject.toJSONString());
                MemFlow1 memFlow = new MemFlow1();
                memFlow.setCode(code);
                JSONObject ldbj = jsonObject.getJSONObject("LDBJ");
                String lddj0020 = ldbj.getString("LDDJ0020");
                if (StrUtil.isNotBlank(lddj0020)) {
                    Date lddj0020Date = new SimpleDateFormat("yyyy-MM").parse(lddj0020);
                    memFlow.setPartyExpensesOutTime(lddj0020Date);
                }
                String lddj0033 = ldbj.getString("LDDJ0033");
                if (StrUtil.isNotBlank(lddj0033)) {
                    Date lddj00330Date = DateUtil.parseDateTime(lddj0033);
                    memFlow.setRegisterTime(lddj00330Date);
                    memFlow.setOutRegisterTime(lddj00330Date);
                }
                return memFlow1Service.updateById(memFlow);
            }
        }
        return false;
    }

    public String downloadLDDY(JSONObject pushData) throws Exception {
        try {
            LddyWebServiceImplService lddyWebServiceImplService = new LddyWebServiceImplService();
            LddyWebServiceImpl lddyWebServiceImplPort = lddyWebServiceImplService.getLddyWebServiceImplPort();

            Map<String, String> stringMap;
            stringMap = CryptoTwoUtil.signData(pushData.toString(), "1");

            String signData = stringMap.get("signData");
            String data = stringMap.get("data");
            String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
            String unicode = cnToUnicode(qianMingData);
            String resultStr = lddyWebServiceImplPort.download(unicode);
            return new String(Base64Utils.decode(resultStr.getBytes()), StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("下载流入的流动党员状态检查接口异常：", e);
            throw e;
        }
    }

    private void saveFlowContent(String flowUqCode, String flowCode, String realInfo, String dataType) {
        try {
            FlowContent flowContent = new FlowContent();
            String contentId = StrKit.getRandomUUID();
            flowContent.setId(contentId);
            flowContent.setContent(realInfo);
            flowContent.setCreateTime(new Date());
            flowContent.setDataType(dataType);
            flowContent.setFlowUqCode(flowUqCode);
            flowContent.setFlowCode(flowCode);
            flowContentMapper.insert(flowContent);
        } catch (Exception e) {
            log.error("下载流入的流动党员状态检查接口保存content表异常", e);
        }
    }

    @Override
    public OutMessage<Object> findExchangeLxfs(String flowCode) {
        MemFlow1 memFlow1 = memFlow1Service.getById(flowCode);
        if (ObjectUtil.isEmpty(memFlow1) || StrUtil.startWith(flowCode, "05200")) {
            return new OutMessage<>(Status.SUCCESS);
        }
        String flowUpCode = memFlow1.getFlowUqCode();
        JSONObject jsonData = new JSONObject();
        jsonData.put("accessID", CommonConstant.XZQH_GUIZHOU);//本省根节点
        jsonData.put("dataType", "21");//传输的类型

        String[] sm2Keys = CryptoUtil.getSm2Keys(CryptoUtil.CERT_ID);
        jsonData.put("secretKey", sm2Keys[1]);//随机码,sm2加密后的
        JSONArray infoObject = new JSONArray();
        JSONObject uploadData = new JSONObject();
        uploadData.put("ID", IdUtil.fastSimpleUUID());
        uploadData.put("LDDY_ID", memFlow1.getLddyIdZzb());
        uploadData.put("LDLX0001", CommonConstant.XZQH_GUIZHOU);
        uploadData.put("LDLX0002", "");
        String outPlaceCode = memFlow1.getOutPlaceCode();
        if (StrUtil.equals(outPlaceCode, "5")) {
            // 流向流动党组织
            LambdaQueryWrapper<TransferOrgFlow> queryMemFlowByCode = new LambdaQueryWrapper<TransferOrgFlow>().eq(TransferOrgFlow::getD01id, memFlow1.getOutOrgBranchCode())
                    .orderByAsc(TransferOrgFlow::getId).last("limit 1");
            TransferOrgFlow transferOrg = transferOrgFlowMapper.selectOne(queryMemFlowByCode);
            if (ObjectUtil.isNull(transferOrg) || null == transferOrg.getJddm()) {
            } else {
                uploadData.put("LDLX0002", transferOrg.getJddm());
            }
        } else {
            String outOrgCode = memFlow1.getOutOrgCode();
            LambdaQueryWrapper<TransferOrg> queryMemFlowByCode = new LambdaQueryWrapper<TransferOrg>().eq(TransferOrg::getD01id, outOrgCode).orderByAsc(TransferOrg::getId).last("limit 1");
            TransferOrg transferOrg = transferOrgMapper.selectOne(queryMemFlowByCode);
            if (ObjectUtil.isNull(transferOrg) || null == transferOrg.getJddm()) {
            } else {
                uploadData.put("LDLX0002", transferOrg.getJddm());
            }
        }
        uploadData.put("SP_ORGID", memFlow1.getOutOrgCode());
        infoObject.add(uploadData);
        String infoJsonString = infoObject.toJSONString();
        // 加密
        jsonData.put("info", CryptoUtil.sm4Encrypt(infoJsonString, sm2Keys[0]));

        log.info("上传获取联系记录方式数据：{}", infoJsonString);
        try {
            String resultJson = memFlow1Service.uploadLDDY(jsonData, false);
            log.info("上传获取联系记录方式结果数据:{}", resultJson);
            jsonData.put("info", infoJsonString);
            memFlow1Service.deailWithUploadData(resultJson, flowCode, flowUpCode, 1005, jsonData.toJSONString());
        } catch (Exception e) {
            log.error("上传获取联系记录方式失败", e);
            jsonData.put("info", infoJsonString);
            memFlow1Service.catchDeailData(flowCode, flowUpCode, 1005, jsonData.toJSONString());
        }
        return new OutMessage<>(Status.SUCCESS);
    }

    @Override
    public OutMessage<Object> findExchangeLxfsList(List<String> orgCode) {
        if (CollUtil.isEmpty(orgCode)) {
            return new OutMessage<>(Status.SUCCESS, new ArrayList<>());
        }
        List<MemFlow1> lxfsCallbackListByOrgCodeList = memFlow1Mapper.findLxfsCallbackListByOrgCodeList(orgCode);
        return new OutMessage<>(Status.SUCCESS, lxfsCallbackListByOrgCodeList);
    }

    @Override
    public void repair500Error(String startDate) {
        LambdaQueryWrapper<FlowUploadData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ne(FlowUploadData::getDataType, 1);
        queryWrapper.ge(FlowUploadData::getCreateTime, DateUtil.beginOfDay(DateUtil.parseDate(startDate)));
        queryWrapper.eq(FlowUploadData::getBackCode, "100");
        queryWrapper.eq(FlowUploadData::getIsSuccess, 0);
        queryWrapper.orderByAsc(FlowUploadData::getCreateTime);
        List<FlowUploadData> flowUploadData = flowUploadMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(flowUploadData)) {
            return;
        }
        flowUploadData.forEach(flowUploadData1 -> {
            JSONObject jsonObject = JSONObject.parseObject(flowUploadData1.getUploadData());

            String info = jsonObject.getString("info");
            boolean jsonObjectOrArray = isJsonObjectOrArray(info);
            if (jsonObjectOrArray) {
                String[] sm2Keys = CryptoUtil.getSm2Keys(CryptoUtil.CERT_ID);
                jsonObject.put("secretKey", sm2Keys[1]);//随机码,sm2加密后的
                jsonObject.put("info", CryptoUtil.sm4Encrypt(info, sm2Keys[0]));
            }
            try {
                String reusltStr = memFlow1Service.uploadLDDY(jsonObject, false);
                log.info("修复500错误,flowUploadData-Id:{},flowUqCode:{},结果数据:{}", flowUploadData1.getId(), flowUploadData1.getFlowUqCode(), reusltStr);
                JSONObject resultJson = JSONObject.parseObject(reusltStr);
                String code = resultJson.get("code").toString();
                String msg = resultJson.get("msg").toString();
                if (code.equals(CommonConstant.ZERO_ONE) || code.equals("00")) {
                    flowUploadData1.setIsSuccess(CommonConstant.ONE_INT);
                } else if (code.equals("07") && StrUtil.equals(msg, "数据终止成功")) {
                    flowUploadData1.setIsSuccess(CommonConstant.ONE_INT);
                }
                flowUploadData1.setBackCode(code);
                flowUploadData1.setBackMessage(reusltStr);
                flowUploadMapper.updateById(flowUploadData1);
            } catch (Exception e) {
                log.error("修复500错误失败", e);
            }
        });
    }

    public static boolean isJsonObjectOrArray(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }
        try {
            Object obj = JSON.parse(str);
            return obj instanceof JSONObject || obj instanceof JSONArray;
        } catch (Exception e) {
            return false;
        }
    }
}




