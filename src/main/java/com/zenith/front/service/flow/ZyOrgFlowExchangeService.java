package com.zenith.front.service.flow;

import com.alibaba.fastjson.JSONObject;
import com.zenith.front.common.OutMessage;
import com.zenith.front.entity.model.ExchangeLog;
import com.zenith.front.entity.model.OrgFlow;
import com.zenith.front.entity.model.OrgFlowAudit;
import com.zenith.front.entity.model.TransferOrgFlowAudit;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/1/17 17:49
 */
public interface ZyOrgFlowExchangeService {
    /**
     * 流入
     * ——包头没有dataType，为党组织上传（具有审批预备党员权限组织、或者流入地成立的流动党员党组织）；
     *
     * @param type 1-新增，2-修改，3-删除
     */
    public boolean uploadLiuRu(OrgFlow orgFlow, OrgFlowAudit orgFlowAudit, String type) throws Exception;

    /**
     * 流出
     * ——包头的dataType取值为1表示上传待备案的流出地成立的流动党员党组织。
     * ——包头的dataType取值为5表示流出地上传备案通过的流动党员党组织的修改信息。
     *
     * @param orgFlow
     * @param orgFlowAudit
     * @param type         1-新增，2-修改，3-删除
     * @return
     * @throws Exception
     */
    public boolean uploadLiuChu(OrgFlow orgFlow, OrgFlowAudit orgFlowAudit, String type) throws Exception;

    /**
     * 全量下载交换区流动党组织
     * ——包头的dataType取值为1表示下载具有审批预备党员权限组织或者驻在地成立的流动党员党组织、或者全量下载具有审批预备党员权限组织或者驻在地成立的流动党员党组织。
     */
    void downloadAllOrgFlow() throws Exception;

    /**
     * 下载需要我方审批的数据
     * ——包头的dataType取值为3表示流入地下载流出地成立的，待流入地备案的流动党员党组织信息。10
     *
     * @throws Exception
     */
    void downloadNeedMyAudit() throws Exception;

    /**
     * 下载我方审批后对方变更后的数据
     * ——包头的dataType取值为6表示流入地下载流出地修改备案通过后的流动党员党组织信息。50
     * <p>
     * // todo: tanmw 2025/1/22 15:08  中组部，目前对方变更后能用datatype=3下载，确实待审批
     *
     * @throws Exception
     */
    void downloadNeedMyAuditUpdate() throws Exception;

    /**
     * 我方审批其他外省份流动党组织结果上传
     * <p>
     * 包头的dataType取值为2表示流入地上传备案流程审批结果信息。
     *
     * @param flowCode
     * @param audit
     * @return
     */
    Boolean uploadCheckOther(String flowCode, TransferOrgFlowAudit audit);

    /**
     * 下载我方流出需要其他省份审核的结果
     * <p>
     * 包头的dataType取值为4表示流出地下载驻在地审批的备案流程和流出地成立的流动党员党组织信息。
     */
    void downloadNeedOtherAudit() throws Exception;

    /**
     * dataType为10：驻在地待备案的流动党员党组织下载结果确认上传
     * dataType为20：流出地备案后的流动党员党组织审批记录下载结果确认上传
     * dataType为50：驻在地备案通过后修改的流动党员党组织下载结果确认上传
     *
     * @param result   下载结果，00为成功。10为失败。
     * @param result   orgidArray 多个待审批的流动党员党组织
     * @param dataType
     */
    void uploadDownResult(String dataType, String result, List<String> orgidArray) throws Exception;

    /**
     * 上传操作保存操作日志
     *
     * @param bizId
     * @param requestData
     * @param encryptStr
     * @return
     */
    ExchangeLog saveExchangeLog(String bizId, String requestData, String encryptStr);

    /**
     * 党组织通用下载测试接口
     *
     * @param orgFlowCodeList
     * @param type
     * @throws Exception
     */
    public void download(List<String> orgFlowCodeList, String type) throws Exception;

    /**
     * 党组织通用上传测试接口，info加密
     *
     * @param jsonObject
     * @return
     * @throws Exception
     */
    OutMessage<Object> uploadLiuRuTest(JSONObject jsonObject) throws Exception;

    /**
     * 党组织通用上传测试接口，不加密
     *
     * @param jsonObject
     * @return
     * @throws Exception
     */
    OutMessage<Object> uploadNotEncrypt(JSONObject jsonObject) throws Exception;

    /**
     * 党组织通用下载测试接口
     *
     * @param jsonObject
     * @return
     * @throws Exception
     */
    OutMessage<Object> downloadLiuRuTest(JSONObject jsonObject) throws Exception;

    /**
     * 保存下载到的数据
     *
     * @param requestUrl 请求方法
     * @param allResult  返回的整个数据
     * @throws Exception
     */
    String saveDownloadExchangeLog(String requestUrl, String allResult) throws Exception;

    void updateDownloadExchangeLog(String logId, String info);

    void uploadErrorByLogList(List<ExchangeLog> exchangeLogList) throws Exception;

    void syncLiuChuResult();

    OutMessage upload531() throws Exception;

    OutMessage<Object> uploadMemTest(JSONObject jsonObject) throws Exception;

    OutMessage<Object> downloadMemTest(JSONObject jsonObject) throws Exception;

}
