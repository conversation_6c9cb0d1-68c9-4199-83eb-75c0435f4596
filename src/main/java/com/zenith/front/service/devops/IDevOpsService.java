package com.zenith.front.service.devops;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.common.OutMessage;
import com.zenith.front.entity.dto.*;
import com.zenith.front.entity.model.DevOps;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-29
 */
public interface IDevOpsService extends IService<DevOps> {

    OutMessage start(DevOpsDto data, String basePath);

    OutMessage getList(DevOpsListDto data, String token);

    OutMessage uploadWeb(MultipartFile file, String basePath) throws Exception;

    OutMessage uploadJar(MultipartFile file, String basePath) throws Exception;

    OutMessage getPrefectureLevel();

    OutMessage getProgressBar(String key);

    OutMessage getData();

    /**
     * 根据节点key查找对应的层级码
     *
     * @param nk 节点key
     * @return 节点组织层级码
     */
    String findOrgCodeByNginxKey(String nk);

    /**
     * 重置密码
     *
     * @param data 请求实体类
     * @return
     */
    OutMessage<?> resetPassword(ResetPasswordDTO data);

    /**
     * 文件上传
     *
     * @param file     文件
     * @param baseUploadPath 基础路径
     * @return com.zenith.front.common.OutMessage<java.lang.Object>
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2023年02月03日 11时28分
     */
    OutMessage<Object> upload(MultipartFile file, String baseUploadPath) throws Exception;
    OutMessage<Object> uploadData(MultipartFile file, String baseUploadPath,String account) throws Exception;

    OutMessage<?> toAuthorization(ToAuthorizationDTO data);

    /**
     * 党组织编码搜索
     * @param dto
     * @return
     */
    OutMessage<?> findOrgD01001(OrgD01001DTO dto);

    /**
     * 查询所有
     * @return
     */
    OutMessage getListAll(DevOpsListDto dto);
}
