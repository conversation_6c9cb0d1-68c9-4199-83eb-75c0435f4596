package com.zenith.front.service.relationshiptransfer;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zenith.front.entity.model.TransferLetter;
import com.zenith.front.entity.model.TransferProcess;
import com.zenith.front.mapper.TransferLetterMapper;
import com.zenith.front.mapper.TransferProcessMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-13
 */
@Service
public class TransferProcessServiceImpl extends ServiceImpl<TransferProcessMapper, TransferProcess> implements ITransferProcessService {

    @Resource
    private TransferProcessMapper transferProcessMapper;

    @Override
    public List<TransferProcess> findTransferProcessListByLetterNumber(String letterNumber) {
        return transferProcessMapper.selectList(new QueryWrapper<TransferProcess>().lambda().eq(TransferProcess::getLetterNumber, letterNumber).orderByDesc(TransferProcess::getBlgc0006));
    }
}
