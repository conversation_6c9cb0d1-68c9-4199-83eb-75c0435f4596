package com.zenith.front.service.dataexchange;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.entity.model.MemHistory;
import com.zenith.front.mapper.MemHistoryMapper;
import com.zenith.front.service.dataexchange.api.IMemHistoryService;
import org.springframework.stereotype.Service;

/**
 * 历史党员
 *
 * <AUTHOR>
 * @date 2021/7/15
 */
@Service
public class MemHistoryServiceImpl extends ServiceImpl<MemHistoryMapper, MemHistory> implements IMemHistoryService {

}
