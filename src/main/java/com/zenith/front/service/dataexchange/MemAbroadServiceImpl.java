package com.zenith.front.service.dataexchange;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.entity.model.MemAbroad;
import com.zenith.front.mapper.MemAbroadMapper;
import com.zenith.front.service.dataexchange.api.IMemAbroadService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 出入国服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Service
public class MemAbroadServiceImpl extends ServiceImpl<MemAbroadMapper, MemAbroad> implements IMemAbroadService {


}
