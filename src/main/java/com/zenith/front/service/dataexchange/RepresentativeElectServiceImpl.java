package com.zenith.front.service.dataexchange;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.entity.model.RepresentativeElect;
import com.zenith.front.mapper.RepresentativeElectMapper;
import com.zenith.front.service.dataexchange.api.IRepresentativeElectService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Service
public class RepresentativeElectServiceImpl extends ServiceImpl<RepresentativeElectMapper,RepresentativeElect> implements IRepresentativeElectService {

}
