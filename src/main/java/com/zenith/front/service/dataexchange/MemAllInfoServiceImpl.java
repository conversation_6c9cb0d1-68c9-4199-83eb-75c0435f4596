package com.zenith.front.service.dataexchange;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.entity.model.MemAllInfo;
import com.zenith.front.mapper.MemAllInfoMapper;
import com.zenith.front.service.dataexchange.api.IMemAllInfoService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-25
 */
@Service
public class MemAllInfoServiceImpl extends ServiceImpl<MemAllInfoMapper, MemAllInfo> implements IMemAllInfoService {

}
