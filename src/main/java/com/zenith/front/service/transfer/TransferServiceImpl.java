package com.zenith.front.service.transfer;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.webservice.esb.ESBUploadAnQuerybusinessServiceImpl;
import com.webservice.esb.ESBUploadAnQuerybusinessServiceImplService;
import com.webservice.util.CryptoUtil;
import com.webservice.util.JackSonUtil;
import com.zenith.front.common.OutMessage;
import com.zenith.front.common.Status;
import com.zenith.front.config.TaskConfig;
import com.zenith.front.constant.CommonConstant;
import com.zenith.front.entity.dto.CLGCDTO;
import com.zenith.front.entity.model.*;
import com.zenith.front.entity.vo.JSXVO;
import com.zenith.front.entity.dto.TransferUploadDataDTO;
import com.zenith.front.entity.vo.TransferProcessVO;
import com.zenith.front.mapper.*;
import com.zenith.front.scheduled.RepairTransferUploadData;
import com.zenith.front.service.relationshiptransfer.ITransferLetterService;
import com.zenith.front.service.relationshiptransfer.ITransferOrgService;
import com.zenith.front.service.relationshiptransfer.ITransferProcessService;
import com.zenith.front.untils.LetterUntils;
import com.zenith.front.untils.StrKit;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.webservice.util.CryptoUtil.cnToUnicode;
import static com.webservice.util.CryptoUtil.getQianMingData;
import static java.util.Comparator.comparing;

/**
 * @author: D.watermelon
 * @date: 2021/11/15 21:01
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 */
@Service
public class TransferServiceImpl extends ServiceImpl<TransFerMapper, TransFerExchangeArea>  implements TransferService {

    @Resource
    private TransFerMapper transFerMapper;

    @Autowired
    private OrgExchangeAreaMapper orgMapper;

    @Resource
    private ITransferOrgService transferOrgService;

    @Autowired
    private ITransferLetterService letterService;
    @Resource
    private ITransferProcessService iTransferProcessService;
    @Resource
    private TransferUploadMapper transferUploadMapper;
    @Resource
    private TransferContentMapper contentMapper;

    @Resource
    private DevOpsMapper opsMapper;

    @Resource
    private RepairTransferUploadData repairTransferUploadData;

    @Resource
    private TransferMonthStatisticsMapper transferMonthStatisticsMapper;

    @Autowired
    private TaskConfig taskConfig;

    @Override
    public OutMessage deailData(JSONObject jsonObject)  {
        //获取组织关系转接基础数据
        String  transferRecordId = jsonObject.getString("id");
        // TODO: 2021/11/15 这里还要根据ID去查询是否存在， 存在应该是更新，而不是新增了。
        TransFerExchangeArea byTransferId = transFerMapper.findByTransferId(transferRecordId);
        boolean b;
        Integer type = jsonObject.getInteger("type");
        if (ObjectUtil.isNull(byTransferId)){
            TransFerExchangeArea transFerExchangeArea = new TransFerExchangeArea();
            String trasFerId = StrUtil.uuid().replace("-", "");
            transFerExchangeArea.setId(trasFerId);
            transFerExchangeArea.setCreateTime(new Date());
            transFerExchangeArea.setUpdateTime(new Date());
            String srcOrgId = jsonObject.getString("srcOrgId");
            String srcOrgName = jsonObject.getString("srcOrgName");
            String targetOrgName = jsonObject.getString("targetOrgName");
            String targetOrgId = jsonObject.getString("targetOrgId");
            String srcExchangeKey = jsonObject.getString("srcExchangeKey");
            Integer status = jsonObject.getInteger("status");
            transFerExchangeArea.setStatus(status);
            transFerExchangeArea.setTransferId(transferRecordId);
            transFerExchangeArea.setSrcOrgId(srcOrgId);
            transFerExchangeArea.setSrcOrgName(srcOrgName);
            transFerExchangeArea.setTargetOrgName(targetOrgName);
            transFerExchangeArea.setTargetOrgId(targetOrgId);
            transFerExchangeArea.setData(jsonObject.toJSONString());
            transFerExchangeArea.setType(type);
           if (StrUtil.isNotBlank(srcExchangeKey)){
               transFerExchangeArea.setSrcExchangeKey(srcExchangeKey);
           }
            b=save(transFerExchangeArea);
           //新增出发交换区
            if (224==type){
                JSONObject jsonData = this.transferOutNational(transFerExchangeArea);
                if (b&&ObjectUtil.isNotNull(jsonData)){
                    try {
                        OutMessage outMessage = letterService.uploadESB(jsonData);
                        // TODO: 2022/9/27 修改为最新得重复推送方式
                        String data = (String) outMessage.getData();
                        this.deailWithUploadData(outMessage,transferRecordId,StrUtil.isBlank(data)?"":data,CommonConstant.ONE_INT,jsonData.toJSONString());
                        this.updateThransferState(outMessage,trasFerId,false);
                    } catch (Exception e) {
                        e.printStackTrace();
                        this.catchDeailData(transferRecordId,jsonData.toJSONString(),CommonConstant.ONE_INT);
                    }
                }
            }
        }else {
            //如果这个id存在的情况下，就代表是任意一个业务节点操作了该数据，同步过来处理的
            String srcOrgId = jsonObject.getString("srcOrgId");
            String srcOrgName = jsonObject.getString("srcOrgName");
            String targetOrgName = jsonObject.getString("targetOrgName");
            String targetOrgId = jsonObject.getString("targetOrgId");
            //状态
            Integer status = jsonObject.getInteger("status");
            TransFerExchangeArea transFerExchangeArea = new TransFerExchangeArea();
            transFerExchangeArea.setStatus(status);
            transFerExchangeArea.setTransferId(transferRecordId);
            transFerExchangeArea.setSrcOrgId(srcOrgId);
            transFerExchangeArea.setSrcOrgName(srcOrgName);
            transFerExchangeArea.setTargetOrgName(targetOrgName);
            transFerExchangeArea.setTargetOrgId(targetOrgId);
            transFerExchangeArea.setData(jsonObject.toJSONString());
            transFerExchangeArea.setUpdateTime(new Date());
            String trasFerId = byTransferId.getId();
            transFerExchangeArea.setId(trasFerId);
            transFerExchangeArea.setType(type);
            b=updateById(transFerExchangeArea);
            //类别为转出类型的时候，才走全国交换区
            if (224==type){
                //如果状态没变化=更新信息发起，发生变化=否者等于撤销
                Integer oldStatus = byTransferId.getStatus();
                if (status.equals(oldStatus)){
                    JSONObject jsonData = this.transferOutNational(transFerExchangeArea);
                    OutMessage outMessage = null;
                    try {
                        outMessage = letterService.uploadESB(jsonData);
                    } catch (Exception e) {
                        e.printStackTrace();
                        this.catchDeailData(transferRecordId,jsonData.toJSONString(),CommonConstant.ONE_INT);
                    }
                    String data = (String) outMessage.getData();
                    int returnCode = outMessage.getCode();
                    if (returnCode ==CommonConstant.ZERO_INT){
                        TransFerExchangeArea updateTransferArea= new TransFerExchangeArea();
                        updateTransferArea.setId(trasFerId);
                        updateTransferArea.setLetterNumber(data);
                        updateTransferArea.setUpdateTime(new Date());
                        updateById(updateTransferArea);
                    }
                    this.deailWithUploadData(outMessage,transferRecordId,StrUtil.isBlank(data)?"":data,CommonConstant.ONE_INT,jsonData.toJSONString());
                    this.updateThransferState(outMessage,trasFerId,false);
                }
                if(!status.equals(oldStatus)&&status.equals(CommonConstant.TWO_INT)){
                    String transferIdData = transFerExchangeArea.getData();
                    JSONObject transferJson = JSONObject.parseObject(transferIdData);
                    //审核步骤办理情况
                    JSONArray approvalJson = transferJson.getJSONArray("approval");
                    // TODO: 2022/10/14 这里可能存在一种情况， 就是上级直接拒绝，没有发起全国交换区， 所以需要判断是否已经上传到中间交换区
                    if(approvalJson.size()>1){
                        JSONArray bLGCJson= new JSONArray();
                        this.blgcData(approvalJson,bLGCJson,null,"7","撤销办理",byTransferId.getLetterNumber(),CommonConstant.ONE_INT);
                        try {
                            OutMessage outMessage =  letterService.uploadBLGC(bLGCJson.toJavaList(CLGCDTO.class));
                            this.deailWithUploadData(outMessage,transferRecordId,byTransferId.getLetterNumber(),CommonConstant.TWO_INT,bLGCJson.toJSONString());
                            this.updateThransferState(outMessage,trasFerId,true);
                        } catch (Exception e) {
                            e.printStackTrace();
                            this.catchDeailData(transferRecordId,bLGCJson.toJSONString(),CommonConstant.TWO_INT);
                        }
                    }
                }
            }
            //类别为转入类型的时候，如果关系转接状态完成，需要告诉省外转接流程报道和完成
            if (124==type){
                this.inTransFerData(status,transFerExchangeArea,transferRecordId);
            }
        }
        return new OutMessage<>(b ? Status.SUCCESS : Status.FAIL);

    }

    @Override
    public OutMessage<Object> backData(JSONObject jsonObject) {
        //获取组织关系转接基础数据
        String  transferId = jsonObject.getString("id");
        // TODO: 2021/11/15 这里还要根据ID去查询是否存在， 存在应该是更新，而不是新增了。
        TransFerExchangeArea byTransferId = transFerMapper.findByTransferId(transferId);
        boolean b;
        Integer type = jsonObject.getInteger("type");
        if (ObjectUtil.isNull(byTransferId)){
            TransFerExchangeArea transFerExchangeArea = new TransFerExchangeArea();
            String trasFerId = StrUtil.uuid().replace("-", "");
            transFerExchangeArea.setId(trasFerId);
            transFerExchangeArea.setCreateTime(jsonObject.getDate("createTime"));
            transFerExchangeArea.setUpdateTime(jsonObject.getDate("updateTime"));
            String srcOrgId = jsonObject.getString("srcOrgId");
            String srcOrgName = jsonObject.getString("srcOrgName");
            String targetOrgName = jsonObject.getString("targetOrgName");
            String targetOrgId = jsonObject.getString("targetOrgId");
            String srcExchangeKey = jsonObject.getString("srcExchangeKey");
            Integer status = jsonObject.getInteger("status");
            transFerExchangeArea.setStatus(status);
            transFerExchangeArea.setTransferId(transferId);
            transFerExchangeArea.setSrcOrgId(srcOrgId);
            transFerExchangeArea.setSrcOrgName(srcOrgName);
            transFerExchangeArea.setTargetOrgName(targetOrgName);
            transFerExchangeArea.setTargetOrgId(targetOrgId);
            transFerExchangeArea.setData(jsonObject.toJSONString());
            transFerExchangeArea.setType(type);
            if (StrUtil.isNotBlank(srcExchangeKey)){
                transFerExchangeArea.setSrcExchangeKey(srcExchangeKey);
            }
            b=save(transFerExchangeArea);
        }else {
            //如果这个id存在的情况下，就代表是任意一个业务节点操作了该数据，同步过来处理的
            String srcOrgId = jsonObject.getString("srcOrgId");
            String srcOrgName = jsonObject.getString("srcOrgName");
            String targetOrgName = jsonObject.getString("targetOrgName");
            String targetOrgId = jsonObject.getString("targetOrgId");
            //状态
            Integer status = jsonObject.getInteger("status");
            TransFerExchangeArea transFerExchangeArea = new TransFerExchangeArea();
            transFerExchangeArea.setStatus(status);
            transFerExchangeArea.setTransferId(transferId);
            transFerExchangeArea.setSrcOrgId(srcOrgId);
            transFerExchangeArea.setSrcOrgName(srcOrgName);
            transFerExchangeArea.setTargetOrgName(targetOrgName);
            transFerExchangeArea.setTargetOrgId(targetOrgId);
            transFerExchangeArea.setData(jsonObject.toJSONString());
            transFerExchangeArea.setUpdateTime(jsonObject.getDate("updateTime"));
            //transFerExchangeArea.setUpdateTime(new Date());
            String trasFerId = byTransferId.getId();
            transFerExchangeArea.setId(trasFerId);
            transFerExchangeArea.setType(type);
            b=updateById(transFerExchangeArea);
        }
        return new OutMessage<>(b ? Status.SUCCESS : Status.FAIL);
    }

    @Override
    public OutMessage findOnUniqueCode(String uniqueCode) throws Exception {
        //判断关系转接是否已经被拉取
        List<TransFerExchangeArea> transFerExchangeAreas = transFerMapper.selectList(new QueryWrapper<TransFerExchangeArea>().lambda().eq(TransFerExchangeArea::getLetterNumber, uniqueCode));
        if (transFerExchangeAreas.size()>CommonConstant.ZERO_INT){
            return new OutMessage(Status.UNIQUE_KEY_IS_EXIST);
        }
        ESBUploadAnQuerybusinessServiceImplService service = new ESBUploadAnQuerybusinessServiceImplService();
        ESBUploadAnQuerybusinessServiceImpl serviceImplPort = service.getESBUploadAnQuerybusinessServiceImplPort();
        JSONObject postJsonData=new JSONObject();
        JSONArray dataJsonArray=new JSONArray();
        dataJsonArray.add(uniqueCode);
        postJsonData.put("dataType", CommonConstant.FIVE);
        postJsonData.put("accessID", CommonConstant.XZQH_GUIZHOU);
        postJsonData.put("uniqueKey",dataJsonArray);
        Map<String, String> stringMap = CryptoUtil.signData(postJsonData.toString(), "1");
        String signData = stringMap.get("signData");
        String data = stringMap.get("data");
        String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
        String unicode = cnToUnicode(qianMingData);
        String resultStr = serviceImplPort.query(unicode);
        if (StrUtil.isEmpty(resultStr)){
            return new OutMessage(Status.ZZB_DATA_IS_NULL);
        }
        String responseData = new String(Base64Utils.decode(resultStr.getBytes()), StandardCharsets.UTF_8);
        JSXVO jsxvo = JackSonUtil.toObject(responseData, JSXVO.class);
        if (!jsxvo.getCode().equals(CommonConstant.ZERO_ONE)){
            String msg = jsxvo.getMsg();
            if (msg.equals("未查询到数据")){
                return new OutMessage(Status.ZZB_DATA_IS_ERR_MESSAGE);
            }
            return new OutMessage(CommonConstant.THIRTY_THREE_INT, msg,null);
        }
        if (StrUtil.isEmpty(jsxvo.getSecretKey()) || StrUtil.isEmpty(jsxvo.getInfo())){
            return new OutMessage(Status.ZZB_DATA_IS_ERR);
        }
        String realInfo = CryptoUtil.getRealInfo(jsxvo.getInfo(), jsxvo.getSecretKey());
        JSONArray jsonArray = JSONArray.parseArray(realInfo);
        // TODO: 2022/9/29 入库洪海林数据content表
        TransferContent transferContent = new TransferContent();
        String contentId = StrKit.getRandomUUID();
        transferContent.setId(contentId);
        transferContent.setContent(realInfo);
        transferContent.setCreateTime(new Date());
        transferContent.setDataType("唯一码拉取");
        contentMapper.insert(transferContent);
        return this.createProcess(jsonArray,contentId);
    }

    @Override
    public List<TransFerExchangeArea> findAlltransferData(Integer status) {
         if (ObjectUtil.isNull(status)){
             return list(new QueryWrapper<TransFerExchangeArea>().isNotNull("letter_number").ne("letter_number",""));
         }
        return list(new QueryWrapper<TransFerExchangeArea>().select("letter_number","status","transfer_id").isNotNull("letter_number").ne("letter_number","").eq("status",status));
    }

    @Override
    public void inTransFerData(Integer status,TransFerExchangeArea transFerExchangeArea,String trasFerRecordId) {
        TransFerExchangeArea byTransferId = transFerMapper.findByTransferId(trasFerRecordId);
        if (status.equals(CommonConstant.ONE_INT)){
            //转入接收完成，本地已经入库， 需要处理全新的办理过程
            String transferIdData = transFerExchangeArea.getData();
            JSONObject transferJson = JSONObject.parseObject(transferIdData);
            //审核步骤办理情况
            JSONArray approvalJson = transferJson.getJSONArray("approval");
            JSONArray bLGCJson= new JSONArray();
            //处理序号问题
            List<TransferProcess> transferProcessListByLetterNumber = iTransferProcessService.findTransferProcessListByLetterNumber(byTransferId.getLetterNumber());
            transferProcessListByLetterNumber.sort(comparing(TransferProcess::getBlgc0011).reversed());
            Integer number;
            if (transferProcessListByLetterNumber.size()==CommonConstant.ZERO_INT||ObjectUtil.isNull(transferProcessListByLetterNumber)){
//                number=CommonConstant.HUNDRED_INI;
                // todo 中组部对顺序号超过2位的进行了限制。固定给个6，一般情况顺序号没这么大。
                number = CommonConstant.SIX_INT;
            }else {
                number=transferProcessListByLetterNumber.get(CommonConstant.ZERO_INT).getBlgc0011();
            }
            //党员同意
            this.blgcDataTwo(approvalJson,bLGCJson,"0",null,byTransferId.getLetterNumber(),number+CommonConstant.ONE_INT);
            //处理党员报道
            this.blgcDataTwo(approvalJson,bLGCJson,"5",null,byTransferId.getLetterNumber(),number+CommonConstant.TWO_INT);
            try {
                OutMessage outMessage =  letterService.uploadBLGC(bLGCJson.toJavaList(CLGCDTO.class));
                this.deailWithUploadData(outMessage,trasFerRecordId,byTransferId.getLetterNumber(),CommonConstant.TWO_INT,bLGCJson.toJSONString());
                this.updateThransferState(outMessage,byTransferId.getId(),false);
            } catch (Exception e) {
                e.printStackTrace();
                this.catchDeailData(trasFerRecordId,bLGCJson.toJSONString(),CommonConstant.TWO_INT);
            }
        }
        if (status.equals(CommonConstant.TWO_INT)){
            //转入接收完成，本地已经入库， 需要处理全新的办理过程
            String transferIdData = transFerExchangeArea.getData();
            JSONObject transferJson = JSONObject.parseObject(transferIdData);
            //审核步骤办理情况
            JSONArray approvalJson = transferJson.getJSONArray("approval");
            JSONArray bLGCJson= new JSONArray();
            //处理序号问题
            List<TransferProcess> transferProcessListByLetterNumber = iTransferProcessService.findTransferProcessListByLetterNumber(byTransferId.getLetterNumber());
            transferProcessListByLetterNumber.sort(comparing(TransferProcess::getBlgc0011).reversed());
            Integer number;
            if (transferProcessListByLetterNumber.size()==CommonConstant.ZERO_INT||ObjectUtil.isNull(transferProcessListByLetterNumber)){
//                number=CommonConstant.HUNDRED_INI;
                // todo 中组部对顺序号超过2位的进行了限制。固定给个6，一般情况顺序号没这么大。
                number = CommonConstant.SIX_INT;
            }else {
                number=transferProcessListByLetterNumber.get(CommonConstant.ZERO_INT).getBlgc0011();
            }
            //处理党员审核拒绝
            this.blgcDataTwo(approvalJson,bLGCJson,"1","2",byTransferId.getLetterNumber(),number+CommonConstant.ONE_INT);
            try {
                OutMessage outMessage =  letterService.uploadBLGC(bLGCJson.toJavaList(CLGCDTO.class));
                this.deailWithUploadData(outMessage,trasFerRecordId,byTransferId.getLetterNumber(),CommonConstant.TWO_INT,bLGCJson.toJSONString());
                this.updateThransferState(outMessage,transFerExchangeArea.getId(),false);
            } catch (Exception e) {
                e.printStackTrace();
                this.catchDeailData(trasFerRecordId,bLGCJson.toJSONString(),CommonConstant.TWO_INT);
            }
        }
    }

    public void blgcDataTwo(JSONArray approvalJson ,JSONArray bLGCJson,String BLGC0006,String BLGC0007,String uniqueKey,Integer number){
        approvalJson.sort(comparing(obj -> JSONObject.toJavaObject((JSONObject) obj, TransferApproval.class).getCreateTime()).reversed());
        TransferApproval transferApproval;
        // TODO: 2022/4/28  这里到底要取哪一个，需要根据流程步骤来看。以及流程双方节点来看
        if (approvalJson.size()>CommonConstant.THREE_INT){
            transferApproval = JSONObject.toJavaObject((JSONObject) approvalJson.get(CommonConstant.ONE_INT), TransferApproval.class);
        }else {
            transferApproval = JSONObject.toJavaObject((JSONObject) approvalJson.get(CommonConstant.ZERO_INT), TransferApproval.class);
        }
        JSONObject blgcJson=new JSONObject();
        String orgId = transferApproval.getOrgId();
        OrgExchangeArea orgByCode = orgMapper.findOrgByCode(orgId);
        //经办党组织代码
        blgcJson.put("BLGC0001",null==orgByCode?null:orgByCode.getD01001());
        if (StrUtil.isNotBlank(uniqueKey)){
            blgcJson.put("uniqueKey",uniqueKey);
        }
        //经办党组织名称
        blgcJson.put("BLGC0002",null==orgByCode?"经办党组织名称":orgByCode.getName());
        //经办人的姓名
        blgcJson.put("BLGC0003",null==orgByCode?"经办人的姓名":orgByCode.getContacter());
        //经办人的联系电话（手机）
        blgcJson.put("BLGC0004",null==orgByCode?"经办人的联系电话（手机）":orgByCode.getContactPhone());
        //办理日期
        blgcJson.put("BLGC0005",DateUtil.format(transferApproval.getCreateTime(),"yyyy-MM-dd HH:mm:ss"));
        //办理情况
        blgcJson.put("BLGC0006",BLGC0006);
        //办理意见
        blgcJson.put("BLGC0007",BLGC0007);
        //备注
        blgcJson.put("BLGC0008",null);
        //与党员见面人
        blgcJson.put("BLGC0009",null);
        //党员报到日期 DateUtil.format(reportTime,"yyyy-MM-dd HH:mm:ss")
        blgcJson.put("BLGC0010",null);
        //顺序号
        blgcJson.put("BLGC0011",String.valueOf(number));
        //是否由上级党组织代办,1是0否
        blgcJson.put("BLGC0012",String.valueOf(transferApproval.getIsInstead()));
        bLGCJson.add(blgcJson);

    }
    @Override
    public boolean updateThransferState(OutMessage outMessage, String trasFerId,Boolean isBack){
        String data = (String) outMessage.getData();
        TransFerExchangeArea updateTransferArea= new TransFerExchangeArea();
        updateTransferArea.setId(trasFerId);
        //交换区无法连接或者报错
        if (ObjectUtil.isNull(outMessage)&&!isBack){
            updateTransferArea.setLetterMessageCode(CommonConstant.MINUS_ZERO);
            updateTransferArea.setLetterMessage(outMessage.toString());
        }
        int returnCode = outMessage.getCode();
        //上传成功
        if (returnCode ==CommonConstant.ZERO_INT&&!isBack){
            updateTransferArea.setLetterMessageCode(String.valueOf(returnCode));
            updateTransferArea.setLetterNumber(data);
            updateTransferArea.setLetterMessage(outMessage.toString());
        }
        if (ObjectUtil.isNotNull(outMessage)&&!isBack){
            //上传失败
            updateTransferArea.setLetterMessageCode(String.valueOf(returnCode));
            updateTransferArea.setLetterMessage(outMessage.toString());
        }

        if (ObjectUtil.isNull(outMessage)&&isBack){
            updateTransferArea.setLetterMessageCodeBack(CommonConstant.MINUS_ZERO);
            updateTransferArea.setLetterMessageBack(outMessage.toString());
        }

        if (returnCode ==CommonConstant.ZERO_INT&&isBack){
            updateTransferArea.setLetterMessageCodeBack(String.valueOf(returnCode));
            updateTransferArea.setLetterMessageBack(outMessage.toString());
        }
        if (ObjectUtil.isNotNull(outMessage)&&isBack){
            //上传失败
            updateTransferArea.setLetterMessageCodeBack(String.valueOf(returnCode));
            updateTransferArea.setLetterMessageBack(outMessage.toString());
        }
        updateTransferArea.setUpdateTime(new Date());
        return updateById(updateTransferArea);
    }
    @Override
    public JSONObject transferOutNational(TransFerExchangeArea byTransferId){
        String transferIdData = byTransferId.getData();
        JSONObject transferJson = JSONObject.parseObject(transferIdData);
        //审核步骤办理情况
        JSONArray approvalJson = transferJson.getJSONArray("approval");
        transferJson.remove("approval");
        if (approvalJson.size()<=CommonConstant.ONE_INT){
            //this.createProcess(JSONArray.parseArray("[{\"DYXX\":[{\"C01\":{\"C01004\":\"420984196309074035\",\"C01015\":\"\",\"C01005\":\"1963-09-07 00:00:00\",\"C01016\":\"13312345678\",\"C01006\":\"11\",\"C01UP1\":\"1\",\"C01007\":null,\"C01018\":null,\"C01UP2\":\"2022-03-08 13:28:07\",\"C01008\":\"01\",\"C01019\":\"\",\"C01009\":\"2022-04-01 00:00:00\",\"D01ID\":\"1506287340018991104\",\"C01ID\":\"3cea463402a944da92fdb92424a24d35\",\"C01010\":\"2023-04-01 00:00:00\",\"C01001\":\"1\",\"C01012\":\"10101\",\"C01002\":\"范高\",\"C01013\":null,\"C01003\":\"1\",\"C01014\":null}}],\"uniqueKey\":\"1231231231252022042500014\",\"BLGC\":[{\"uniqueKey\":\"1231231231252022042500014\",\"BLGC0010\":null,\"BLGC0001\":\"123123123125\",\"BLGC0012\":\"0\",\"BLGC0011\":1,\"BLGC0007\":\"9\",\"BLGC0006\":\"0\",\"BLGC0009\":\"\",\"BLGC0008\":\"222222222\",\"BLGC0003\":\"CPC\",\"BLGC0002\":\"测试党支部B\",\"BLGC0005\":\"2022-04-25 09:13:14\",\"BLGC0004\":\"13312340004\"},{\"uniqueKey\":\"1231231231252022042500014\",\"BLGC0010\":null,\"BLGC0001\":\"061000000001\",\"BLGC0012\":\"0\",\"BLGC0011\":2,\"BLGC0007\":\"9\",\"BLGC0006\":\"0\",\"BLGC0009\":\"\",\"BLGC0008\":\"dfasdfasdfaasdfasdfasdf\",\"BLGC0003\":\"CPC\",\"BLGC0002\":\"xxx集团股份有限公司委员会\",\"BLGC0005\":\"2022-04-25 09:14:25\",\"BLGC0004\":\"13312340004\"}],\"JSX\":{\"JSX0017\":\"中共遵义市桐梓县教育体育局委员会\",\"JSX0016\":\"052000000445\",\"JSX0019\":90,\"JSX0018\":\"2022-04-25 00:00:00\",\"JSX0013\":\"测试党支部B\",\"JSX0012\":\"123123123125\",\"JSX0015\":\"A366DF5DF3DD436E8BAFF5CB0A6FBEE7\",\"JSX0014\":\"3\",\"JSX0031\":\"1\",\"JSX0030\":\"1\",\"JSX0011\":\"1506287340018991104\",\"JSX0010\":\"10101\",\"JSX0009\":58,\"uniqueKey\":\"1231231231252022042500014\",\"JSX0006\":\"01\",\"JSX0028\":\"\",\"JSX0005\":\"1\",\"JSX0027\":\"\",\"JSX0008\":\"420984196309074035\",\"JSX0007\":\"1\",\"JSX0029\":\"052000000001\",\"JSX0002\":\"1\",\"JSX0024\":\"ceshi11\",\"JSX0023\":\"\",\"JSX0001\":\"3cea463402a944da92fdb92424a24d35\",\"JSX0004\":\"范高\",\"JSX0026\":\"\",\"JSX0003\":\"jsxtt\",\"JSX0025\":\"ceshi11\",\"JSX0020\":\"sdfsadfasdf\",\"JSX0022\":\"13312345678\",\"JSX0021\":\"2022-04-25 00:00:00\"}}]"));
            return null;
        }
        JSONArray bLGCJson= new JSONArray();
        // TODO: 2022/4/19 这里需要根据创建时间排序， 然后进行自动生成序号
        JSONObject jSXJson= new JSONObject();
        TransferRecord transferRecord = JSONObject.toJavaObject(transferJson, TransferRecord.class);
        this.blgcData(approvalJson,bLGCJson,jSXJson,"0","允许转接",null,CommonConstant.ONE_INT);
        //党员附加基础信息
        JSONArray dYXXJson= new JSONArray();
        this.deailWithDYXX(dYXXJson,transferRecord,jSXJson);

        //介绍信信息
        //转接类型
        jSXJson.put("JSX0002","1");
        //抬头（转入党组织名称）(放具有预备党员审批权限得党委)
        jSXJson.put("JSX0003",transferRecord.getTargetOrgName());

        //转出党支部单位性质
        String outD04Code = transferRecord.getOutD04Code();
        if (StrUtil.isBlank(outD04Code)||outD04Code.length()>4){
            outD04Code="411";
        }
        jSXJson.put("JSX0014",outD04Code);
        //接收党组织ID(暂时写死)
        String targetOrgId = transferRecord.getTargetOrgId();
        jSXJson.put("JSX0015", targetOrgId);
        //jSXJson.put("JSX0015", "0178344FD2DF000C295FCBFA6E3E1C8A");
        TransferOrg orgByName = transferOrgService.getOrgByName(targetOrgId);
        //接收党组织代码(暂时写死)
        jSXJson.put("JSX0016",orgByName.getD01001());
        //jSXJson.put("JSX0016", "035010000124");
        //接收党组织名称(暂时写死)
        String targetPartyBranch = transferRecord.getTargetPartyBranch();
        jSXJson.put("JSX0017",StrUtil.isBlank(targetPartyBranch)?transferRecord.getTargetOrgName():targetPartyBranch);
        //jSXJson.put("JSX0017", "tpjd2");
        //党费缴至日期
        jSXJson.put("JSX0018",DateUtil.format(transferRecord.getMemFeeEndTime(),"yyyy-MM-dd HH:mm:ss"));
        //有效期（天）
        jSXJson.put("JSX0019","90");
        //开具日期//
        jSXJson.put("JSX0021",DateUtil.format(transferRecord.getCreateTime(),"yyyy-MM-dd HH:mm:ss"));

        //党员原所在基层党委通讯地址
        jSXJson.put("JSX0024",null);
        //党员原所在基层党委联系传真
        jSXJson.put("JSX0026",null);
        //党员原所在基层党委邮编
        jSXJson.put("JSX0027",null);
        //备注
        jSXJson.put("JSX0028","备注");
        //接收地系统接入标识;通过转入的组织查询他的上级编码来查询
        jSXJson.put("JSX0029", orgByName.getJddm());
        //变更标识
        jSXJson.put("JSX0030","1");
        //转接原因
        jSXJson.put("JSX0031",transferRecord.getD146Code());
        JSONObject dataJsonObject = new JSONObject();
        dataJsonObject.put("DYXX",dYXXJson);
        dataJsonObject.put("BLGC",bLGCJson);
        dataJsonObject.put("JSX",jSXJson);
        return dataJsonObject;
    }

    public  void  deailWithDYXX( JSONArray dYXXJson, TransferRecord transferRecord,JSONObject jSXJson){
        JSONObject memJson = (JSONObject) transferRecord.getExtraData();
        Mem transFerMem = JSONObject.toJavaObject(memJson, Mem.class);
        // TODO: 2022/4/19 处理党员基本信息
        //党员基础信息
        JSONObject c01Json =new JSONObject();
        //党员唯一标识Y
        c01Json.put("C01ID",transFerMem.getCode());
        //党员唯一标识
        jSXJson.put("JSX0001",transFerMem.getCode());
        //党员联系电话（手机）
        // TODO: 2023/6/10 增加修复兼容党员如果从省外转入，没经过编辑就直接转出的情况
        jSXJson.put("JSX0022",ObjectUtil.isNull(transFerMem.getPhone())?" ":transFerMem.getPhone());
        //党员其他联系方式
        jSXJson.put("JSX0023",ObjectUtil.isNull(transFerMem.getPhone())?" ":transFerMem.getPhone());
        //党组织唯一标识
        c01Json.put("D01ID",transFerMem.getOrgCode());
        //转出党组织ID
        jSXJson.put("JSX0011",transFerMem.getOrgCode());
        //人员类别
        c01Json.put("C01001",ObjectUtil.isNull(transFerMem.getd08Code())?" ":transFerMem.getd08Code());
        //人员类别
        jSXJson.put("JSX0007",ObjectUtil.isNull(transFerMem.getd08Code())?" ":transFerMem.getd08Code());
        //姓名
        c01Json.put("C01002",ObjectUtil.isNull(transFerMem.getName())?" ":transFerMem.getName());
        //姓名
        jSXJson.put("JSX0004",ObjectUtil.isNull(transFerMem.getName())?" ":transFerMem.getName());
        //性别
        String sexCode = ObjectUtil.isNull(transFerMem.getSexCode()) ? " " : transFerMem.getSexCode();
        if (sexCode.equals(CommonConstant.ZERO)){
            sexCode=CommonConstant.TWO;
        }
        c01Json.put("C01003", sexCode);
        //性别
        jSXJson.put("JSX0005", sexCode);
        //公民身份号码
        c01Json.put("C01004",ObjectUtil.isNull(transFerMem.getIdcard())?" ":transFerMem.getIdcard());
        //公民身份号码
        jSXJson.put("JSX0008",ObjectUtil.isNull(transFerMem.getIdcard())?" ":transFerMem.getIdcard());
        //出生日期
        c01Json.put("C01005",ObjectUtil.isNull(transFerMem.getBirthday())?DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss"):
        DateUtil.format(transFerMem.getBirthday(),"yyyy-MM-dd HH:mm:ss"));
        //年龄（系统计算年龄）
        jSXJson.put("JSX0009",DateUtil.ageOfNow(transFerMem.getBirthday()));
        //学历
        c01Json.put("C01006",transFerMem.getd07Code());
        //学位
        c01Json.put("C01007",transFerMem.getD145Code());
        //民族
        c01Json.put("C01008",transFerMem.getd06Code());
        //民族
        jSXJson.put("JSX0006",transFerMem.getd06Code());
        //入党日期

        c01Json.put("C01009",DateUtil.format(transFerMem.getJoinOrgDate(),"yyyy-MM-dd HH:mm:ss"));
        //转正日期
        c01Json.put("C01010",DateUtil.format(transFerMem.getFullMemberDate(),"yyyy-MM-dd HH:mm:ss"));
        //党龄校正值
        c01Json.put("C01011","0");
        //工作岗位
        c01Json.put("C01012",transFerMem.getd09Code());
        //党员原工作岗位
        jSXJson.put("JSX0010",transFerMem.getd09Code().length()>5?"0602":transFerMem.getd09Code());
        //新社会阶层类型
        c01Json.put("C01013",transFerMem.getd20Code());
        //从事专业技术职务
        c01Json.put("C01014",transFerMem.getd19Code());
        //是否农民工
        c01Json.put("C01015",transFerMem.getIsFarmer());
        //手机号码
        c01Json.put("C01016",transFerMem.getPhone());
        //联合支部所在单位
        c01Json.put("C01017",null);
        //户籍所在地
        c01Json.put("C01018",transFerMem.getHouseholdRegister());
        //现居住地
        c01Json.put("C01019",transFerMem.getHomeAddress());
        //进入本信息系统类型
        c01Json.put("C01023",transFerMem.getd11Code());
        //进入本信息系统日期
        c01Json.put("C01024",DateUtil.format(transFerMem.getJoinOrgPartyDate(),"yyyy-MM-dd HH:mm:ss"));
        //进入本信息系统操作党组织
        c01Json.put("C01025",transFerMem.getOrgCode());
        //离开本信息系统类型//跨省转出DM25
        c01Json.put("C01026",CommonConstant.THREE);
        //离开本信息系统日期
        c01Json.put("C01027",DateUtil.format(transferRecord.getCreateTime(),"yyyy-MM-dd HH:mm:ss"));
        //离开本信息系统操作党组织
        c01Json.put("C01028",transFerMem.getOrgCode());
        //人员状态//当前人员DM36
        c01Json.put("C01UP1",CommonConstant.ONE);
        //更新时间戳
        c01Json.put("C01UP2",DateUtil.format(transFerMem.getUpdateTime(),"yyyy-MM-dd HH:mm:ss"));
        //操作党组织
        c01Json.put("C01UP3",transFerMem.getOrgCode());

        //党代表以及委员信息
        JSONArray c02Json = new JSONArray();
        //党员奖惩信息
        JSONArray c03Json = new JSONArray();
        //党员出国境信息
        JSONArray c04Json = new JSONArray();
        //党员帮扶信息
        JSONArray c05Json = new JSONArray();
        //党员入党情况
        JSONObject c06Json = new JSONObject();
        JSONObject dYXXJsonObj= new JSONObject();
        dYXXJsonObj.put("C01",c01Json);
        dYXXJsonObj.put("C02",c02Json);
        dYXXJsonObj.put("C03",c03Json);
        dYXXJsonObj.put("C04",c04Json);
        dYXXJsonObj.put("C05",c05Json);
        dYXXJsonObj.put("C06",c06Json);
        dYXXJson.add(dYXXJsonObj);
    }

    public void blgcData(JSONArray approvalJson ,JSONArray bLGCJson,JSONObject jSXJson,String BLGC0006,String BLGC0007,String uniqueKey,Integer reduce){
        approvalJson.sort(comparing(obj -> JSONObject.toJavaObject((JSONObject) obj, TransferApproval.class).getCreateTime().getTime()).reversed());
        for (int i = 0; i < approvalJson.size()-reduce; i++) {
            if (i==0)continue;
            TransferApproval transferApproval = JSONObject.toJavaObject((JSONObject) approvalJson.get(i), TransferApproval.class);
            JSONObject blgcJson=new JSONObject();
            String orgId = transferApproval.getOrgId();
            OrgExchangeArea orgByCode = orgMapper.findOrgByCode(orgId);
            //经办党组织代码
            blgcJson.put("BLGC0001",null==orgByCode?null:orgByCode.getD01001());
            System.out.println("查询的经办党组织orgid: "+orgId);
            if (i==1&&ObjectUtil.isNotNull(jSXJson)){
                //转出党组织代码
                jSXJson.put("JSX0012",null==orgByCode?null:orgByCode.getD01001());
                //转出党组织名称
                jSXJson.put("JSX0013",null==orgByCode?null:orgByCode.getName());
                //党员原所在基层党委联系电话
                jSXJson.put("JSX0025",null==orgByCode?null:orgByCode.getContactPhone());
                //落款
                jSXJson.put("JSX0020",orgByCode.getName());
            }
            if (StrUtil.isNotBlank(uniqueKey)){
                blgcJson.put("uniqueKey",uniqueKey);
            }
            //经办党组织名称
            blgcJson.put("BLGC0002",null==orgByCode?"经办党组织名称":orgByCode.getName());
            //经办人的姓名
            blgcJson.put("BLGC0003",null==orgByCode?"经办人的姓名":orgByCode.getContacter());
            //经办人的联系电话（手机）
            blgcJson.put("BLGC0004",null==orgByCode?"经办人的联系电话（手机）":orgByCode.getContactPhone());
            //办理日期
            blgcJson.put("BLGC0005",DateUtil.format(transferApproval.getCreateTime(),"yyyy-MM-dd HH:mm:ss"));
            //办理情况
            blgcJson.put("BLGC0006",BLGC0006);
            //办理意见 1 接收党组织填写错误 2 接收党组织不同意接收 3 党员信息错误 4 不符合有关政策规定 9 其他
            blgcJson.put("BLGC0007","9");
            //备注
            blgcJson.put("BLGC0008",BLGC0007);
            //与党员见面人
            blgcJson.put("BLGC0009",null);
            //党员报到日期 DateUtil.format(reportTime,"yyyy-MM-dd HH:mm:ss")
            blgcJson.put("BLGC0010",null);
            //顺序号
            blgcJson.put("BLGC0011",String.valueOf(i));
            //是否由上级党组织代办,1是0否
            blgcJson.put("BLGC0012",String.valueOf(transferApproval.getIsInstead()));
            bLGCJson.add(blgcJson);
        }
    }

    @Override
    public OutMessage findData(String orgCode,Integer type) {
        // TODO: 2021/11/16 这里是要根据顶层节点去查询他的信息
        List<TransFerExchangeArea> byCode =new ArrayList<>();
        //根据交换区key去查询组织信息
        List<String> orgByExchangeKey = orgMapper.findOrgByExchangeKey(orgCode);
        if (ObjectUtil.isNotNull(orgByExchangeKey)&&orgByExchangeKey.size()>0){
            //根据组织信息的id去查询关系转接
            if (type.equals(CommonConstant.ONE_INT)){
                //请求中间交换区中，转接到我的目的节点的数据
                byCode = transFerMapper.findByTargetCode(orgByExchangeKey);
            }
            if (type.equals(CommonConstant.TWO_INT)){
                //请求中间节点是我发起的数据回来处理
                byCode = transFerMapper.findBySrcCode(orgByExchangeKey);
            }
            if (type.equals(CommonConstant.THREE_INT)){
                //请求中间节点是我发起的整建制
                byCode = transFerMapper.findBySrcExchangeKey(orgCode);
            }
        }
        return new OutMessage<>(Status.SUCCESS,byCode);
    }

    //接收党员数据
    @Override
    public OutMessage createProcess(JSONArray dataJsonArray,String contenctId ){
        StringBuffer transFerId = new StringBuffer();
        for (Object o : dataJsonArray) {
            JSONObject dataJsonObject= (JSONObject) o;
            //解析中间交换区数据结构
            //构造我们自己得中间交换区结构
            //分节点自己处理相应得数据结构
            //JSONObject dataJsonObject = dataJsonArray.getJSONObject(0);
            JSONArray dyxx = dataJsonObject.getJSONArray("DYXX");
            JSONArray blgc = dataJsonObject.getJSONArray("BLGC");
            JSONObject jSXJson = dataJsonObject.getJSONObject("JSX");
            //处理介绍信，生成本地中间库信息
            TransFerExchangeArea saveTransferArea= new TransFerExchangeArea();
            String uniqueKey = jSXJson.getString("uniqueKey");
            JSONObject lastBlgc = LetterUntils.lastBlgc(blgc);
            boolean lastIsCancel = LetterUntils.isCancel(lastBlgc);
            // 如果新转入的最后一个直接是取消状态，本地不做任何处理，也不进行入库
            if (lastIsCancel){
                return null;
            }
            // TODO: 2022/5/7 解决重复入库
            List<TransFerExchangeArea> transFerExchangeAreas = transFerMapper.selectList(new QueryWrapper<TransFerExchangeArea>().lambda().eq(TransFerExchangeArea::getLetterNumber, uniqueKey));
            if (CollectionUtil.isNotEmpty(transFerExchangeAreas)){
                // TODO: 2023/8/18 这里可能会出现我们拒绝后，对面重复发起的问题，所以只要捕捉到了新的重新发起的流程，需要删除原来数据， 不管数据进行到那一步，都需要重新处理
                // TODO: 2024/9/22 如果是出现重复数据， 可能出现几种情况，1.对面转入，我们拒绝后，重新发起转入进来的；2.我们拒绝后，交换区直接90天终止或者下载10次，一直在推送；
                //查看转入的状态，如果是取消状态，根据本地状态进行处理，本地是完成状态--不用管，本地是进行中--取消本地，本地是已拒绝--不用管
                //过滤出本地是进行中的
                List<TransFerExchangeArea> zeroList = transFerExchangeAreas.stream().filter(transFerExchangeArea -> transFerExchangeArea.getStatus().equals(CommonConstant.ZERO_INT)).collect(Collectors.toList());
                zeroList.forEach(transFerExchangeArea->{
                    if (lastIsCancel){
                        //transFerExchangeArea.setDeleteTime(new Date());
                        transFerExchangeArea.setStatus(CommonConstant.TWO_INT);
                        String transferDate = transFerExchangeArea.getData();
                        JSONObject transferObject = JSONObject.parseObject(transferDate);
                        //transferObject.put("deleteTime",System.currentTimeMillis());
                        transferObject.put("status", CommonConstant.TWO_INT);
                        transFerExchangeArea.setData(transferObject.toJSONString());
                        transFerMapper.updateById(transFerExchangeArea);
                    }
                });
            }
            saveTransferArea.setLetterNumber(uniqueKey);
            String trasFerId = StrUtil.uuid().replace("-", "");
            String trasFerRecordId = StrUtil.uuid().replace("-", "");
            saveTransferArea.setId(trasFerId);
            saveTransferArea.setTransferId(trasFerRecordId);
            String jsx0011 = jSXJson.getString("JSX0011");
            saveTransferArea.setSrcOrgId(jsx0011);
            String jsx0013 = jSXJson.getString("JSX0013");
            saveTransferArea.setSrcOrgName(jsx0013);
            //"中共遵义市第一人民医院委员会";//
            String jsx0017 = jSXJson.getString("JSX0017");
            saveTransferArea.setTargetOrgName(jsx0017);
            //"AF0E79EAC86046E3984B290B03EB96F5";//
            String jsx0015 = jSXJson.getString("JSX0015");
            saveTransferArea.setTargetOrgId(jsx0015);
            saveTransferArea.setCreateTime(new Date());
            saveTransferArea.setUpdateTime(new Date());
            saveTransferArea.setType(124);
            saveTransferArea.setStatus(CommonConstant.ZERO_INT);
            //处理分库基础组织表
            TransferRecord transferRecordData= new TransferRecord();
            transferRecordData.setId(trasFerRecordId);
            transferRecordData.setUserId(StrKit.getRandomUUID().replace("-", ""));
            transferRecordData.setSrcOrgId(jsx0011);
            transferRecordData.setSrcOrgName(jsx0013);
            transferRecordData.setTargetOrgId(jsx0015);
            transferRecordData.setTargetOrgName(jsx0017);

            //创建公共的节点东西
            transferRecordData.setCommonOrgId("5048C51OE8B74ACF891A1EE5143F85A7");
            transferRecordData.setCommonOrgName("中共贵州省委员会");

            //创建发起节点集合
            List<String> srcList =Arrays.asList("5048C51OE8B74ACF891A1EE5143F85A7",jsx0011);
            transferRecordData.setSrcOrgRelation(srcList.toString());

            //创建接收节点集合
            List<String> targetList =Arrays.asList("5048C51OE8B74ACF891A1EE5143F85A7",jsx0015);
            transferRecordData.setSrcOrgRelation(targetList.toString());

            //党费缴纳到日期
            Date jsx0018 = jSXJson.getDate("JSX0018");
            transferRecordData.setMemFeeEndTime(jsx0018);
            //党费缴纳标准
            transferRecordData.setMemFeeStandard(null);
            transferRecordData.setOutType("");
            transferRecordData.setInType("124");
            transferRecordData.setType("124");
            transferRecordData.setStatus(0);
            transferRecordData.setReason(null);
            transferRecordData.setCreateTime(new Date());
            transferRecordData.setUpdateTime(new Date());
            // 党员人员类别
            String jsx0007 = jSXJson.getString("JSX0007");
            Mem mem = this.decryptMem(dyxx,jsx0007);
            //党员姓名
            transferRecordData.setName(mem.getName());
            //党员id
            // TODO: 2023/8/2 因为出现一种情况有BUG，1.从省内转到省外再转回来；2.省外转入省内转出再转回来；经过基础工程定时任务修复，会导致历史数据重新出现
            transferRecordData.setMemId(StrKit.getRandomUUID());
            transferRecordData.setExtraData(JSONObject.parseObject(JSONObject.toJSONString(mem)));
            transferRecordData.setLetterUrl("介绍信url");
            transferRecordData.setEffectMems(1);
            transferRecordData.setRemark("备注");
            transferRecordData.setSrcOrgRelationRel(Arrays.asList("5048C51OE8B74ACF891A1EE5143F85A7",jsx0011));
            transferRecordData.setTargetOrgRelationRel(Arrays.asList("5048C51OE8B74ACF891A1EE5143F85A7",jsx0015));
            transferRecordData.setSrcOrgRelation(Arrays.asList("5048C51OE8B74ACF891A1EE5143F85A7",jsx0011));
            transferRecordData.setTargetOrgRelation(Arrays.asList("5048C51OE8B74ACF891A1EE5143F85A7",jsx0015));
            transferRecordData.setTransferOutTime(null);
            transferRecordData.setReportTime(null);
            String jsx0031 = jSXJson.getString("JSX0031");
            transferRecordData.setD146Code(jsx0031);
            transferRecordData.setD146Name(jsx0031);
            String jsx0014 = jSXJson.getString("JSX0014");
            transferRecordData.setOutD04Code(jsx0014);
            String currentApprovalId = StrKit.getRandomUUID().replace("-", "");
            transferRecordData.setCurrentApprovalId(currentApprovalId);
            //放入步骤信息
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(transferRecordData));
            jsonObject.put("approval", this.stepData(blgc,trasFerRecordId,jsx0015,currentApprovalId));
            saveTransferArea.setData(jsonObject.toJSONString());
            //保存进入数据库
            save(saveTransferArea);
            transFerId.append(trasFerId+",");
        }
        if (StrUtil.isNotBlank(contenctId)){
            TransferContent transferContent = new TransferContent();
            transferContent.setId(contenctId);
            transferContent.setLetterNumber(transFerId.toString());
            contentMapper.updateById(transferContent);
        }
        return new OutMessage(Status.SUCCESS);
    }
    public Mem decryptMem(JSONArray dyxxJson,String jsx0007){
        Mem mem= new Mem();
        for (Object json : dyxxJson) {
            JSONObject jsonObject = (JSONObject) json;
            JSONObject c01=null;
            if (jsonObject.containsKey("C01")){
                c01= jsonObject.getJSONObject("C01");
            }
            if (jsonObject.containsKey("c01")){
                c01= jsonObject.getJSONObject("c01");
            }
            if (jsonObject.containsKey("A3")){
                c01= jsonObject.getJSONObject("A3");
            }
            if (ObjectUtil.isNotNull(c01)){
                // TODO: 2022/4/19 处理党员基本信息
                //党员基础信息
                //党员唯一标识
                // c01Json.put("C01ID",transFerMem.getCode());
                //党员唯一标识
                //jSXJson.put("JSX0001",transFerMem.getCode());
                String c01ID = c01.getString("C01ID");
                if (StrUtil.isEmpty(c01ID)){
                    c01ID = c01.getString("c01ID");
                }
                mem.setCode(c01ID);
                //党员联系电话（手机）
                //jSXJson.put("JSX0022",transFerMem.getPhone());
                String c01016 = c01.getString("C01016");
                if (StrUtil.isEmpty(c01016)){
                    c01016 = c01.getString("c01016");
                }
                mem.setPhone(c01016);
                //党员其他联系方式
                //党组织唯一标识
                //c01Json.put("D01ID",transFerMem.getOrgCode());
                //转出党组织ID
                //jSXJson.put("JSX0011",transFerMem.getOrgCode());
                //姓名
                //c01Json.put("C01002",transFerMem.getName());
                String c01002 = c01.getString("C01002");
                if (StrUtil.isEmpty(c01002)){
                    c01002 = c01.getString("c01002");
                }
                mem.setName(c01002);
                //性别
                //c01Json.put("C01003",transFerMem.getSexCode());
                String c01003 = c01.getString("C01003");
                if (StrUtil.isEmpty(c01002)){
                    c01003 = c01.getString("c01003");
                }
                mem.setSexCode(c01003);
                //公民身份号码
                //c01Json.put("C01004",transFerMem.getIdcard());
                String c01004 = c01.getString("C01004");
                if (StrUtil.isEmpty(c01002)){
                    c01004 = c01.getString("c01004");
                }
                mem.setIdcard(c01004);
                //出生日期
                //c01Json.put("C01005",DateUtil.format(transFerMem.getBirthday(),"yyyy-MM-dd HH:mm:ss"));
                Date c01005 = c01.getDate("C01005");
                if (ObjectUtil.isNull(c01005)){
                    c01005 = c01.getDate("c01005");
                }
                mem.setBirthday(c01005);
                //学历
                //c01Json.put("C01006",transFerMem.getd07Code());
                String c01006 = c01.getString("C01006");
                if (StrUtil.isEmpty(c01006)){
                    c01006 = c01.getString("c01006");
                }
                mem.setd07Code(c01006);
                //学位
                //c01Json.put("C01007",transFerMem.getD145Code());
                String c01007 = c01.getString("C01007");
                if (StrUtil.isEmpty(c01007)){
                    c01007 = c01.getString("c01007");
                }
                mem.setD145Code(c01007);
                //民族
                //c01Json.put("C01008",transFerMem.getd06Code());
                String c01008 = c01.getString("C01008");
                if (StrUtil.isEmpty(c01007)){
                    c01008 = c01.getString("c01008");
                }
                mem.setd06Code(c01008);
                //入党日期
                //c01Json.put("C01009",DateUtil.format(transFerMem.getJoinOrgDate(),"yyyy-MM-dd HH:mm:ss"));
                Date c01009 = c01.getDate("C01009");
                if (ObjectUtil.isNull(c01009)){
                    c01009= c01.getDate("c01009");
                }
                mem.setJoinOrgDate(c01009);
                //转正日期
                //c01Json.put("C01010",DateUtil.format(transFerMem.getFullMemberDate(),"yyyy-MM-dd HH:mm:ss"));
                Date c01010 = c01.getDate("C01010");
                if (ObjectUtil.isNull(c01010)){
                    c01010 = c01.getDate("c01010");
                }
                mem.setFullMemberDate(c01010);

                //人员类别
                //c01Json.put("C01001",transFerMem.getd08Code());
                // TODO: 2024/9/22 修复党员组织关系转接接入的党员身份问题，重新组织代码
                String c01001 = c01.getString("C01001");
                //兼容中组部以前数据， 存在大写也存在小写的数据
                if (StrUtil.isBlank(c01001)){
                    c01001 = c01.getString("c01001");
                }
                //c01001没值通过计算c01009和c01010来确定身份
                if (StrUtil.isEmpty(c01001)){
                    if (ObjectUtil.isNotNull(c01009)){
                        c01001=CommonConstant.TWO;
                        mem.setd08Code(CommonConstant.TWO);
                        mem.setd08Name("预备党员");
                    }
                    if (ObjectUtil.isNotNull(c01009)&&ObjectUtil.isNotNull(c01010)){
                        c01001=CommonConstant.ONE;
                        mem.setd08Code(CommonConstant.ONE);
                        mem.setd08Name("正式党员");
                    }
                }

                //通过计算后依旧没值。直接取介绍信值
                if (StrUtil.isEmpty(c01001)){
                    c01001=jsx0007;
                }
                mem.setd08Code(c01001);
                //处理正式党员和预备党员文字问题
                if (StrUtil.isNotEmpty(c01001)){
                    if (c01001.equals(CommonConstant.TWO)){
                        mem.setd08Code(CommonConstant.TWO);
                        mem.setd08Name("预备党员");
                    }
                    if (c01001.equals(CommonConstant.ONE)){
                        mem.setd08Code(CommonConstant.ONE);
                        mem.setd08Name("正式党员");
                    }
                }
                //预备党员，需要计算转正日期
                if (mem.getd08Code().equals(CommonConstant.TWO)&&ObjectUtil.isNotNull(mem.getJoinOrgDate())){
                    mem.setExtendPreparDate(DateUtil.offset( mem.getJoinOrgDate(), DateField.YEAR,CommonConstant.ONE_INT));
                }

                //党龄校正值
                //c01Json.put("C01011","0");
                //工作岗位
                //c01Json.put("C01012",transFerMem.getd09Code());
                String c01012 = c01.getString("C01012");
                if (StrUtil.isEmpty(c01012)){
                    c01012 = c01.getString("c01012");
                }
                mem.setd09Code(c01012);
                //新社会阶层类型
                //c01Json.put("C01013",transFerMem.getd20Code());
                String c01013 = c01.getString("C01013");
                if (StrUtil.isEmpty(c01013)){
                    c01013 = c01.getString("c01013");
                }
                mem.setd20Code(c01013);
                //从事专业技术职务
                //c01Json.put("C01014",transFerMem.getd19Code());
                String c01014 = c01.getString("C01014");
                if (StrUtil.isEmpty(c01014)){
                    c01014 = c01.getString("c01014");
                }
                mem.setd19Code(c01014);
                //是否农民工
                //c01Json.put("C01015",transFerMem.getIsFarmer());
                mem.setIsFarmer(CommonConstant.ZERO_INT);
                //手机号码
                //c01Json.put("C01016",transFerMem.getPhone());
                //联合支部所在单位
                //c01Json.put("C01017",null);
                //户籍所在地
                //c01Json.put("C01018",transFerMem.getHouseholdRegister());
                String c01018 = c01.getString("C01018");
                if (StrUtil.isEmpty(c01018)){
                    c01018 = c01.getString("c01018");
                }
                mem.setHouseholdRegister(c01018);
                //现居住地
                //c01Json.put("C01019",transFerMem.getHomeAddress());
                String c01019 = c01.getString("C01019");
                if (StrUtil.isEmpty(c01019)){
                    c01019 = c01.getString("c01019");
                }
                mem.setHomeAddress(c01019);
                //进入本信息系统类型
                //c01Json.put("C01023",transFerMem.getd11Code());
                String c01023 = c01.getString("C01023");
                if (StrUtil.isEmpty(c01023)){
                    c01023 = c01.getString("c01023");
                }
                mem.setd11Code(c01023);
                //进入本信息系统日期
                //c01Json.put("C01024",DateUtil.format(transFerMem.getJoinOrgPartyDate(),"yyyy-MM-dd HH:mm:ss"));
                // todo 废除掉C01024这个字段， 不再使用C01024，使用接受拉取到介绍信的时间
                mem.setJoinOrgPartyDate(new Date());
                //进入本信息系统操作党组织
                //c01Json.put("C01025",transFerMem.getOrgCode());
                //离开本信息系统类型//跨省转出DM25
                //c01Json.put("C01026","3");
                //离开本信息系统日期
                //c01Json.put("C01027",DateUtil.format(transferRecord.getCreateTime(),"yyyy-MM-dd HH:mm:ss"));
                mem.setCreateTime(new Date());
                //离开本信息系统操作党组织
                //c01Json.put("C01028",transFerMem.getOrgCode());
                //人员状态//当前人员DM36
                //c01Json.put("C01UP1","1");
                //更新时间戳
                //c01Json.put("C01UP2",DateUtil.format(transFerMem.getUpdateTime(),"yyyy-MM-dd HH:mm:ss"));
                mem.setUpdateTime(new Date());
                //操作党组织
                //c01Json.put("C01UP3",transFerMem.getOrgCode());
            }
        }
        return mem;
    }
    //处理审核节点分库步骤问题
    public List<TransferApproval> stepData(JSONArray blgc, String thransferRecordId, String tarOrgId,String twoAppId){
        blgc.sort(comparing(obj -> {
            JSONObject object= (JSONObject) obj;
            return object.getInteger("BLGC0011");
        }).reversed());
        
        JSONObject jsonObject = (JSONObject) blgc.get(blgc.size() - CommonConstant.ONE_INT);
        //生成办理过程
        List<TransferApproval> transferApprovalList =new ArrayList<>();
        //处理第一个审批节点
        TransferApproval oneTransferApproval= new TransferApproval();
        String oneAppId = StrKit.getRandomUUID().replace("-", "");
        oneTransferApproval.setId(oneAppId);
        oneTransferApproval.setRecordId(thransferRecordId);
        String blgc0003 = jsonObject.getString("BLGC0003");
        oneTransferApproval.setUserId(blgc0003);
        String blgc0002 = jsonObject.getString("BLGC0002");
        oneTransferApproval.setOrgId(blgc0002);
        oneTransferApproval.setNextOrgId("5048C51OE8B74ACF891A1EE5143F85A7");
        Integer blgc0012 = jsonObject.getInteger("BLGC0012");
        oneTransferApproval.setIsInstead(blgc0012);
        oneTransferApproval.setStatus(CommonConstant.ONE_INT);
        oneTransferApproval.setParentId(CommonConstant.MINUS_ZERO);
        oneTransferApproval.setCreateTime(new Date());
        oneTransferApproval.setUpdateTime(new Date());
        oneTransferApproval.setDirection(CommonConstant.ZERO_INT);
        transferApprovalList.add(oneTransferApproval);
        //处理中间库审批节点
        TransferApproval twoTransferApproval= new TransferApproval();
        twoTransferApproval.setId(twoAppId);
        twoTransferApproval.setRecordId(thransferRecordId);
        twoTransferApproval.setUserId(blgc0003);
        twoTransferApproval.setOrgId("5048C51OE8B74ACF891A1EE5143F85A7");
        twoTransferApproval.setNextOrgId(tarOrgId);
        twoTransferApproval.setIsInstead(CommonConstant.ONE_INT);
        twoTransferApproval.setStatus(CommonConstant.ONE_INT);
        twoTransferApproval.setParentId(oneAppId);
        twoTransferApproval.setCreateTime(new Date());
        twoTransferApproval.setUpdateTime(new Date());
        twoTransferApproval.setDirection(CommonConstant.TWO_INT);
        transferApprovalList.add(twoTransferApproval);
        return transferApprovalList;
    }

    /**
     * 处理组织关系转接转出得情况处理
     * **/
    @Override
    public  void createOutProcess(String letter_number){
        //查询办理过程得状态
        List<TransferProcess> transferProcessListByLetterNumber = iTransferProcessService.findTransferProcessListByLetterNumber(letter_number);
        if (transferProcessListByLetterNumber.size()>CommonConstant.ZERO_INT&&ObjectUtil.isNotNull(transferProcessListByLetterNumber)){
            TransferProcess transferProcess = transferProcessListByLetterNumber.get(CommonConstant.ZERO_INT);
            //办理情况(0-审批通过；1-审批未通过，退回上一步；2-全国业务数据交换区已接收，待下载； 4-接收党员；5-确认报到；
            // 7-发起方主动撤回，终止（只能是转出地具有审批预备党员权限的党组织才能撤回）；9-超时，全国业务数据交换区终止；12-撤回上一步，重新办理（只能是下一步经办党组织尚未点击办理时才可撤回）
            //注：2、9仅由全国业务数据交换区使用
            String blgc0006 = transferProcess.getBlgc0006();
            //前面所有过程忽略 处理办理成功状态（4，5）
            Integer integer = Integer.valueOf(blgc0006);
            TransFerExchangeArea transFerExchangeArea = transFerMapper.selectOne(new QueryWrapper<TransFerExchangeArea>().lambda().eq(TransFerExchangeArea::getLetterNumber, letter_number));
            if (ObjectUtil.isNull(transFerExchangeArea))return;
            //我转出的关系转接
            if (letter_number.startsWith("05200")){
                //办理情况(0-审批通过）我们不需要处理
                //1-审批未通过，退回上一步 我们本地需要退回撤销,7-发起方主动撤回，终止（只能是转出地具有审批预备党员权限的党组织才能撤回）；9-超时，全国业务数据交换区终止；12-撤回上一步，重新办理（只能是下一步经办党组织尚未点击办理时才可撤回）
                if (integer.equals(CommonConstant.NINE_INT)||integer.equals(CommonConstant.ONE_INT)||integer.equals(CommonConstant.SEVEN_INT)){
                    //处理中间数据状态为撤销
                    this.deailTransFerExchangeArea(transFerExchangeArea,CommonConstant.TWO_INT);
                    // TODO: 2022/5/17 处理生成撤销审核数据流程
                    String transferIdData = transFerExchangeArea.getData();
                    JSONObject transferJson = JSONObject.parseObject(transferIdData);
                    //审核步骤办理情况
                    JSONArray approvalJson = transferJson.getJSONArray("approval");
                    JSONArray bLGCJson= new JSONArray();
                    this.blgcData(approvalJson,bLGCJson,null,"7","撤销办理",transFerExchangeArea.getLetterNumber(),CommonConstant.ONE_INT);
                    try {
                        OutMessage outMessage = letterService.uploadBLGC(bLGCJson.toJavaList(CLGCDTO.class));
                        this.deailWithUploadData(outMessage,transFerExchangeArea.getTransferId(),transFerExchangeArea.getLetterNumber(),CommonConstant.TWO_INT,bLGCJson.toJSONString());
                        this.updateThransferState(outMessage,transFerExchangeArea.getId(),true);
                    } catch (Exception e) {
                        e.printStackTrace();
                        this.catchDeailData(transFerExchangeArea.getTransferId(),bLGCJson.toJSONString(),CommonConstant.TWO_INT);
                    }

                }
                // 4-接收党员；5-确认报到；
                if (integer.equals(CommonConstant.FOUR_INT)||integer.equals(CommonConstant.FIVE_INT)){
                    //处理本地数据状态为完成
                    this.deailTransFerExchangeArea(transFerExchangeArea,CommonConstant.ONE_INT);
                }
            }
            //转入我方的
            if(!letter_number.startsWith("05200")){
                //办理情况(0-审批通过）我们不需要处理，因为已经到我们这边该我们这边审核了
                //1-审批未通过，退回上一步 我们不需要处理，因为已经到我们这边该我们这边审核了，如果没通过， 就不会审核的
                // 4-接收党员；5-确认报到；这个是通过流程上传的， 我们这边不需要处理
                //7-发起方主动撤回，终止（只能是转出地具有审批预备党员权限的党组织才能撤回）；9-超时，全国业务数据交换区终止；12-撤回上一步，重新办理（只能是下一步经办党组织尚未点击办理时才可撤回）
                if (integer.equals(CommonConstant.NINE_INT)||integer.equals(CommonConstant.SEVEN_INT)){
                    //处理本地数据状态为撤销
                    this.deailTransFerExchangeArea(transFerExchangeArea,CommonConstant.TWO_INT);
                }
            }
            transFerMapper.updateById(transFerExchangeArea);
        }
    }
    public  void  deailTransFerExchangeArea(TransFerExchangeArea transFerExchangeArea,Integer status){
        transFerExchangeArea.setStatus(status);
        //处理json内部的数据为完成
        String data = transFerExchangeArea.getData();
        JSONObject transferObject = JSONObject.parseObject(data);
        transferObject.put("status",status);
        transFerExchangeArea.setData(transferObject.toJSONString());
    }

    /**
     * 处理上传数据记录表数据
     *
     * */
    @Override
    public void  deailWithUploadData(OutMessage outMessage,String trasFerRecordId,String letterNumber,Integer type,String uploadData){
        TransferUploadData saveUploadData= new TransferUploadData();
        saveUploadData.setId(StrKit.getRandomUUID());
        saveUploadData.setLetterNumber(letterNumber);
        saveUploadData.setTransferId(trasFerRecordId);
        saveUploadData.setUploadData(uploadData);
        saveUploadData.setDataType(type);
        int code = outMessage.getCode();
        saveUploadData.setBackCode(String.valueOf(code));
        saveUploadData.setBackMessage(outMessage.toString());
        saveUploadData.setCreateTime(new Date());
        saveUploadData.setUpdateTime(new Date());
        if (code==CommonConstant.ZERO_INT){
            saveUploadData.setIsSuccess(CommonConstant.ONE_INT);
        }else {
            saveUploadData.setIsSuccess(CommonConstant.ZERO_INT);
        }
        transferUploadMapper.insert(saveUploadData);
    }

    @Override
    public OutMessage monthStatistics(JSONObject jsonObject) {
        TransferMonthStatistics uploadTransMonthObj = JSON.toJavaObject(jsonObject, TransferMonthStatistics.class);
        uploadTransMonthObj.setID(StrKit.getRandomUUID());
        String zjtj0035 = uploadTransMonthObj.getZJTJ0035();
        DevOps devOps = opsMapper.selectOne(new LambdaQueryWrapper<DevOps>().eq(DevOps::getNodeKey, zjtj0035));
        if (ObjectUtil.isNotNull(devOps)){
            uploadTransMonthObj.setZJTJ0003(devOps.getMonthDatCode());
            uploadTransMonthObj.setZJTJ0004(devOps.getMonthDatName());

        }else {
            uploadTransMonthObj.setZJTJ0003("暂无顶层节点");
            uploadTransMonthObj.setZJTJ0004("暂无顶层节点");
        }
        //uploadTransMonthObj.setZJTJ0005("上级党组织编码，应该统一为贵州");
        //在本级党组织展示时使用的排序号
        uploadTransMonthObj.setZJTJ0029(CommonConstant.ONE_INT);
        uploadTransMonthObj.setZJTJ0033(CommonConstant.ONE_INT);
        uploadTransMonthObj.setZJTJ0034(CommonConstant.ONE_INT);
        uploadTransMonthObj.setCreateTime(new Date());
        //存储数据库

        LambdaQueryWrapper<TransferMonthStatistics> selectOne = new LambdaQueryWrapper<>();
        selectOne.eq(TransferMonthStatistics::getZJTJ0035,zjtj0035);
        selectOne.isNull(TransferMonthStatistics::getDeleteTime);
        Date zjtj0002 = uploadTransMonthObj.getZJTJ0002();
        selectOne .last(" and to_char(out_time,'yyyy-mm')='"+zjtj0002+"'");
        TransferMonthStatistics transferMonthStatistics = transferMonthStatisticsMapper.selectOne(selectOne);
        if (ObjectUtil.isNotNull(transferMonthStatistics)){
            transferMonthStatistics.setDeleteTime(new Date());
            transferMonthStatisticsMapper.updateById(transferMonthStatistics);
        }
        int insert = transferMonthStatisticsMapper.insert(uploadTransMonthObj);
        return new OutMessage(insert>CommonConstant.ZERO_INT?Status.SUCCESS:Status.FAIL);
    }

    @Override
    public OutMessage transferUnDo(String uniqueCode) {
        //获取是否拉入到当前数据库
        List<TransFerExchangeArea> transFerExchangeAreas = transFerMapper.selectList(new QueryWrapper<TransFerExchangeArea>().lambda().eq(TransFerExchangeArea::getLetterNumber, uniqueCode));
        if (transFerExchangeAreas.size()==CommonConstant.ZERO_INT){
            return new OutMessage(Status.DATA_DOES_NOT_EXIST);
        }
        //通过唯一码进行中组部的获取相关数据，同步中组部相关状态到本地
        for (TransFerExchangeArea transFerExchangeArea : transFerExchangeAreas) {
            String transFerExchangeAreaLetterNumber = transFerExchangeArea.getLetterNumber();
            if (!transFerExchangeAreaLetterNumber.startsWith("05200")){
                return new OutMessage(Status.ZZB_DATA_IS_ERR_GZ);
            }
            //是我们主动发起的数据， 进行主动撤销操作
            this.deailUpdateTransfer(transFerExchangeArea.getId(),transFerExchangeArea.getData(),CommonConstant.TWO_INT,null,"通过运维平台主动完成");
        }

        return null;
    }

    @Override
    public OutMessage failedData(int pageNum, int pageSize, String keyWord) {
        Page<TransferUploadData> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<TransferUploadData> wrapper = new QueryWrapper<TransferUploadData>().lambda()
                .isNull(TransferUploadData::getDeleteTime)
                .eq(TransferUploadData::getIsSuccess, CommonConstant.ZERO_INT)
                .orderByAsc(TransferUploadData::getCreateTime);
        wrapper.like(StringUtils.isNotBlank(keyWord), TransferUploadData::getTransferId, keyWord);
        Page<TransferUploadData> dataPage = transferUploadMapper.selectPage(page, wrapper);
        return new OutMessage(Status.SUCCESS, dataPage);
    }

    @Override
    public OutMessage updateTransfer(TransferUploadDataDTO vo) {
        TransferUploadData entity = new TransferUploadData();
        BeanUtils.copyProperties(vo, entity);
        int b = transferUploadMapper.updateById(entity);
        return new OutMessage(b > 0 ? Status.SUCCESS : Status.FAIL);
    }

    @Override
    public void pullTransfer(String id) {
        TransferUploadData transferUploadData = transferUploadMapper.selectById(id);
        repairTransferUploadData.processUploadData(transferUploadData);
    }

    @Override
    public OutMessage findStatusOnUniqueCode(String uniqueCode) throws Exception {
        ESBUploadAnQuerybusinessServiceImplService service = new ESBUploadAnQuerybusinessServiceImplService();
        ESBUploadAnQuerybusinessServiceImpl serviceImplPort = service.getESBUploadAnQuerybusinessServiceImplPort();
        JSONObject postJsonData=new JSONObject();
        JSONArray dataJsonArray=new JSONArray();
        dataJsonArray.add(uniqueCode);
        postJsonData.put("dataType", CommonConstant.FIVE);
        postJsonData.put("accessID", CommonConstant.XZQH_GUIZHOU);
        postJsonData.put("uniqueKey",dataJsonArray);
        Map<String, String> stringMap = CryptoUtil.signData(postJsonData.toString(), "1");
        String signData = stringMap.get("signData");
        String data = stringMap.get("data");
        String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
        String unicode = cnToUnicode(qianMingData);
        String resultStr = serviceImplPort.query(unicode);
        if (StrUtil.isEmpty(resultStr)){
            return new OutMessage(Status.ZZB_DATA_IS_NULL);
        }
        String responseData = new String(Base64Utils.decode(resultStr.getBytes()), StandardCharsets.UTF_8);
        JSXVO jsxvo = JackSonUtil.toObject(responseData, JSXVO.class);
        if (!jsxvo.getCode().equals(CommonConstant.ZERO_ONE)){
            String msg = jsxvo.getMsg();
            if (msg.equals("未查询到数据")){
                return new OutMessage(Status.ZZB_DATA_IS_ERR_MESSAGE);
            }
            return new OutMessage(CommonConstant.THIRTY_THREE_INT, msg,null);
        }
        if (StrUtil.isEmpty(jsxvo.getSecretKey()) || StrUtil.isEmpty(jsxvo.getInfo())){
            return new OutMessage(Status.ZZB_DATA_IS_ERR);
        }
        String realInfo = CryptoUtil.getRealInfo(jsxvo.getInfo(), jsxvo.getSecretKey());
        //解析数据
        cn.hutool.json.JSONArray jsonArray = JSONUtil.parseArray(realInfo);
        List<TransferProcessVO> result = new ArrayList<>();
        Map<String,Object> returnMap=new HashMap<>();
        for (Object o : jsonArray) {
            Object blgc = JSONObject.parseObject(o.toString()).get("BLGC");
            Object dyxxArray = JSONObject.parseObject(o.toString()).get("DYXX");
            cn.hutool.json.JSONArray dyxx = JSONUtil.parseArray(dyxxArray.toString());
            JSONObject jsx = (JSONObject) JSONObject.parseObject(o.toString()).get("JSX");
            for (Object json : dyxx) {
                JSONObject jsonObject = JSONObject.parseObject(json.toString());
                JSONObject c01=null;
                if (jsonObject.containsKey("C01")){
                    c01= jsonObject.getJSONObject("C01");
                }
                if (jsonObject.containsKey("c01")){
                    c01= jsonObject.getJSONObject("c01");
                }
                if (jsonObject.containsKey("A3")){
                    c01= jsonObject.getJSONObject("A3");
                }
                if (ObjectUtil.isNotNull(c01)){
                    String c01002 = c01.getString("C01002");
                    returnMap.put("name",c01002);
                    String c01004 = c01.getString("C01004");
                    returnMap.put("idCard",c01004);
                }
            }
            String uniqueKey = jsx.getString("uniqueKey");
            String jsx0013 = jsx.getString("JSX0013");
            String jsx0017 = jsx.getString("JSX0017");
            returnMap.put("uniqueKey",uniqueKey);
            returnMap.put("srcOrg",jsx0013);
            returnMap.put("traOrg",jsx0017);
            cn.hutool.json.JSONArray array = JSONUtil.parseArray(blgc.toString());
            List<TransferProcessVO> transferProcesses = JSONUtil.toList(array, TransferProcessVO.class);
            List<TransferProcessVO> sortedTransferProcesses = transferProcesses.stream()
                    .peek(item -> {
                        item.setBLGC0012(StrUtil.isNotBlank(item.getBLGC0012()) && StrUtil.equals(item.getBLGC0012(), CommonConstant.ONE) ? "是" : "否");
                        item.setBLGC0006(BLGC_0006_MAPPING.getOrDefault(item.getBLGC0006(), item.getBLGC0006()));
                        item.setBLGC0007(BLGC_0007_MAPPING.getOrDefault(item.getBLGC0007(), item.getBLGC0007()));
                    })
                    // 按照BLGC0011字段倒序排序
                    .sorted(Comparator.comparingInt(TransferProcessVO::getBLGC0011).reversed())
                    .collect(Collectors.toList());
            result.addAll(sortedTransferProcesses);
        }
        returnMap.put("blgc",result);
        return new OutMessage(Status.SUCCESS, returnMap);
    }
    // 定义BLGC0006的映射关系
    private static final Map<String, String> BLGC_0006_MAPPING = new HashMap<String, String>() {
        private static final long serialVersionUID = -813344097306551926L;
        {
            put("0", "审批通过");
            put("1", "审批未通过，退回上一步");
            put("2", "全国业务数据交换区已接收，待下载");
            put("4", "接收党员");
            put("5", "确认报到");
            put("7", "发起方主动撤回，终止");
            put("9", "超时，全国业务数据交换区终止");
            put("12", "撤回上一步，重新办理");
        }
    };

    // 定义BLGC0007的映射关系
    private static final Map<String, String> BLGC_0007_MAPPING = new HashMap<String, String>() {
        private static final long serialVersionUID = -5937692565685922547L;
        {
            put("1", "接收党组织填写错误");
            put("2", "接收党组织不同意接收");
            put("3", "党员信息错误");
            put("4", "不符合有关政策规定");
            put("9", "其他");
        }
    };

    public boolean deailUpdateTransfer(String transferId,String data,Integer status,CallBack saveCallBack,String remark) {
        TransFerExchangeArea updateTransfer = new TransFerExchangeArea();
        updateTransfer.setId(transferId);
        JSONObject jsonDataObject = JSONObject.parseObject(data);
        jsonDataObject.put("status", status);
        updateTransfer.setData(jsonDataObject.toJSONString());
        updateTransfer.setStatus(status);
        updateTransfer.setUpdateTime(new Date());
        String jsonString = JSONObject.toJSONString(updateTransfer);
        updateTransfer.setRemark(remark);
        saveCallBack.setBackData(jsonString);
        return updateById(updateTransfer);
    }


    /**
     * 上传数据统一的catrh处理
     * **/
    private void  catchDeailData(String transferRecordId,String json,Integer uploadDataType){
        OutMessage outMessage = new OutMessage();
        outMessage.setMessage("服务器发送了 HTTP 状态代码 500: null");
        outMessage.setCode(CommonConstant.HUNDRED_INI);
        this.deailWithUploadData(outMessage,transferRecordId,null==outMessage.getData()?"":outMessage.getData().toString(),uploadDataType,json);
    }


}
