package com.zenith.front.service.st.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.entity.model.Org;
import com.zenith.front.entity.model.st.VcTownshipSupportFund;
import com.zenith.front.entity.model.st.VcTownshipSupportMain;
import com.zenith.front.mapper.st.VcTownshipSupportMainMapper;
import com.zenith.front.service.st.VcTownshipSupportMainService;
import com.zenith.front.untils.JsonUtil;
import com.zenith.front.untils.PgUtil;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * vc_township_support_main 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2023-06-13 14:51:19
 */
@Service
public class VcTownshipSupportMainServiceImpl extends ServiceImpl<VcTownshipSupportMainMapper, VcTownshipSupportMain> implements VcTownshipSupportMainService {

    /**
     * 获取数据
     *
     * @param database
     * @param org
     * @return
     */
    @Override
    public List<VcTownshipSupportMain> findAllList(String database, Org org) {
        return PgUtil.executeQuery(database, "select * from vc_township_support_main where delete_time is null  and org_level_code  like ?",
                new Object[]{org.getOrgCode() + "%"})
                .stream().map(v -> JsonUtil.toBean(v, VcTownshipSupportMain.class)).collect(Collectors.toList());
    }
}
