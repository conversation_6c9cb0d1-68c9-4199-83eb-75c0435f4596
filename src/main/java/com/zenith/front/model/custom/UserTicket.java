package com.zenith.front.model.custom;
import com.zenith.front.model.bean.User;
import com.zenith.front.model.bean.UserRolePermission;
import com.zenith.front.model.bean.st.VcSysUser;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * create_time:2019/3/12
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class UserTicket implements Serializable {

    private static final long serialVersionUID = 6583234580161864965L;
    /***
     * 用户对象
     * */
    private User user;
    /***
     * 用户登录token
     * */
    private String token;
    /***
     * 是否是管理员
     * */
    private boolean isAdmin;
    /**
     * 票据生成时间
     */
    private long createTime;
    /**
     * 微信openId
     */
    private String openId;
    /****
     * 微信appId
     * */
    private String appId;
    /***
     * 是否是微信票据
     * */
    private boolean isWeChatToken = false;

    /**
     * 农村党建用户信息
     */
    private VcSysUser vcUser;

    /***
     * 用于权限对象
     * */
    private UserRolePermission userRolePermission;
}
