package com.zenith.front.model.bean;

import java.util.Date;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
public class User {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID,uuid,不能为null
     */
    private String id;

    /**
     * 用户账号,不能为null
     */
    private String account;

    /**
     * 用户密码,不能为null
     */
    private String password;

    /**
     * 用户名称,不能为null
     */
    private String name;

    /**
     * 用户手机号,允许为null
     */
    private String phone;

    /**
     * 角色ID,用户当前角色,不允许为null
     */
    private String currentUserRoleid;

    /**
     * 0 未删除 1已删除 默认为0
     */
    private Integer isDelete;

    /**
     * 0 为锁定 1已锁定 默认为0
     */
    private Integer isLock;

    /**
     * 创建时间,不允许为null
     */
    private Date createTime;

    /**
     * 更新时间,不允许为null,默认值是createTime
     */
    private Date updateTime;

    /**
     * 创建者,只能是account
     */
    private String createAccount;

    /**
     * 组织id
     */
    private String orgId;

    /**
     * 0 不只读 1是只读
     */
    private Integer readOnly;

    /**
     * 用户管理人员id
     */
    private String memCode;

    /**
     * 组织code
     */
    private String orgCode;

    /**
     * 修改者账号
     */
    private String updateAccount;

    /**
     * 最后登录时间
     */
    private Date lastLoginTime;

    /**
     * 模块概况数据
     */
    private Object chart;

    /**
     * 用户openId(渝快政)
     */
    private String openId;

    /**
     * 默认综合党务管理系统 1 村社区管理系统 2 todo 兼容村社区管理系统
     */
    private String managementSystem;

    private Integer type;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getCurrentUserRoleid() {
        return currentUserRoleid;
    }

    public void setCurrentUserRoleid(String currentUserRoleid) {
        this.currentUserRoleid = currentUserRoleid;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getIsLock() {
        return isLock;
    }

    public void setIsLock(Integer isLock) {
        this.isLock = isLock;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateAccount() {
        return createAccount;
    }

    public void setCreateAccount(String createAccount) {
        this.createAccount = createAccount;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public Integer getReadOnly() {
        return readOnly;
    }

    public void setReadOnly(Integer readOnly) {
        this.readOnly = readOnly;
    }

    public String getMemCode() {
        return memCode;
    }

    public void setMemCode(String memCode) {
        this.memCode = memCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    public Date getLastLoginTime() {
        return lastLoginTime;
    }

    public void setLastLoginTime(Date lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }

    public Object getChart() {
        return chart;
    }

    public void setChart(Object chart) {
        this.chart = chart;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getManagementSystem() {
        return managementSystem;
    }

    public void setManagementSystem(String managementSystem) {
        this.managementSystem = managementSystem;
    }

}
