package com.zenith.front.untils;

import com.zenith.front.annotation.DecryptField;
import com.zenith.front.annotation.EnabledDecrypt;
import org.reflections.Reflections;

import java.lang.reflect.Field;
import java.util.*;

/**
 * 扫描实体类包下带有EnabledDecrypt解密的类和具有DecryptField的属性，装配到ENCRYPT_MAP（内网导数据包时解密部分属性值）
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2022/11/7 15:02
 */
public class ReflectionUtil {

    private static final String MODEL_PACKAGE_PATH = "com.zenith.front.entity.model";
    public static final LinkedHashMap<String, LinkedHashSet<String>> ENCRYPT_MAP;

    static {
        ENCRYPT_MAP = scanAnnotationClass();
    }

    public static LinkedHashMap<String, LinkedHashSet<String>> scanAnnotationClass() {
        Reflections reflections = new Reflections(MODEL_PACKAGE_PATH);
        //扫描带有EnabledDecrypt的类
        Set<Class<?>> classesList = reflections.getTypesAnnotatedWith(EnabledDecrypt.class);
        LinkedHashMap<String, LinkedHashSet<String>> mapList = new LinkedHashMap<>(16 * 8);
        for (Class<?> aClass : classesList) {
            final String typeName = aClass.getTypeName();
            final Field[] fields = aClass.getDeclaredFields();
            LinkedHashSet<String> linkedHashSet = new LinkedHashSet<>();
            //扫描带有DecryptField的属性
            Arrays.stream(fields)
                    .filter(field -> Objects.nonNull(field.getAnnotation(DecryptField.class)))
                    .forEach(field -> linkedHashSet.add(field.getName()));
            if (!linkedHashSet.isEmpty()) {
                mapList.put(typeName, linkedHashSet);
            }
        }
        return mapList;
    }
}
