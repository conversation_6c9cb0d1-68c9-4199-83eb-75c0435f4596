package com.zenith.front.untils;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.webservice.esb.ESBUploadAnQuerybusinessServiceImpl;
import com.webservice.util.CryptoUtil;
import com.webservice.util.JackSonUtil;
import com.zenith.front.constant.CommonConstant;
import com.zenith.front.entity.model.TransferProcess;
import com.zenith.front.entity.vo.JSXVO;
import org.springframework.util.Base64Utils;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.webservice.util.CryptoUtil.cnToUnicode;
import static com.webservice.util.CryptoUtil.getQianMingData;
import static java.util.Comparator.comparing;

/**
 * @author: D.watermelon
 * @date: 2022/10/11 0:24
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 */
public  class LetterUntils {

    public static  <T> List<List<T>> subListObject(Integer pageSize, List<T> list) {
        List<List<T>> result = new ArrayList<>();
        int subSize = pageSize;
        int subCount = list.size();
        int subPageTotal = (subCount / subSize) + ((subCount % subSize > 0) ? 1 : 0);
        for (int i = 0, len = subPageTotal - 1; i <= len; i++) {
            int fromIndex = i * subSize;
            int toIndex = ((i == len) ? subCount : ((i + 1) * subSize));
            List<T> strings = list.subList(fromIndex, toIndex);
            result.add(strings);
        }
        return result;
    }

    public static JSONObject lastBlgc(JSONArray blgc ){
        blgc.sort(comparing(obj -> {
            JSONObject object= (JSONObject) obj;
            return object.getInteger("BLGC0011");
        }).reversed());
        return (JSONObject) blgc.get(CommonConstant.ZERO_INT);
    }


    public static boolean isCancel(JSONObject object){
        String blgc0006 = object.getString("BLGC0006");
        if (blgc0006.equals(CommonConstant.NINE)||blgc0006.equals(CommonConstant.ONE)||blgc0006.equals(CommonConstant.SEVEN)){
            return true;
        }
        return false;
    }

    public  static  JSONArray getJSX(String  letterNumber, ESBUploadAnQuerybusinessServiceImpl serviceImplPort){
        JSONArray dataJsonArray =new JSONArray();
        dataJsonArray.add(letterNumber);
        JSONObject postJsonData=new JSONObject();
        postJsonData.put("dataType", CommonConstant.FIVE);
        postJsonData.put("accessID","052000000001");
        postJsonData.put("uniqueKey",dataJsonArray);
        Map<String, String> stringMap = null;
        try {
            stringMap = CryptoUtil.signData(postJsonData.toString(), "1");
        } catch (Exception e) {
            e.printStackTrace();
        }
        String signData = stringMap.get("signData");
        String data = stringMap.get("data");
        String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
        String unicode = cnToUnicode(qianMingData);
        String resultStr =null;
        try {
            resultStr = serviceImplPort.query(unicode);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StrUtil.isEmpty(resultStr)){
            return null;
        }
        String responseData = null;
        responseData = new String(Base64Utils.decode(resultStr.getBytes()), StandardCharsets.UTF_8);
        JSXVO jsxvo = JackSonUtil.toObject(responseData, JSXVO.class);
        if (jsxvo.getSecretKey()==null){
            return null;
        }
        String realInfo = null;
        try {
            realInfo = CryptoUtil.getRealInfo(jsxvo.getInfo(), jsxvo.getSecretKey());
        } catch (Exception e) {
            e.printStackTrace();
        }
        JSONArray jsonArray = JSONArray.parseArray(realInfo);
        return jsonArray;
    }


    public static  TransferProcess endTransferProcess(JSONArray jsonArray, String letterNumber){
        List<TransferProcess> transferProcessList =new ArrayList<>();
        jsonArray.forEach(jsonObject->{
            JSONObject dataObject= (JSONObject) jsonObject;
            JSONArray blgcJsonArray = dataObject.getJSONArray("BLGC");
            if (dataObject.getString("uniqueKey").equals(letterNumber)){
                blgcJsonArray.forEach(blgcJson->{
                    JSONObject blgcJsonObject= (JSONObject) blgcJson;
                    TransferProcess transferProcess = new TransferProcess();
                    transferProcess.setBlgc0001(blgcJsonObject.getString("BLGC0001"));
                    transferProcess.setBlgc0002(blgcJsonObject.getString("BLGC0002"));
                    transferProcess.setBlgc0003(blgcJsonObject.getString("BLGC0003"));
                    transferProcess.setBlgc0004(blgcJsonObject.getString("BLGC0004"));
                    transferProcess.setBlgc0005(blgcJsonObject.getString("BLGC0005"));
                    transferProcess.setBlgc0006(blgcJsonObject.getString("BLGC0006"));
                    transferProcess.setBlgc0007(blgcJsonObject.getString("BLGC0007"));
                    transferProcess.setBlgc0008(blgcJsonObject.getString("BLGC0008"));
                    transferProcess.setBlgc0009(blgcJsonObject.getString("BLGC0009"));
                    transferProcess.setBlgc0010(blgcJsonObject.getString("BLGC0010"));
                    transferProcess.setBlgc0011(blgcJsonObject.getInteger("BLGC0011"));
                    transferProcess.setBlgc0012(blgcJsonObject.getString("BLGC0012"));
                    transferProcessList.add(transferProcess);
                });
            }
        });
        transferProcessList.sort(comparing(TransferProcess::getBlgc0011).reversed());
        TransferProcess transferProcess = transferProcessList.get(CommonConstant.ZERO_INT);
        return transferProcess;
    }

}
