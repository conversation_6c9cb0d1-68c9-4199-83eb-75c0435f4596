package com.zenith.front.dataexchange;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.ssh.SshjSftp;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.dataformat.xml.XmlFactory;
import com.zenith.front.common.OutMessage;
import com.zenith.front.common.SqlCommon;
import com.zenith.front.common.Status;
import com.zenith.front.config.InternalProperties;
import com.zenith.front.constant.CommonConstant;
import com.zenith.front.constant.FileConstant;
import com.zenith.front.controller.base.BaseController;
import com.zenith.front.entity.model.*;
import com.zenith.front.enums.XmlNodeExtendEnum;
import com.zenith.front.mapper.DataExchangeMapper;
import com.zenith.front.service.dataexchange.api.*;
import com.zenith.front.service.devops.IDevOpsService;
import com.zenith.front.service.dynamic.DynamicService;
import com.zenith.front.service.flow.MemFlowSign2Service;
import com.zenith.front.service.flow.MemFlowSignAuditService;
import com.zenith.front.service.flow.OrgFlow2Service;
import com.zenith.front.service.flow.OrgFlowAudit2Service;
import com.zenith.front.service.memSpection.IMemFlowInspectionFormService;
import com.zenith.front.service.st.StIService;
import com.zenith.front.service.st.VcPleasDestroyHolidayService;
import com.zenith.front.untils.*;
import lombok.SneakyThrows;
import net.lingala.zip4j.ZipFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.File;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Service
public class ExchangeXmlServiceImpl implements IExchangeXmlService {
    @Resource
    private JdbcTemplate jdbcTemplate;

    private static final Logger log = LoggerFactory.getLogger(ExchangeXmlServiceImpl.class);
    /**
     * 列出所有数据库（不包含模板和postgres）
     */
    private static final String DAT_NAME_SQL = "SELECT datname FROM pg_database WHERE datistemplate = FALSE AND datname <> 'postgres' ORDER BY datname DESC;";
    private static final String sql1 = "SELECT * FROM ccp_org WHERE LENGTH ( org_code ) <= 6 ORDER BY org_code;";
    private static final String sql2 = "SELECT * FROM ccp_org_all WHERE LENGTH ( org_code ) <= 6 ORDER BY org_code;";
    private static final String sql3 = "SELECT * FROM ccp_org_committee_elect WHERE elect_org_code in (SELECT org_code FROM ccp_org WHERE LENGTH ( org_code ) <= 6 ORDER BY org_code);";
    private static final String sql4 = "SELECT * FROM ccp_org_committee WHERE elect_code in (SELECT code FROM ccp_org_committee_elect WHERE elect_org_code in (SELECT org_code FROM ccp_org WHERE LENGTH ( org_code ) <= 6 ORDER BY org_code));";
    private static final String sql5 = "SELECT * from ccp_org_reward WHERE reward_org_code in (SELECT org_code FROM ccp_org WHERE LENGTH ( org_code ) <= 6 ORDER BY org_code);";
    private static final String sql6 = "SELECT * FROM ccp_org_group WHERE group_org_code in (SELECT org_code FROM ccp_org WHERE LENGTH ( org_code ) <= 6 ORDER BY org_code);";
    private static final String sql7 = "SELECT * from ccp_org_group_member WHERE group_code in (SELECT code FROM ccp_org_group WHERE group_org_code in (SELECT org_code FROM ccp_org WHERE LENGTH ( org_code ) <= 6 ORDER BY org_code));";
    private static final String sql8 = "SELECT * from ccp_org_recognition WHERE recognition_org_code in (SELECT org_code FROM ccp_org WHERE LENGTH ( org_code ) <= 6 ORDER BY org_code);";
    private static final String sql9 = "SELECT * from ccp_org_recognition_data WHERE recognition_code in (SELECT code from ccp_org_recognition WHERE recognition_org_code in (SELECT org_code FROM ccp_org WHERE LENGTH ( org_code ) <= 6 ORDER BY org_code));";
    private static final String sql10 = "SELECT * from ccp_org_recognition_all WHERE org_level_code in (SELECT org_code FROM ccp_org WHERE LENGTH ( org_code ) <= 6 ORDER BY org_code);";
    private static final String sql11 = "SELECT * from ccp_org_recognition_situation WHERE situation_org_code in (SELECT org_code FROM ccp_org WHERE LENGTH ( org_code ) <= 6 ORDER BY org_code);";
    private static final String sql12 = "SELECT * from ccp_org_caucus WHERE caucus_org_code in (SELECT org_code FROM ccp_org WHERE LENGTH ( org_code ) <= 6 ORDER BY org_code);";
    private static final String sql13 = "SELECT * from ccp_org_party_congress_elect WHERE elect_org_code in (SELECT org_code FROM ccp_org WHERE LENGTH ( org_code ) <= 6 ORDER BY org_code);";
    private static final String sql14 = "SELECT * from ccp_org_party_congress_committee WHERE elect_code in (SELECT code from ccp_org_party_congress_elect WHERE elect_org_code in (SELECT org_code FROM ccp_org WHERE LENGTH ( org_code ) <= 6 ORDER BY org_code));";
    private static final String sql15 = "SELECT * from ccp_org_party_congress_committee_all WHERE elect_code in (SELECT code from ccp_org_party_congress_elect WHERE elect_org_code in (SELECT org_code FROM ccp_org WHERE LENGTH ( org_code ) <= 6 ORDER BY org_code));";
    private static final String sql16 = "SELECT * from ccp_mem_train WHERE org_org_code in (SELECT org_code FROM ccp_org WHERE LENGTH ( org_code ) <= 6 ORDER BY org_code);";
    private static final String sql17 = "SELECT * from ccp_org_non_public_party WHERE org_level_code in (SELECT org_code FROM ccp_org WHERE LENGTH ( org_code ) <= 6 ORDER BY org_code);";
    private static final String sql18 = "SELECT * from ccp_org_appraisal WHERE org_code in (SELECT code FROM ccp_org WHERE LENGTH ( org_code ) <= 6 ORDER BY org_code);";
    private static final String sql19 = "SELECT * from ccp_org_reviewers WHERE appraisal_code in (SELECT code from ccp_org_appraisal WHERE org_code in (SELECT code FROM ccp_org WHERE LENGTH ( org_code ) <= 6 ORDER BY org_code));";
    private static final String sql20 = "SELECT * from ccp_org_township_leadership WHERE org_code in (SELECT code FROM ccp_org WHERE LENGTH ( org_code ) <= 6 ORDER BY org_code);";
    private static final String sql21 = "SELECT * from sys_user WHERE is_delete='0' ORDER BY create_time;";
    private static final String sql22 = "SELECT * FROM sys_user_role_permission WHERE user_id in (SELECT id from sys_user WHERE is_delete='0');";

    @Resource
    IDemocraticReviewService democraticReviewService;
    @Resource
    IDemocraticReviewLeadService democraticReviewLeadService;
    @Resource
    IDemocraticReviewMemService democraticReviewMemService;
    @Resource
    IDevelopPlanLogService developPlanLogService;
    @Resource
    IDevelopPlanService developPlanService;
    @Resource
    IDevelopStepLogAllService developStepLogAllService;
    @Resource
    IDevelopStepLogService developStepLogService;
    @Resource
    ILockFiledLogService lockFiledLogService;
    @Resource
    ILogicCheckIgnoreService logicCheckIgnoreService;
    @Resource
    IMemAbroadService memAbroadService;
    @Resource
    IMemAllInfoService memAllInfoService;
    @Resource
    IMemDevelopAllService memDevelopAllService;
    @Resource
    IMemDevelopService memDevelopService;
    @Resource
    IMemDevelopOperationService memDevelopOperationService;
    @Resource
    IMemDifficultService memDifficultService;
    @Resource
    IMemExtendService memExtendService;
    @Resource
    IMemFlow1AllService memFlow1AllService;
    @Resource
    IMemFlowLogService memFlowLogService;
    @Resource
    IMemFlow1Service memFlow1Service;
    @Resource
    IMemHistoryService memHistoryService;
    @Resource
    IMemLogService memLogService;
    @Resource
    IMemManyService memManyService;
    @Resource
    IMemService memService;
    @Resource
    IMemReportService memReportService;
    @Resource
    IMemRewardAllService memRewardAllService;
    @Resource
    IMemRewardService memRewardService;
    @Resource
    IMemTrainInfoService memTrainInfoService;
    @Resource
    IMemTrainService memTrainService;
    @Resource
    IOrgAllService orgAllService;
    @Resource
    IOrgAppraisalService orgAppraisalService;
    @Resource
    IOrgAssessService orgAssessService;
    @Resource
    IOrgCaucusService orgCaucusService;
    @Resource
    IOrgCommitteeElectService orgCommitteeElectService;
    @Resource
    IOrgCommitteeService orgCommitteeService;
    @Resource
    IOrgDevelopRightsService orgDevelopRightsService;
    @Resource
    IOrgExtendService orgExtendService;
    @Resource
    IOrgGroupService orgGroupService;
    @Resource
    IOrgGroupMemberService orgGroupMemberService;
    @Resource
    IOrgHistoryService orgHistoryService;
    @Resource
    IOrgIndustryAllService orgIndustryAllService;
    @Resource
    IOrgIndustryService orgIndustryService;
    @Resource
    IOrgService orgService;
    @Resource
    IOrgNonPublicPartyService orgNonPublicPartyService;
    @Resource
    IOrgPartyCongressCommitteeAllService orgPartyCongressCommitteeAllService;
    @Resource
    IOrgPartyCongressCommitteeService orgPartyCongressCommitteeService;
    @Resource
    IOrgPartyCongressElectService orgPartyCongressElectService;
    @Resource
    IOrgPartyService orgPartyService;
    @Resource
    IOrgRecognitionAllService orgRecognitionAllService;
    @Resource
    IOrgRecognitionDataService orgRecognitionDataService;
    @Resource
    IOrgRecognitionService orgRecognitionService;
    @Resource
    IOrgRecognitionSituationService orgRecognitionSituationService;
    @Resource
    IOrgReportService orgReportService;
    @Resource
    IOrgReviewersService orgReviewersService;
    @Resource
    IOrgRewardService orgRewardService;
    @Resource
    IOrgSlackAllService orgSlackAllService;
    @Resource
    IOrgSlackService orgSlackService;
    @Resource
    IOrgSlackRectificationService orgSlackRectificationService;
    @Resource
    IOrgSpecialNatureService orgSpecialNatureService;
    @Resource
    IOrgTownshipLeadershipService orgTownshipLeadershipService;
    @Resource
    IRepresentativeContactService representativeContactService;
    @Resource
    IRepresentativeElectService representativeElectService;
    @Resource
    IRepresentativeService representativeService;
    @Resource
    ITransferApprovalService transferApprovalService;
    @Resource
    ITransferEffectMemsService transferEffectMemsService;
    @Resource
    ITransferLogService transferLogService;
    @Resource
    ITransferRecordService transferRecordService;
    @Resource
    IUnitAllService unitAllService;
    @Resource
    IUnitCitySituationService unitCitySituationService;
    @Resource
    IUnitCollectiveEconomicService unitCollectiveEconomicService;
    @Resource
    IUnitCommitteeElectService unitCommitteeElectService;
    @Resource
    IUnitCommitteeService unitCommitteeService;
    @Resource
    IUnitCommunityService unitCommunityService;
    @Resource
    IUnitCountrusideService unitCountrysideService;
    @Resource
    IUnitExpandCollectiveEconomyService unitExpandCollectiveEconomyService;
    @Resource
    IUnitExtendService unitExtendService;
    @Resource
    IUnitIncomeService unitIncomeService;
    @Resource
    IUnitService unitService;
    @Resource
    IUnitOrgLinkedService unitOrgLinkedService;
    @Resource
    IUnitReportService unitReportService;
    @Resource
    IUnitResidentService unitResidentService;
    @Resource
    IUnitSecondaryService unitSecondaryService;
    @Resource
    IUnitSiteConditionsService unitSiteConditionsService;
    @Resource
    IUnitStreetsCadresService unitStreetsCadresService;
    @Resource
    IUnitTownshipCadreInfoService unitTownshipCadreInfoService;
    @Resource
    IActivistTransferApprovalService iActivistTransferApprovalService;
    @Resource
    IActivistTransferLogService iActivistTransferLogService;
    @Resource
    IActivistTransferRecordService iActivistTransferRecordService;
    @Resource
    ITransferStaticsService transferStaticsService;
    @Resource
    IUserService userService;
    @Resource
    IRoleService roleService;
    @Resource
    IUserRolePermissionService userRolePermissionService;
    @Resource
    Map<String, StIService> stIServiceMap;
    @Value("${unzip.password}")
    private String password;
    @Resource
    private DataExchangeMapper dataExchangeMapper;
    @Resource
    private Executor mySimpleAsync;
    @Resource
    private DynamicService dynamicService;
    @Resource
    private VcPleasDestroyHolidayService vcPleasDestroyHolidayService;
    @Autowired
    private IDevOpsService devOpsService;
    @Resource
    private IOrgContactMemService orgContactMemService;
    @Resource
    private IOrgExampleSiteService orgExampleSiteService;
    @Resource
    private IOrgContactWorkService orgContactWorkService;
    @Resource
    OrgFlow2Service orgFlow2Service;
    @Resource
    OrgFlowAudit2Service orgFlowAudit2Service;
    @Resource
    MemFlowSignAuditService memFlowSignAuditService;
    @Resource
    MemFlowSign2Service memFlowSign2Service;
    @Resource
    IMemFlow2Service iMemFlow2Service;
    @Resource
    IMemFlowInspectionFormService iMemFlowInspectionFormService;
    @Resource
    IMemDigitalService iMemDigitalService;
    @Resource
    IMemDigitalCountService iMemDigitalCountService;
    @Resource
    IMemDevelopProcessService iMemDevelopProcessService;
    @Resource
    IMemDigitalOperationLogService iMemDigitalOperationLogService;
    @Resource
    IMemDigitalAuditService iMemDigitalAuditService;


    @Override
    public void exportXml(String database, String nginxKey, String randomUUID, File folder, Set<String> orgLevelCodeSet, Map<String, Boolean> isVcMap) throws Exception {
        log.info("查找机构信息");
        List<Map<String, Object>> orgListMap = PgUtil.executeQuery(database, "SELECT * FROM \"ccp_org\" WHERE \"length\" ( org_code ) = 9 AND delete_time IS NULL AND ( is_dissolve IS NULL OR is_dissolve <> 1 ) ORDER BY org_code", null);
        if (CollUtil.isEmpty(orgListMap)) {
            return;
        }
        // 是否存在农村党建表 true 存在
        boolean flg = false;
        try {
            List<Map<String, Object>> ifExist = PgUtil.executeQuery(database, "select * from information_schema.tables where table_type='BASE TABLE' and table_name='vc_sys_user'", null);
            flg = CollUtil.isNotEmpty(ifExist);
            log.info("{}是否存在农村党建表：{}", database, flg);
        } catch (Exception ignored) {
            log.error(ignored.getMessage());
        }
        isVcMap.put(database, flg);
        List<Org> orgList = orgListMap.stream().map(v -> JsonUtil.toBean(v, Org.class)).collect(Collectors.toList());
        XmlDataExportServiceImpl xmlDataExportService = new XmlDataExportServiceImpl();
        for (Org org : orgList) {
            log.info("开始导出机构：{}", org.getName());
            orgLevelCodeSet.add(org.getOrgCode());

            CompletableFuture<List<Map<String, Object>>> orgExampleSiteFuture = CompletableFuture.supplyAsync(() ->
                            // PgUtil.executeQuery(database, "select * from ccp_org_example_site where delete_time is null ", null)
                            PgUtil.executeQuery(database, "SELECT ccp_org_example_site.* FROM \"ccp_org_example_site\" left join \"ccp_org\" on \"ccp_org_example_site\".org_code = \"ccp_org\".code where \"ccp_org_example_site\".delete_time is null  and  \"ccp_org\".org_code like '" + org.getOrgCode() + "%'", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgContactWorkFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select * from ccp_org_contact_work where org_level_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgContactMemFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select * from ccp_org_contact_mem where org_level_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> democraticReviewFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select * from ccp_democratic_review where review_org_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> democraticReviewLeadFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select * from ccp_democratic_review_lead where lead_org_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> democraticReviewMemFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select * from ccp_democratic_review_mem where review_mem_org_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<DevelopPlan>> developPlanFuture = CompletableFuture.supplyAsync(() ->
                            xmlDataExportService.getDevelopPlan(database, org)
                    , mySimpleAsync);

            CompletableFuture<List<DevelopPlanLog>> developPlanLogFuture = CompletableFuture.supplyAsync(() ->
                            xmlDataExportService.getDevelopPlanLog(database, org)
                    , mySimpleAsync);

            CompletableFuture<List<DevelopStepLog>> developStepLogFuture = CompletableFuture.supplyAsync(() ->
                            xmlDataExportService.getDevelopStepLog(database, org)
                    , mySimpleAsync);

            CompletableFuture<List<DevelopStepLogAll>> developStepLogAllFuture = CompletableFuture.supplyAsync(() ->
                            xmlDataExportService.getDevelopStepLogAll(database, org)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> lockFiledLogFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "SELECT * FROM \"ccp_lock_filed_log\" WHERE level_code like '" + org.getOrgCode() + "%'", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> memFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select * from ccp_mem where d08_code IN ( '1', '2' )  and mem_org_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> memHistoryFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "SELECT * FROM ccp_mem WHERE d08_code IN ( '1', '2' ) AND mem_org_code LIKE '" + org.getOrgCode() + "%' AND delete_time > '2021-01-01' AND ( d12_code is not null OR d50_code IS NOT NULL )", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> memAbroadFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select * from ccp_mem_abroad where abroad_org_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> memAllFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select * from ccp_mem_all where d08_code IN ( '1', '2', '3', '4', '5', '50') and mem_org_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> memAll1Future = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select * from ccp_mem_all where d08_code IN ( '1', '2') and mem_org_code like '" + org.getOrgCode() + "%' and delete_time is not null and is_transfer = 1", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> memHistoryAllFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "SELECT * FROM ccp_mem_all WHERE d08_code IN ( '1', '2' ) AND mem_org_code LIKE '" + org.getOrgCode() + "%' AND delete_time > '2021-01-01' AND ( d12_code IS NOT NULL OR d50_code IS NOT NULL )", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> memDevelopFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select * from ccp_mem_develop where d08_code IN ( '3', '4', '5' ) and develop_org_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> memDevelopAllFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select * from ccp_mem_develop_all where d08_code IN ( '3', '4', '5' ) and develop_org_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> memDevelopOperationFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "SELECT\n" +
                                    "\tccp_mem_develop_operation.* \n" +
                                    "FROM\n" +
                                    "\tccp_mem_develop_operation\n" +
                                    "\tLEFT JOIN ccp_mem_develop ON ccp_mem_develop_operation.develop_code = ccp_mem_develop.code \n" +
                                    "\tAND ccp_mem_develop.delete_time IS NULL \n" +
                                    "WHERE\n" +
                                    "\tccp_mem_develop.develop_org_code LIKE '" + org.getOrgCode() + "%' \n" +
                                    "\tAND ccp_mem_develop_operation.delete_time IS NULL ", null)
                    , mySimpleAsync);

            CompletableFuture<List<MemDifficult>> memDifficultFuture = CompletableFuture.supplyAsync(() ->
                            xmlDataExportService.getMemDifficult(database, org)
                    , mySimpleAsync);

            CompletableFuture<List<MemExtend>> memExtendFuture = CompletableFuture.supplyAsync(() ->
                            xmlDataExportService.getMemExtend(database, org)
                    , mySimpleAsync);

            CompletableFuture<List<MemFlowLog>> memFlowLogFuture = CompletableFuture.supplyAsync(() ->
                            xmlDataExportService.getMemFlowLog(database, org)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> ccpMemHistoryFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "SELECT * FROM ccp_mem_history WHERE mem_org_code LIKE '" + org.getOrgCode() + "%'", null)
                    , mySimpleAsync);

            CompletableFuture<List<MemLog>> memLogFuture = CompletableFuture.supplyAsync(() ->
                            xmlDataExportService.getMemLog(database, org)
                    , mySimpleAsync);

            CompletableFuture<List<MemMany>> memManyFuture = CompletableFuture.supplyAsync(() ->
                            xmlDataExportService.getMemMany(database, org)
                    , mySimpleAsync);

            CompletableFuture<List<MemReport>> memReportFuture = CompletableFuture.supplyAsync(() ->
                            xmlDataExportService.getMemReport(database, org)
                    , mySimpleAsync);

            CompletableFuture<List<MemReward>> memRewardFuture = CompletableFuture.supplyAsync(() ->
                            xmlDataExportService.getMemReward(database, org)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> memRewardAllFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select * from ccp_mem_reward_all where mem_org_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<MemTrain>> memTrainFuture = CompletableFuture.supplyAsync(() ->
                            xmlDataExportService.getMemTrain(database, org)
                    , mySimpleAsync);

            CompletableFuture<List<MemTrainInfo>> memTrainInfoFuture = CompletableFuture.supplyAsync(() ->
                            xmlDataExportService.getMemTrainInfo(database, org)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select  * from ccp_org where ccp_org.org_code LIKE '" + org.getOrgCode() + "%' and delete_time is null AND ( is_dissolve IS NULL OR is_dissolve <> 1 ) ORDER BY org_code", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgAllFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select  * from ccp_org_all where ccp_org_all.org_code LIKE '" + org.getOrgCode() + "%' and delete_time is null AND ( is_dissolve IS NULL OR is_dissolve <> 1 ) ORDER BY org_code", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgAppraisalFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "SELECT\n" +
                                    "\tccp_org_appraisal.* \n" +
                                    "FROM\n" +
                                    "\tccp_org_appraisal\n" +
                                    "\tLEFT JOIN ccp_org ON ccp_org_appraisal.org_code = ccp_org.code \n" +
                                    "\tAND ccp_org.delete_time IS NULL \n" +
                                    "WHERE\n" +
                                    "\tccp_org.org_code LIKE '" + org.getOrgCode() + "%' \n" +
                                    "\tAND ccp_org_appraisal.delete_time IS NULL AND year > '2020-01-01'", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgAssessesFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "SELECT\n" +
                                    "\tccp_org_assess.* \n" +
                                    "FROM\n" +
                                    "\tccp_org_assess\n" +
                                    "\tLEFT JOIN ccp_org ON ccp_org_assess.org_code = ccp_org.code \n" +
                                    "\tAND ccp_org.delete_time IS NULL \n" +
                                    "WHERE\n" +
                                    "\tccp_org.org_code LIKE '" + org.getOrgCode() + "%' \n" +
                                    "\tAND ccp_org_assess.delete_time IS NULL ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgCaucusFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select * from ccp_org_caucus where caucus_org_code like '" + org.getOrgCode() + "%' and ccp_org_caucus.delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgCommitteeFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select * from ccp_org_committee where position_org_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgCommitteeElectFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select * from ccp_org_committee_elect where elect_org_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgDevelopRightsFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select * from ccp_org_develop_rights where org_level_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgExtendFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "SELECT\n" +
                                    "\tccp_org_extend.* \n" +
                                    "FROM\n" +
                                    "\tccp_org_extend\n" +
                                    "\tLEFT JOIN ccp_org ON ccp_org_extend.org_code = ccp_org.code \n" +
                                    "\tAND ccp_org.delete_time IS NULL \n" +
                                    "WHERE\n" +
                                    "\tccp_org.org_code LIKE '" + org.getOrgCode() + "%' \n" +
                                    "\tAND ccp_org_extend.delete_time IS NULL  AND ( is_dissolve IS NULL OR is_dissolve <> 1 ) ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgGroupFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select * from ccp_org_group where group_org_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgGroupMemberFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "SELECT\n" +
                                    "\tccp_org_group_member.* \n" +
                                    "FROM\n" +
                                    "\tccp_org_group_member\n" +
                                    "\tLEFT JOIN ccp_org_group ON ccp_org_group_member.group_code = ccp_org_group.code \n" +
                                    "\tAND ccp_org_group.delete_time IS NULL \n" +
                                    "WHERE\n" +
                                    "\tgroup_org_code LIKE '" + org.getOrgCode() + "%' \n" +
                                    "\tAND ccp_org_group_member.delete_time IS NULL ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgHistoryFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "SELECT * FROM ccp_org_history WHERE org_code LIKE '" + org.getOrgCode() + "%' ORDER BY org_code", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgIndustriesFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select * from ccp_org_industry where industry_org_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgIndustryAllFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select * from ccp_org_industry_all where industry_org_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgNonPublicPartiesFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select * from ccp_org_non_public_party where org_level_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgPartyFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select * from ccp_org_party where party_org_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgPartyCongressCommitteeFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "SELECT * from ccp_org_party_congress_committee WHERE position_org_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgPartyCongressCommitteeAllFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "SELECT * from ccp_org_party_congress_committee_all WHERE position_org_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgPartyCongressElectFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "SELECT * from ccp_org_party_congress_elect WHERE elect_org_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgRecognitionFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "SELECT * from ccp_org_recognition WHERE recognition_org_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgRecognitionAllFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "SELECT * from ccp_org_recognition_all WHERE org_level_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgRecognitionDataFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "SELECT\n" +
                                    "\tccp_org_recognition_data.* \n" +
                                    "FROM\n" +
                                    "\tccp_org_recognition_data\n" +
                                    "\tLEFT JOIN ccp_org_recognition ON ccp_org_recognition.code = ccp_org_recognition_data.recognition_code \n" +
                                    "\tAND ccp_org_recognition.delete_time IS NULL \n" +
                                    "WHERE\n" +
                                    "\trecognition_org_code LIKE '" + org.getOrgCode() + "%' \n" +
                                    "\tAND ccp_org_recognition_data.delete_time IS NULL ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgRecognitionSituationFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select * from ccp_org_recognition_situation where situation_org_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgReportFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "SELECT ccp_org_report.* " +
                                    " FROM ccp_org_report " +
                                    " LEFT JOIN ccp_org ON ccp_org.code = ccp_org_report.report_org_code" +
                                    " WHERE ccp_org_report.delete_time IS NULL AND ccp_org.org_code LIKE '" + org.getOrgCode() + "%'", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgReviewersFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "SELECT " +
                                    " ccp_org_reviewers.*  " +
                                    "FROM " +
                                    " ccp_org_reviewers " +
                                    " LEFT JOIN ccp_org ON ccp_org_reviewers.org_code = ccp_org.code " +
                                    " LEFT JOIN ccp_org_appraisal ON ccp_org_reviewers.appraisal_code = ccp_org_appraisal.code  " +
                                    "WHERE " +
                                    " ccp_org_reviewers.delete_time IS NULL  " +
                                    " AND ccp_org_appraisal.\"year\" > '2020-01-01'  " +
                                    " AND ccp_org_appraisal.delete_time IS NULL  " +
                                    " AND ccp_org.delete_time IS NULL  " +
                                    " AND ccp_org.org_code LIKE '" + org.getOrgCode() + "%'", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgRewardFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select * from ccp_org_reward where reward_org_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgSlackFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select * from ccp_org_slack where slack_org_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgSlackAllFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select * from ccp_org_slack_all where slack_org_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgSlackRectificationFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select * from ccp_org_slack_rectification where slack_org_code like '" + org.getOrgCode() + "%' ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> orgSpecialNatureFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select * from ccp_org_special_nature where special_org_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<OrgTownshipLeadership>> orgTownshipLeadershipFuture = CompletableFuture.supplyAsync(() ->
                            xmlDataExportService.getOrgTownshipLeadership(database, org)
                    , mySimpleAsync);

            CompletableFuture<List<Representative>> representativeFuture = CompletableFuture.supplyAsync(() ->
                            xmlDataExportService.getRepresentative(database, org)
                    , mySimpleAsync);

            CompletableFuture<List<RepresentativeContact>> representativeContactFuture = CompletableFuture.supplyAsync(() ->
                            xmlDataExportService.getRepresentativeContact(database, org)
                    , mySimpleAsync);

            CompletableFuture<List<RepresentativeElect>> representativeElectFuture = CompletableFuture.supplyAsync(() ->
                            xmlDataExportService.getRepresentativeElect(database, org)
                    , mySimpleAsync);

            CompletableFuture<List<TransferApproval>> transferApprovalFuture = CompletableFuture.supplyAsync(() ->
                            xmlDataExportService.getTransferApproval(database, org)
                    , mySimpleAsync);

            CompletableFuture<List<TransferEffectMems>> transferEffectMemsFuture = CompletableFuture.supplyAsync(() ->
                            xmlDataExportService.getTransferEffectMems(database, org)
                    , mySimpleAsync);

            CompletableFuture<List<TransferLog>> transferLogFuture = CompletableFuture.supplyAsync(() ->
                            xmlDataExportService.getTransferLog(database, org)
                    , mySimpleAsync);

            CompletableFuture<List<TransferRecord>> transferRecordFuture = CompletableFuture.supplyAsync(() ->
                            xmlDataExportService.getTransferRecord(database, org)
                    , mySimpleAsync);

            String selectUnitSql = "SELECT * from ccp_unit WHERE (create_unit_org_code like '{}%' OR ((create_unit_org_code is null OR create_unit_org_code='' or length(create_unit_org_code) < 9) AND main_unit_org_code like '{}%') OR ((create_unit_org_code is null OR create_unit_org_code='') AND (main_unit_org_code is null OR main_unit_org_code='') AND manage_unit_org_code like '{}%')) and delete_time is null";
            String selectUnitAllSql = "SELECT * from ccp_unit_all WHERE (create_unit_org_code like '{}%' OR ((create_unit_org_code is null OR create_unit_org_code='' or length(create_unit_org_code) < 9) AND main_unit_org_code like '{}%') OR ((create_unit_org_code is null OR create_unit_org_code='') AND (main_unit_org_code is null OR main_unit_org_code='') AND manage_unit_org_code like '{}%')) and delete_time is null";
            CompletableFuture<List<Map<String, Object>>> unitFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, StrUtil.replace(selectUnitSql, "{}", org.getOrgCode()), null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> unitAllFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, StrUtil.replace(selectUnitAllSql, "{}", org.getOrgCode()), null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> unitCitySituationFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "SELECT * FROM \"ccp_unit_city_situation\" WHERE delete_time IS NULL AND unit_code IN ( SELECT code FROM \"ccp_unit\" WHERE delete_time IS NULL AND ( manage_org_code LIKE '" + org.getOrgCode() + "%' OR main_unit_org_code LIKE '" + org.getOrgCode() + "%' OR create_unit_org_code LIKE '" + org.getOrgCode() + "%' ) )", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> unitCollectiveEconomicFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select * from ccp_unit_collective_economic where economic_org_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> unitCommitteeFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select\n" +
                                    "\tccp_unit_committee.*\n" +
                                    "from\n" +
                                    "\tccp_unit_committee\n" +
                                    "left join ccp_unit_committee_elect on\n" +
                                    "\tccp_unit_committee.elect_code = ccp_unit_committee_elect.code and ccp_unit_committee_elect.delete_time is null\n" +
                                    "left join ccp_unit on ccp_unit.code =ccp_unit_committee_elect.unit_code and ccp_unit.delete_time is null\n" +
                                    "where ccp_unit.create_unit_org_code like '" + org.getOrgCode() + "%' and ccp_unit_committee.delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> unitCommitteeElectFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select\n" +
                                    "\tccp_unit_committee_elect.*\n" +
                                    "from\n" +
                                    "\tccp_unit_committee_elect\n" +
                                    "left join ccp_unit on ccp_unit.code =ccp_unit_committee_elect.unit_code and ccp_unit.delete_time is null\n" +
                                    "where ccp_unit.create_unit_org_code like '" + org.getOrgCode() + "%' and ccp_unit_committee_elect.delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> unitCommunityFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "select\n" +
                                    "\tccp_unit_community.*\n" +
                                    "from\n" +
                                    "\tccp_unit_community\n" +
                                    "left join ccp_unit on ccp_unit.code =ccp_unit_community.unit_code and ccp_unit.delete_time is null\n" +
                                    "where ccp_unit.create_unit_org_code like '" + org.getOrgCode() + "%' and ccp_unit_community.delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> unitCountrysideFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "SELECT\n" +
                                    "\tccp_unit_countryside.* \n" +
                                    "FROM\n" +
                                    "\tccp_unit_countryside\n" +
                                    "\tLEFT JOIN ccp_unit ON ccp_unit_countryside.unit_code = ccp_unit.code \n" +
                                    "\tAND ccp_unit.delete_time IS NULL \n" +
                                    "WHERE\n" +
                                    "\tccp_unit.create_unit_org_code LIKE '" + org.getOrgCode() + "%'", null)
                    , mySimpleAsync);

            CompletableFuture<List<UnitExpandCollectiveEconomy>> unitExpandCollectiveEconomyFuture = CompletableFuture.supplyAsync(() ->
                            xmlDataExportService.getUnitExpandCollectiveEconomy(database, org)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> unitExtendFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "SELECT ccp_unit_extend.* from ccp_unit_extend inner join ccp_unit on ccp_unit_extend.code=ccp_unit.code WHERE ccp_unit.create_unit_org_code like '" + org.getOrgCode() + "%' and ccp_unit.delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> unitIncomeFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "SELECT\n" +
                                    "\tccp_unit_income.* \n" +
                                    "FROM\n" +
                                    "\tccp_unit_income\n" +
                                    "\tLEFT JOIN ccp_unit_collective_economic ON ccp_unit_income.economic_code = ccp_unit_collective_economic.code \n" +
                                    "\tAND ccp_unit_collective_economic.delete_time IS NULL \n" +
                                    "WHERE\n" +
                                    "\tccp_unit_collective_economic.economic_org_code LIKE '" + org.getOrgCode() + "%' \n" +
                                    "\tAND ccp_unit_income.delete_time IS NULL ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> unitOrgLinkedFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "SELECT * from ccp_unit_org_linked WHERE linked_org_code like '" + org.getOrgCode() + "%' and delete_time is null ", null)
                    , mySimpleAsync);

            CompletableFuture<List<UnitReport>> unitReportFuture = CompletableFuture.supplyAsync(() ->
                            xmlDataExportService.getUnitReport(database, org)
                    , mySimpleAsync);

            CompletableFuture<List<UnitResident>> unitResidentFuture = CompletableFuture.supplyAsync(() ->
                            xmlDataExportService.getUnitResident(database, org)
                    , mySimpleAsync);

//            CompletableFuture<List<Map<String, Object>>> unitSecondaryFuture = CompletableFuture.supplyAsync(() ->
//                            PgUtil.executeQuery(database, "SELECT\n" +
//                                    "\tccp_unit_secondary.* \n" +
//                                    "FROM\n" +
//                                    "\tccp_unit_secondary\n" +
//                                    "\tLEFT JOIN ccp_org ON ccp_unit_secondary.org_code = ccp_org.code \n" +
//                                    "WHERE\n" +
//                                    "\tccp_org.org_code LIKE '" + org.getOrgCode() + "%' \n" +
//                                    "\tAND ccp_unit_secondary.delete_time IS NULL ", null)
//                    , mySimpleAsync);
            // TODO: 2023/1/29 合并数据的时候，存在可能没关联党组织，所以导致合掉的问题git
            CompletableFuture<List<Map<String, Object>>> unitSecondaryFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "SELECT " +
                                    " ccp_unit_secondary.* " +
                                    " FROM " +
                                    " ccp_unit_secondary " +
                                    " LEFT JOIN ccp_unit ON ccp_unit.code = ccp_unit_secondary.unit_code " +
                                    " LEFT JOIN ccp_org ON ccp_org.code = ccp_unit.create_org_code " +
                                    " WHERE " +
                                    " ccp_unit_secondary.delete_time IS NULL " +
                                    " AND ccp_org.org_code LIKE '" + org.getOrgCode() + "%'", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> unitSiteConditionFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "SELECT\n" +
                                    "\tccp_unit_site_conditions.* \n" +
                                    "FROM\n" +
                                    "\tccp_unit_site_conditions\n" +
                                    "\tLEFT JOIN ccp_org ON ccp_unit_site_conditions.org_code = ccp_org.code \n" +
                                    "\tAND ccp_org.delete_time IS NULL \n" +
                                    "WHERE\n" +
                                    "\tccp_org.org_code LIKE '" + org.getOrgCode() + "%' \n" +
                                    "\tAND ccp_unit_site_conditions.delete_time IS NULL ", null)
                    , mySimpleAsync);

            CompletableFuture<List<Map<String, Object>>> unitStreetsCadreFuture = CompletableFuture.supplyAsync(() ->
                            PgUtil.executeQuery(database, "SELECT\n" +
                                    "\tccp_unit_streets_cadres.* \n" +
                                    "FROM\n" +
                                    "\tccp_unit_streets_cadres\n" +
                                    "\tLEFT JOIN ccp_org ON ccp_unit_streets_cadres.org_code = ccp_org.code \n" +
                                    "\tAND ccp_org.delete_time IS NULL \n" +
                                    "WHERE\n" +
                                    "\tccp_org.org_code LIKE '" + org.getOrgCode() + "%' \n" +
                                    "\tAND ccp_unit_streets_cadres.delete_time IS NULL ", null)
                    , mySimpleAsync);

            CompletableFuture<List<UnitTownshipCadreInfo>> unitTownshipCadreInfoFuture = CompletableFuture.supplyAsync(() ->
                            xmlDataExportService.getUnitTownshipCadreInfo(database, org)
                    , mySimpleAsync);

            CompletableFuture<List<ActivistTransferApproval>> activistTransferApprovalFuture = CompletableFuture.supplyAsync(() ->
                            xmlDataExportService.getActivistTransferApproval(database, org)
                    , mySimpleAsync);
            CompletableFuture<List<ActivistTransferLog>> activistTransferLogFuture = CompletableFuture.supplyAsync(() ->
                            xmlDataExportService.getActivistTransferLog(database, org)
                    , mySimpleAsync);
            CompletableFuture<List<ActivistTransferRecord>> activistTransferRecordFuture = CompletableFuture.supplyAsync(() ->
                            xmlDataExportService.getActivistTransferRecord(database, org)
                    , mySimpleAsync);


            // 驻村数据查询
            List<CompletableFuture<Map<String, List<?>>>> stFutures = null;
            if (flg) {
                log.info("{}-机构编码[{}]执行驻村数据查询", database, org.getOrgCode());
                stFutures = stAsync(database, org);
            }

            String shortName = org.getName();
            String name = org.getOrgCode() + "_" + shortName + "_" + DateUtil.format(new Date(), "yyyyMMddHHmmss") + "." + XmlFactory.FORMAT_NAME_XML.toLowerCase(Locale.ROOT);

            List<Map<String, Object>> memList = memFuture.get();
            List<Map<String, Object>> memHistoryList = memHistoryFuture.get();
            if (CollUtil.isNotEmpty(memList) && CollUtil.isNotEmpty(memHistoryList)) {
                memList.addAll(memHistoryList);
            }
            List<Map<String, Object>> mapAllList = memAllFuture.get();
            List<Map<String, Object>> memHistoryAllList = memHistoryAllFuture.get();
            if (CollUtil.isNotEmpty(mapAllList) && CollUtil.isNotEmpty(memHistoryAllList)) {
                mapAllList.addAll(memHistoryAllList);
            }
            List<Map<String, Object>> memAll1List = memAll1Future.get();
            if (CollUtil.isNotEmpty(mapAllList) && CollUtil.isNotEmpty(memAll1List)) {
                memAll1List.forEach(k -> k.put("code", StrKit.getRandomUUID()));
                mapAllList.addAll(memAll1List);
            }
            XmlNode xmlNode = new XmlNode();
            xmlNode
                    .setCode(org.getOrgCode())
                    .setName(shortName)
                    .setNk(nginxKey)
                    .setDemocraticReview(democraticReviewFuture.get().stream().map(v -> JsonUtil.toBean(v, DemocraticReview.class)).collect(Collectors.toList()))
                    .setDemocraticReviewLead(democraticReviewLeadFuture.get().stream().map(v -> JsonUtil.toBean(v, DemocraticReviewLead.class)).collect(Collectors.toList()))
                    .setDemocraticReviewMem(democraticReviewMemFuture.get().stream().map(v -> JsonUtil.toBean(v, DemocraticReviewMem.class)).collect(Collectors.toList()))
                    .setDevelopPlan(developPlanFuture.get())
                    .setDevelopPlanLog(developPlanLogFuture.get())
                    .setDevelopStepLog(developStepLogFuture.get())
                    .setDevelopStepLogAll(developStepLogAllFuture.get())
                    .setLockFiledLog(lockFiledLogFuture.get().stream().map(v -> JsonUtil.toBean(v, LockFiledLog.class)).collect(Collectors.toList()))
                    .setMem(memList.stream().map(v -> JsonUtil.toBean(v, Mem.class)).collect(Collectors.toList()))
                    .setMemAbroad(memAbroadFuture.get().stream().map(v -> JsonUtil.toBean(v, MemAbroad.class)).collect(Collectors.toList()))
                    .setMemAllInfos(mapAllList.stream().map(v -> JsonUtil.toBean(v, MemAllInfo.class)).collect(Collectors.toList()))
                    .setMemDevelop(memDevelopFuture.get().stream().map(v -> JsonUtil.toBean(v, MemDevelop.class)).collect(Collectors.toList()))
                    .setMemDevelopAll(memDevelopAllFuture.get().stream().map(v -> JsonUtil.toBean(v, MemDevelopAll.class)).collect(Collectors.toList()))
                    .setMemDevelopOperation(memDevelopOperationFuture.get().stream().map(v -> JsonUtil.toBean(v, MemDevelopOperation.class)).collect(Collectors.toList()))
                    .setMemDifficult(memDifficultFuture.get())
                    .setMemExtend(memExtendFuture.get())
                    .setMemFlowLog(memFlowLogFuture.get())
                    .setMemHistory(ccpMemHistoryFuture.get().stream().map(v -> JsonUtil.toBean(v, MemHistory.class)).collect(Collectors.toList()))
                    .setMemLog(memLogFuture.get())
                    .setMemMany(memManyFuture.get())
                    .setMemReportList(memReportFuture.get())
                    .setMemReward(memRewardFuture.get())
                    .setMemRewardAll(memRewardAllFuture.get().stream().map(v -> JsonUtil.toBean(v, MemRewardAll.class)).collect(Collectors.toList()))
                    .setMemTrain(memTrainFuture.get())
                    .setMemTrainInfoList(memTrainInfoFuture.get())
                    .setOrg(orgFuture.get().stream().map(v -> JsonUtil.toBean(v, Org.class)).collect(Collectors.toList()))
                    .setOrgAll(orgAllFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgAll.class)).collect(Collectors.toList()))
                    .setOrgAppraisal(orgAppraisalFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgAppraisal.class)).collect(Collectors.toList()))
                    .setOrgAssesses(orgAssessesFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgAssess.class)).collect(Collectors.toList()))
                    .setOrgCaucus(orgCaucusFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgCaucus.class)).collect(Collectors.toList()))
                    .setOrgCommittee(orgCommitteeFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgCommittee.class)).collect(Collectors.toList()))
                    .setOrgCommitteeElect(orgCommitteeElectFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgCommitteeElect.class)).collect(Collectors.toList()))
                    .setOrgDevelopRights(orgDevelopRightsFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgDevelopRights.class)).collect(Collectors.toList()))
                    .setOrgExtend(orgExtendFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgExtend.class)).collect(Collectors.toList()))
                    .setOrgGroup(orgGroupFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgGroup.class)).collect(Collectors.toList()))
                    .setOrgGroupMember(orgGroupMemberFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgGroupMember.class)).collect(Collectors.toList()))
                    .setOrgHistory(orgHistoryFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgHistory.class)).collect(Collectors.toList()))
                    .setOrgIndustries(orgIndustriesFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgIndustry.class)).collect(Collectors.toList()))
                    .setOrgIndustryAll(orgIndustryAllFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgIndustryAll.class)).collect(Collectors.toList()))
                    .setOrgNonPublicParties(orgNonPublicPartiesFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgNonPublicParty.class)).collect(Collectors.toList()))
                    .setOrgParty(orgPartyFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgParty.class)).collect(Collectors.toList()))
                    .setOrgPartyCongressCommittee(orgPartyCongressCommitteeFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgPartyCongressCommittee.class)).collect(Collectors.toList()))
                    .setOrgPartyCongressCommitteeAlls(orgPartyCongressCommitteeAllFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgPartyCongressCommitteeAll.class)).collect(Collectors.toList()))
                    .setOrgPartyCongressElect(orgPartyCongressElectFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgPartyCongressElect.class)).collect(Collectors.toList()))
                    .setOrgRecognition(orgRecognitionFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgRecognition.class)).collect(Collectors.toList()))
                    .setOrgRecognitionAll(orgRecognitionAllFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgRecognitionAll.class)).collect(Collectors.toList()))
                    .setOrgRecognitionData(orgRecognitionDataFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgRecognitionData.class)).collect(Collectors.toList()))
                    .setOrgRecognitionSituation(orgRecognitionSituationFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgRecognitionSituation.class)).collect(Collectors.toList()))
                    .setOrgReport(orgReportFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgReport.class)).collect(Collectors.toList()))
                    .setOrgReviewers(orgReviewersFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgReviewers.class)).collect(Collectors.toList()))
                    .setOrgReward(orgRewardFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgReward.class)).collect(Collectors.toList()))
                    .setOrgSlack(orgSlackFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgSlack.class)).collect(Collectors.toList()))
                    .setOrgSlackAll(orgSlackAllFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgSlackAll.class)).collect(Collectors.toList()))
                    .setOrgSlackRectification(orgSlackRectificationFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgSlackRectification.class)).collect(Collectors.toList()))
                    .setOrgSpecialNature(orgSpecialNatureFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgSpecialNature.class)).collect(Collectors.toList()))
                    .setOrgTownshipLeaderships(orgTownshipLeadershipFuture.get())
                    .setRepresentative(representativeFuture.get())
                    .setRepresentativeContacts(representativeContactFuture.get())
                    .setRepresentativeElect(representativeElectFuture.get())
                    .setTransferApproval(transferApprovalFuture.get())
                    .setTransferEffectMem(transferEffectMemsFuture.get())
                    .setTransferLog(transferLogFuture.get())
                    .setTransferRecord(transferRecordFuture.get())
                    .setUnit(unitFuture.get().stream().map(v -> JsonUtil.toBean(v, Unit.class)).collect(Collectors.toList()))
                    .setUnitAll(unitAllFuture.get().stream().map(v -> JsonUtil.toBean(v, UnitAll.class)).collect(Collectors.toList()))
                    .setUnitCitySituation(unitCitySituationFuture.get().stream().map(v -> JsonUtil.toBean(v, UnitCitySituation.class)).collect(Collectors.toList()))
                    .setUnitCollectiveEconomic(unitCollectiveEconomicFuture.get().stream().map(v -> JsonUtil.toBean(v, UnitCollectiveEconomic.class)).collect(Collectors.toList()))
                    .setUnitCommittee(unitCommitteeFuture.get().stream().map(v -> JsonUtil.toBean(v, UnitCommittee.class)).collect(Collectors.toList()))
                    .setUnitCommitteeElect(unitCommitteeElectFuture.get().stream().map(v -> JsonUtil.toBean(v, UnitCommitteeElect.class)).collect(Collectors.toList()))
                    .setUnitCommunity(unitCommunityFuture.get().stream().map(v -> JsonUtil.toBean(v, UnitCommunity.class)).collect(Collectors.toList()))
                    .setUnitCountryside(unitCountrysideFuture.get().stream().map(v -> JsonUtil.toBean(v, UnitCountryside.class)).collect(Collectors.toList()))
                    .setUnitExpandCollectiveEconomyList(unitExpandCollectiveEconomyFuture.get())
                    .setUnitExtend(unitExtendFuture.get().stream().map(v -> JsonUtil.toBean(v, UnitExtend.class)).collect(Collectors.toList()))
                    .setUnitIncome(unitIncomeFuture.get().stream().map(v -> JsonUtil.toBean(v, UnitIncome.class)).collect(Collectors.toList()))
                    .setUnitOrgLinked(unitOrgLinkedFuture.get().stream().map(v -> JsonUtil.toBean(v, UnitOrgLinked.class)).collect(Collectors.toList()))
                    .setUnitReportList(unitReportFuture.get())
                    .setUnitResidentList(unitResidentFuture.get())
                    .setUnitSecondary(unitSecondaryFuture.get().stream().map(v -> JsonUtil.toBean(v, UnitSecondary.class)).collect(Collectors.toList()))
                    .setUnitSiteCondition(unitSiteConditionFuture.get().stream().map(v -> JsonUtil.toBean(v, UnitSiteConditions.class)).collect(Collectors.toList()))
                    .setUnitStreetsCadre(unitStreetsCadreFuture.get().stream().map(v -> JsonUtil.toBean(v, UnitStreetsCadres.class)).collect(Collectors.toList()))
                    .setUnitTownshipCadreInfoList(unitTownshipCadreInfoFuture.get())
                    .setActivistTransferApprovalList(activistTransferApprovalFuture.get())
                    .setActivistTransferLogList(activistTransferLogFuture.get())
                    .setActivistTransferRecordList(activistTransferRecordFuture.get())
                    .setOrgContactMem(orgContactMemFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgContactMem.class)).collect(Collectors.toList()))
                    .setOrgExampleSite(orgExampleSiteFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgExampleSite.class)).collect(Collectors.toList()))
                    .setOrgContactWork(orgContactWorkFuture.get().stream().map(v -> JsonUtil.toBean(v, OrgContactWork.class)).collect(Collectors.toList()))

            ;

            // 导出时对数据进行解密处理
            log.info("{}-机构编码[{}]开始导出解密处理", database, org.getOrgCode());
            final LinkedHashMap<String, LinkedHashSet<String>> encryptMap = ReflectionUtil.ENCRYPT_MAP;
//            decryptXmlNodeData(xmlNode, nginxKey, encryptMap);

            // 驻村数据
            if (flg && CollUtil.isNotEmpty(stFutures)) {
                log.info("{}-机构编码[{}]执行驻村数据导出xml", database, org.getOrgCode());
                for (CompletableFuture<Map<String, List<?>>> stFuture : stFutures) {
                    Map<String, List<?>> stFutureMap = stFuture.get();
                    stFutureMap.forEach((upperFieldName, list) -> ReflectUtil.invoke(xmlNode, "set" + upperFieldName, list));
                }
            }
            XmlUtil.writeValue(new File(folder.getPath() + File.separator + name), xmlNode);
        }

        // 针对没通过orgLevelCode查询数据的，每个database只执行一次
        if (flg) {
            XmlNode xmlNode = new XmlNode();
            log.info("{}-全量执行驻村数据查询", database);
            List<CompletableFuture<Map<String, List<?>>>> stFutures = stAsync(database, null);
            log.info("{}-全量执行驻村数据导出xml", database);
            for (CompletableFuture<Map<String, List<?>>> stFuture : stFutures) {
                Map<String, List<?>> stFutureMap = stFuture.get();
                stFutureMap.forEach((upperFieldName, list) -> ReflectUtil.invoke(xmlNode, "set" + upperFieldName, list));
            }

            // 对全量驻村数据进行解密处理
            log.info("{}-全量驻村数据开始导出解密处理", database);
            final LinkedHashMap<String, LinkedHashSet<String>> encryptMap = ReflectionUtil.ENCRYPT_MAP;
//            decryptXmlNodeData(xmlNode, nginxKey, encryptMap);

            String name = database + "_full_all_" + DateUtil.format(new Date(), "yyyyMMddHHmmss") + "." + XmlFactory.FORMAT_NAME_XML.toLowerCase(Locale.ROOT);
            XmlUtil.writeValue(new File(folder.getPath() + File.separator + name), xmlNode);
        }

        // 导出档案表相关数据
        xmlDataExportService.exportMemDigitalData(database, nginxKey, null, folder);
    }

    @Override
    public OutMessage<Object> importXml(String url, String basePath, String account) throws Exception {
        final String zipFilePath = basePath + url;
        File zipFile = new File(zipFilePath);
        if (!FileUtil.exist(zipFile)) {
            return new OutMessage<>(Status.FILE_CANNOT_BE_FOUND);
        }
        ZipFile zFile = new ZipFile(zipFile);
        if (!zFile.isValidZipFile()) {
            return new OutMessage<>(500, "压缩文件不合法,可能被损坏.联系管理员", null);
        }
        //读取zip文件并解压
        File unzipFile = ZipUtil.unzip(zipFile, password);
        //加载所有xml文件
        List<File> fileList = FileUtil.loopFiles(unzipFile);
        log.info("=======================================解压后文件个数"+fileList.size()+"=======================================");
        final LinkedHashMap<String, LinkedHashSet<String>> encryptMap = ReflectionUtil.ENCRYPT_MAP;
        List<String> orgCodeList = new ArrayList<>();
        List<String> orgNameList = new ArrayList<>();
        log.info("=======================================开始处理数据导入=======================================");
        long startTime = System.currentTimeMillis();

        fileList.parallelStream().forEach((file) -> {
            XmlNode xmlNode = XmlUtil.readValue(file, XmlNode.class);
            if (Objects.nonNull(xmlNode)) {
                String code = xmlNode.getCode();
                if (Objects.nonNull(code)) {
                    orgCodeList.add(code);
                }

                String name = xmlNode.getName();
                if (Objects.nonNull(name)) {
                    orgNameList.add(name);
                }
                final String nk = xmlNode.getNk();
                log.info("=======================================开始导入文件"+file.getName()+"=======================================");
                this.saveBatchTmp(xmlNode, nk, encryptMap);
            }

        });
        long endTime = System.currentTimeMillis();
        log.info("=======================================数据导入耗时" + (endTime - startTime) + "毫秒=======================================");
        //清理解压过后的文件
        FileUtil.del(unzipFile);
        DataExchange dataExchange = new DataExchange();
        dataExchange.setId(StrKit.getRandomUUID());
        dataExchange.setOrgCode(String.join(",", orgCodeList));
        dataExchange.setOrgName(String.join(",", orgNameList));
        dataExchange.setFileName(FileUtil.getName(zipFile));
        dataExchange.setUploadCapacity(String.valueOf(zipFile.length()));
        dataExchange.setFileUrl(url);
        dataExchange.setFileFormat(FileUtil.extName(zipFile));
        dataExchange.setAccept(CommonConstant.ZERO);
        dataExchange.setClean(CommonConstant.ZERO);
        dataExchange.setUserId("");
        dataExchange.setUpdateAccount(account);
        dataExchange.setVersionNumber("1.0.0");
        dataExchange.setCreateTime(new Date());
        int updateCount = dataExchangeMapper.insert(dataExchange);
        return new OutMessage<>(updateCount > 0 ? Status.SUCCESS : Status.FAIL);
    }

    private void saveBatchTmp(XmlNode xmlNode, String nk, LinkedHashMap<String, LinkedHashSet<String>> encryptMap) {
        log.info("开始写入节点：{}；{}", nk, "MemDigitalAudit");
        List<MemDigitalAudit> memDigitalAuditList = xmlNode.getMemDigitalAuditList();
        if (CollUtil.isNotEmpty(memDigitalAuditList)) {
            iMemDigitalAuditService.saveBatch(memDigitalAuditList, memDigitalAuditList.size());
            memDigitalAuditList.clear();
        }

        log.info("开始写入节点：{}；{}", nk, "MemDigitalCount");
        List<MemDigitalCount> memDigitalCountList = xmlNode.getMemDigitalCountList();
        if (CollUtil.isNotEmpty(memDigitalCountList)) {
            iMemDigitalCountService.saveBatch(memDigitalCountList, memDigitalCountList.size());
            memDigitalCountList.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "MemDigital");
        List<MemDigital> memDigitalList = xmlNode.getMemDigitalList();
        if (CollUtil.isNotEmpty(memDigitalList)) {
            iMemDigitalService.saveBatch(memDigitalList, memDigitalList.size());
            memDigitalList.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "MemDevelopProcess");
        List<MemDevelopProcess> memDevelopProcessList = xmlNode.getMemDevelopProcessList();
        if (CollUtil.isNotEmpty(memDevelopProcessList)) {
            iMemDevelopProcessService.saveBatch(memDevelopProcessList, memDevelopProcessList.size());
            memDevelopProcessList.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "MemDigitalOperationLog");
        List<MemDigitalOperationLog> memDigitalOperationLogList = xmlNode.getMemDigitalOperationLogList();
        if (CollUtil.isNotEmpty(memDigitalOperationLogList)) {
            iMemDigitalOperationLogService.saveBatch(memDigitalOperationLogList, memDigitalOperationLogList.size());
            memDigitalOperationLogList.clear();
        }
        // ------------------------------------------------------
        // todo: 2025-03-03 流动党员数据合并写入
        log.info("开始写入节点：{}；{}", nk, "OrgFlow");
        List<OrgFlow2> OrgFlowList = xmlNode.getOrgFlow2List();
        if (CollUtil.isNotEmpty(OrgFlowList)) {
            OrgFlow2 entity = OrgFlowList.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                OrgFlowList.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(model.getNk(), model, encryptMap);
                });
            } else {
                OrgFlowList.forEach((model) -> {
                    model.setId(null);
                });
            }
            this.orgFlow2Service.saveBatch(OrgFlowList, OrgFlowList.size());
            OrgFlowList.clear();
        }
        //流动党员督察表
        log.info("开始写入节点：{}；{}", nk, "memFlowInspectionForm");
        List<MemFlowInspectionForm> memFlowInspectionList = xmlNode.getMemFlowInspectionList();
        if (CollUtil.isNotEmpty(memFlowInspectionList)) {
            this.iMemFlowInspectionFormService.saveBatch(memFlowInspectionList, memFlowInspectionList.size());
            memFlowInspectionList.clear();
        }

        log.info("开始写入节点：{}；{}", nk, "OrgFlowAudit");
        List<OrgFlowAudit2> orgFlowAudit2List = xmlNode.getOrgFlowAudit2List();
        if (CollUtil.isNotEmpty(orgFlowAudit2List)) {
            OrgFlowAudit2 entity = orgFlowAudit2List.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgFlowAudit2List.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(model.getNk(), model, encryptMap);
                });
            } else {
                orgFlowAudit2List.forEach((model) -> {
                    model.setId(null);
                });
            }
            this.orgFlowAudit2Service.saveBatch(orgFlowAudit2List, orgFlowAudit2List.size());
            orgFlowAudit2List.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "MemFlowSignAudit");
        List<MemFlowSignAudit> memFlowSignAuditList = xmlNode.getMemFlowSignAuditList();
        if (CollUtil.isNotEmpty(memFlowSignAuditList)) {
            MemFlowSignAudit entity = memFlowSignAuditList.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                memFlowSignAuditList.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(model.getNk(), model, encryptMap);
                });
            } else {
                memFlowSignAuditList.forEach((model) -> {
                    model.setId(null);
                });
            }
            this.memFlowSignAuditService.saveBatch(memFlowSignAuditList, memFlowSignAuditList.size());
            memFlowSignAuditList.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "MemFlowSign");
        List<MemFlowSign2> memFlowSign2List = xmlNode.getMemFlowSign2List();
        if (CollUtil.isNotEmpty(memFlowSign2List)) {
            MemFlowSign2 entity = memFlowSign2List.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                memFlowSign2List.forEach(model -> {
                    fieldValueDecryption(model.getNk(), model, encryptMap);
                });
            }
            this.memFlowSign2Service.saveBatch(memFlowSign2List, memFlowSign2List.size());
            memFlowSign2List.clear();
        }

        // ------------------------------------------------------
        log.info("开始写入节点：{}；{}", nk, "orgExampleSite");
        List<OrgExampleSite> orgExampleSite = xmlNode.getOrgExampleSite();
        if (CollUtil.isNotEmpty(orgExampleSite)) {
            OrgExampleSite entity = orgExampleSite.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgExampleSite.forEach(model -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }
            this.orgExampleSiteService.saveBatch(orgExampleSite, orgExampleSite.size());
            orgExampleSite.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgContactWork");
        List<OrgContactWork> orgContactWork = xmlNode.getOrgContactWork();
        if (CollUtil.isNotEmpty(orgContactWork)) {
            OrgContactWork entity = orgContactWork.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgContactWork.forEach(model -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.orgContactWorkService.saveBatch(orgContactWork, orgContactWork.size());
            orgContactWork.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgContactMem");
        List<OrgContactMem> orgContactMem = xmlNode.getOrgContactMem();
        if (CollUtil.isNotEmpty(orgContactMem)) {
            OrgContactMem entity = orgContactMem.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgContactMem.forEach(model -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.orgContactMemService.saveBatch(orgContactMem, orgContactMem.size());
            orgContactMem.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "vcPleasDestroyHolidayList");
        List<com.zenith.front.entity.model.st.VcPleasDestroyHoliday> vcPleasDestroyHolidayList = xmlNode.getVcPleasDestroyHolidayList();
        if (CollUtil.isNotEmpty(vcPleasDestroyHolidayList)) {
            com.zenith.front.entity.model.st.VcPleasDestroyHoliday entity = vcPleasDestroyHolidayList.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                vcPleasDestroyHolidayList.forEach(model -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }
            this.vcPleasDestroyHolidayService.saveBatch(vcPleasDestroyHolidayList, vcPleasDestroyHolidayList.size());
            vcPleasDestroyHolidayList.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "democraticReview");
        List<DemocraticReview> democraticReview = xmlNode.getDemocraticReview();
        if (CollUtil.isNotEmpty(democraticReview)) {
            DemocraticReview entity = democraticReview.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                democraticReview.forEach(model -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                democraticReview.forEach(model -> {
                    model.setId(null);
                });
            }
            this.democraticReviewService.saveBatch(democraticReview, democraticReview.size());
            democraticReview.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "democraticReviewLead");
        List<DemocraticReviewLead> democraticReviewLead = xmlNode.getDemocraticReviewLead();
        if (CollUtil.isNotEmpty(democraticReviewLead)) {
            DemocraticReviewLead entity = democraticReviewLead.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                democraticReviewLead.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                democraticReviewLead.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.democraticReviewLeadService.saveBatch(democraticReviewLead, democraticReviewLead.size());
            democraticReviewLead.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "democraticReviewMem");
        List<DemocraticReviewMem> democraticReviewMem = xmlNode.getDemocraticReviewMem();
        if (CollUtil.isNotEmpty(democraticReviewMem)) {
            DemocraticReviewMem entity = democraticReviewMem.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                democraticReviewMem.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                democraticReviewMem.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.democraticReviewMemService.saveBatch(democraticReviewMem, democraticReviewMem.size());
            democraticReviewMem.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "developPlan");
        List<DevelopPlan> developPlan = xmlNode.getDevelopPlan();
        if (CollUtil.isNotEmpty(developPlan)) {
            DevelopPlan entity = developPlan.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                developPlan.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                developPlan.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.developPlanService.saveBatch(developPlan, developPlan.size());
            developPlan.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "developPlanLog");
        List<DevelopPlanLog> developPlanLog = xmlNode.getDevelopPlanLog();
        if (CollUtil.isNotEmpty(developPlanLog)) {
            DevelopPlanLog entity = developPlanLog.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                developPlanLog.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                developPlanLog.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.developPlanLogService.saveBatch(developPlanLog, developPlanLog.size());
            developPlanLog.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "developStepLog");
        List<DevelopStepLog> developStepLog = xmlNode.getDevelopStepLog();
        if (CollUtil.isNotEmpty(developStepLog)) {
            DevelopStepLog entity = developStepLog.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                developStepLog.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                developStepLog.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.developStepLogService.saveBatch(developStepLog, developStepLog.size());
            developStepLog.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "developStepLogAll");
        List<DevelopStepLogAll> developStepLogAll = xmlNode.getDevelopStepLogAll();
        if (CollUtil.isNotEmpty(developStepLogAll)) {
            DevelopStepLogAll entity = developStepLogAll.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                developStepLogAll.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);

                });
            } else {
                developStepLogAll.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.developStepLogAllService.saveBatch(developStepLogAll, developStepLogAll.size());
            developStepLogAll.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "lockFiledLog");
        List<LockFiledLog> lockFiledLog = xmlNode.getLockFiledLog();
        if (CollUtil.isNotEmpty(lockFiledLog)) {
            LockFiledLog entity = lockFiledLog.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                lockFiledLog.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                lockFiledLog.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.lockFiledLogService.saveBatch(lockFiledLog, lockFiledLog.size());
            lockFiledLog.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "logicCheckIgnoreList");
        List<LogicCheckIgnore> logicCheckIgnoreList = xmlNode.getLogicCheckIgnores();
        if (CollUtil.isNotEmpty(logicCheckIgnoreList)) {
            LogicCheckIgnore entity = logicCheckIgnoreList.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                logicCheckIgnoreList.forEach((model) -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.logicCheckIgnoreService.saveBatch(logicCheckIgnoreList, logicCheckIgnoreList.size());
            logicCheckIgnoreList.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "mem");
        List<Mem> mem = xmlNode.getMem();
        if (CollUtil.isNotEmpty(mem)) {
            Mem entity = mem.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                mem.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                mem.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.memService.saveBatch(mem, mem.size());
            mem.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "memAbroad");
        List<MemAbroad> memAbroad = xmlNode.getMemAbroad();
        if (CollUtil.isNotEmpty(memAbroad)) {
            MemAbroad entity = memAbroad.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                memAbroad.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                memAbroad.forEach((model) -> {
                    model.setId(null);
                });
            }
            this.memAbroadService.saveBatch(memAbroad, memAbroad.size());
            memAbroad.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "memAllInfos");
        List<MemAllInfo> memAllInfos = xmlNode.getMemAllInfos();
        if (CollUtil.isNotEmpty(memAllInfos)) {
            MemAllInfo entity = memAllInfos.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                memAllInfos.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                memAllInfos.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.memAllInfoService.saveBatch(memAllInfos, memAllInfos.size());
            memAllInfos.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "memDevelop");
        List<MemDevelop> memDevelop = xmlNode.getMemDevelop();
        if (CollUtil.isNotEmpty(memDevelop)) {
            MemDevelop entity = memDevelop.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                memDevelop.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                memDevelop.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.memDevelopService.saveBatch(memDevelop, memDevelop.size());
            memDevelop.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "memDevelopAll");
        List<MemDevelopAll> memDevelopAll = xmlNode.getMemDevelopAll();
        if (CollUtil.isNotEmpty(memDevelopAll)) {
            MemDevelopAll entity = memDevelopAll.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                memDevelopAll.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                memDevelopAll.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.memDevelopAllService.saveBatch(memDevelopAll, memDevelopAll.size());
            memDevelopAll.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "memDevelopOperation");
        List<MemDevelopOperation> memDevelopOperation = xmlNode.getMemDevelopOperation();
        if (CollUtil.isNotEmpty(memDevelopOperation)) {
            MemDevelopOperation entity = memDevelopOperation.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                memDevelopOperation.forEach((model) -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.memDevelopOperationService.saveBatch(memDevelopOperation, memDevelopOperation.size());
            memDevelopOperation.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "memDifficult");
        List<MemDifficult> memDifficult = xmlNode.getMemDifficult();
        if (CollUtil.isNotEmpty(memDifficult)) {
            MemDifficult entity = memDifficult.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                memDifficult.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                memDifficult.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.memDifficultService.saveBatch(memDifficult, memDifficult.size());
            memDifficult.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "memExtend");
        List<MemExtend> memExtend = xmlNode.getMemExtend();
        if (CollUtil.isNotEmpty(memExtend)) {
            MemExtend entity = memExtend.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                memExtend.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                memExtend.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.memExtendService.saveBatch(memExtend, memExtend.size());
            memExtend.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "memFlow2");
        List<MemFlow2> memFlow2 = xmlNode.getMemFlow2();
        if (CollUtil.isNotEmpty(memFlow2)) {
            log.error("memFlow2数量: {}", memFlow2.size());
            MemFlow2 entity = memFlow2.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                memFlow2.forEach((model) -> {
                    fieldValueDecryption(model.getNk(), model, encryptMap);
                });
            }
            log.error("memFlow2保存数量: {}", memFlow2.size());
            this.iMemFlow2Service.saveBatch(memFlow2, memFlow2.size());
            log.error("memFlow2保存完成: {}", memFlow2.size());
            memFlow2.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "memFlow1All");
        List<MemFlow1All> memFlow1All = xmlNode.getMemFlow1All();
        if (CollUtil.isNotEmpty(memFlow1All)) {
            MemFlow1All entity = memFlow1All.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                memFlow1All.forEach((model) -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.memFlow1AllService.saveBatch(memFlow1All, memFlow1All.size());
            memFlow1All.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "memFlowLog");
        List<MemFlowLog> memFlowLog = xmlNode.getMemFlowLog();
        if (CollUtil.isNotEmpty(memFlowLog)) {
            MemFlowLog entity = memFlowLog.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                memFlowLog.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                memFlowLog.forEach((model) -> {
                    model.setId(null);

                });
            }

            this.memFlowLogService.saveBatch(memFlowLog, memFlowLog.size());
            memFlowLog.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "memHistory");
        List<MemHistory> memHistory = xmlNode.getMemHistory();
        if (CollUtil.isNotEmpty(memHistory)) {
            MemHistory entity = memHistory.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                memHistory.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                memHistory.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.memHistoryService.saveBatch(memHistory, memHistory.size());
            memHistory.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "memLog");
        List<MemLog> memLog = xmlNode.getMemLog();
        if (CollUtil.isNotEmpty(memLog)) {
            MemLog entity = memLog.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                memLog.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                memLog.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.memLogService.saveBatch(memLog, memLog.size());
            memLog.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "memMany");
        List<MemMany> memMany = xmlNode.getMemMany();
        if (CollUtil.isNotEmpty(memMany)) {
            MemMany entity = memMany.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                memMany.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                memMany.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.memManyService.saveBatch(memMany, memMany.size());
            memMany.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "memReportList");
        List<MemReport> memReportList = xmlNode.getMemReportList();
        if (CollUtil.isNotEmpty(memReportList)) {
            MemReport entity = memReportList.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                memReportList.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                memReportList.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.memReportService.saveBatch(memReportList, memReportList.size());
            memReportList.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "memReward");
        List<MemReward> memReward = xmlNode.getMemReward();
        if (CollUtil.isNotEmpty(memReward)) {
            MemReward entity = memReward.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                memReward.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                memReward.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.memRewardService.saveBatch(memReward, memReward.size());
            memReward.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "memRewardAll");
        List<MemRewardAll> memRewardAll = xmlNode.getMemRewardAll();
        if (CollUtil.isNotEmpty(memRewardAll)) {
            MemRewardAll entity = memRewardAll.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                memRewardAll.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                memRewardAll.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.memRewardAllService.saveBatch(memRewardAll, memRewardAll.size());
            memRewardAll.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "memTrain");
        List<MemTrain> memTrain = xmlNode.getMemTrain();
        if (CollUtil.isNotEmpty(memTrain)) {
            MemTrain entity = memTrain.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                memTrain.forEach((model) -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.memTrainService.saveBatch(memTrain, memTrain.size());
            memTrain.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "memTrainInfoList");
        List<MemTrainInfo> memTrainInfoList = xmlNode.getMemTrainInfoList();
        if (CollUtil.isNotEmpty(memTrainInfoList)) {
            MemTrainInfo entity = memTrainInfoList.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                memTrainInfoList.forEach((model) -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.memTrainInfoService.saveBatch(memTrainInfoList, memTrainInfoList.size());
            memTrainInfoList.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "org");
        List<Org> org = xmlNode.getOrg();
        if (CollUtil.isNotEmpty(org)) {
            Org entity = org.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                org.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                org.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.orgService.saveBatch(org, org.size());
            org.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgAll");
        List<OrgAll> orgAll = xmlNode.getOrgAll();
        if (CollUtil.isNotEmpty(orgAll)) {
            OrgAll entity = orgAll.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgAll.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                orgAll.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.orgAllService.saveBatch(orgAll, orgAll.size());
            orgAll.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgAppraisal");
        List<OrgAppraisal> orgAppraisal = xmlNode.getOrgAppraisal();
        if (CollUtil.isNotEmpty(orgAppraisal)) {
            OrgAppraisal entity = orgAppraisal.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgAppraisal.forEach((model) -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.orgAppraisalService.saveBatch(orgAppraisal, orgAppraisal.size());
            orgAppraisal.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgAssesses");
        List<OrgAssess> orgAssesses = xmlNode.getOrgAssesses();
        if (CollUtil.isNotEmpty(orgAssesses)) {
            OrgAssess entity = orgAssesses.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgAssesses.forEach((model) -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.orgAssessService.saveBatch(orgAssesses, orgAssesses.size());
            orgAssesses.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgCaucus");
        List<OrgCaucus> orgCaucus = xmlNode.getOrgCaucus();
        if (CollUtil.isNotEmpty(orgCaucus)) {
            OrgCaucus entity = orgCaucus.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgCaucus.forEach((model) -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.orgCaucusService.saveBatch(orgCaucus, orgCaucus.size());
            orgCaucus.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgCommittee");
        List<OrgCommittee> orgCommittee = xmlNode.getOrgCommittee();
        if (CollUtil.isNotEmpty(orgCommittee)) {
            OrgCommittee entity = orgCommittee.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgCommittee.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                orgCommittee.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.orgCommitteeService.saveBatch(orgCommittee, orgCommittee.size());
            orgCommittee.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgCommitteeElect");
        List<OrgCommitteeElect> orgCommitteeElect = xmlNode.getOrgCommitteeElect();
        if (CollUtil.isNotEmpty(orgCommitteeElect)) {
            OrgCommitteeElect entity = orgCommitteeElect.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgCommitteeElect.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                orgCommitteeElect.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.orgCommitteeElectService.saveBatch(orgCommitteeElect, orgCommitteeElect.size());
            orgCommitteeElect.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgDevelopRights");
        List<OrgDevelopRights> orgDevelopRights = xmlNode.getOrgDevelopRights();
        if (CollUtil.isNotEmpty(orgDevelopRights)) {
            OrgDevelopRights entity = orgDevelopRights.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgDevelopRights.forEach((model) -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.orgDevelopRightsService.saveBatch(orgDevelopRights, orgDevelopRights.size());
            orgDevelopRights.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgExtend");
        List<OrgExtend> orgExtend = xmlNode.getOrgExtend();
        if (CollUtil.isNotEmpty(orgExtend)) {
            OrgExtend entity = orgExtend.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgExtend.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                orgExtend.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.orgExtendService.saveBatch(orgExtend, orgExtend.size());
            orgExtend.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgGroup");
        List<OrgGroup> orgGroup = xmlNode.getOrgGroup();
        if (CollUtil.isNotEmpty(orgGroup)) {
            OrgGroup entity = orgGroup.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgGroup.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                orgGroup.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.orgGroupService.saveBatch(orgGroup, orgGroup.size());
            orgGroup.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgGroupMember");
        List<OrgGroupMember> orgGroupMember = xmlNode.getOrgGroupMember();
        if (CollUtil.isNotEmpty(orgGroupMember)) {
            OrgGroupMember entity = orgGroupMember.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgGroupMember.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                orgGroupMember.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.orgGroupMemberService.saveBatch(orgGroupMember, orgGroupMember.size());
            orgGroupMember.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgHistory");
        List<OrgHistory> orgHistory = xmlNode.getOrgHistory();
        if (CollUtil.isNotEmpty(orgHistory)) {
            OrgHistory entity = orgHistory.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgHistory.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                orgHistory.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.orgHistoryService.saveBatch(orgHistory, orgHistory.size());
            orgHistory.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgIndustries");
        List<OrgIndustry> orgIndustries = xmlNode.getOrgIndustries();
        if (CollUtil.isNotEmpty(orgIndustries)) {
            OrgIndustry entity = orgIndustries.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgIndustries.forEach((model) -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.orgIndustryService.saveBatch(orgIndustries, orgIndustries.size());
            orgIndustries.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgIndustryAll");
        List<OrgIndustryAll> orgIndustryAll = xmlNode.getOrgIndustryAll();
        if (CollUtil.isNotEmpty(orgIndustryAll)) {
            OrgIndustryAll entity = orgIndustryAll.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgIndustryAll.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                orgIndustryAll.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.orgIndustryAllService.saveBatch(orgIndustryAll, orgIndustryAll.size());
            orgIndustryAll.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgNonPublicParties");
        List<OrgNonPublicParty> orgNonPublicParties = xmlNode.getOrgNonPublicParties();
        if (CollUtil.isNotEmpty(orgNonPublicParties)) {
            OrgNonPublicParty entity = orgNonPublicParties.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgNonPublicParties.forEach((model) -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.orgNonPublicPartyService.saveBatch(orgNonPublicParties, orgNonPublicParties.size());
            orgNonPublicParties.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgParty");
        List<OrgParty> orgParty = xmlNode.getOrgParty();
        if (CollUtil.isNotEmpty(orgParty)) {
            OrgParty entity = orgParty.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgParty.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                orgParty.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.orgPartyService.saveBatch(orgParty, orgParty.size());
            orgParty.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgPartyCongressCommittee");
        List<OrgPartyCongressCommittee> orgPartyCongressCommittee = xmlNode.getOrgPartyCongressCommittee();
        if (CollUtil.isNotEmpty(orgPartyCongressCommittee)) {
            OrgPartyCongressCommittee entity = orgPartyCongressCommittee.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgPartyCongressCommittee.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                orgPartyCongressCommittee.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.orgPartyCongressCommitteeService.saveBatch(orgPartyCongressCommittee, orgPartyCongressCommittee.size());
            orgPartyCongressCommittee.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgPartyCongressCommitteeAlls");
        List<OrgPartyCongressCommitteeAll> orgPartyCongressCommitteeAlls = xmlNode.getOrgPartyCongressCommitteeAlls();
        if (CollUtil.isNotEmpty(orgPartyCongressCommitteeAlls)) {
            OrgPartyCongressCommitteeAll entity = orgPartyCongressCommitteeAlls.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgPartyCongressCommitteeAlls.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                orgPartyCongressCommitteeAlls.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.orgPartyCongressCommitteeAllService.saveBatch(orgPartyCongressCommitteeAlls, orgPartyCongressCommitteeAlls.size());
            orgPartyCongressCommitteeAlls.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgPartyCongressElect");
        List<OrgPartyCongressElect> orgPartyCongressElect = xmlNode.getOrgPartyCongressElect();
        if (CollUtil.isNotEmpty(orgPartyCongressElect)) {
            OrgPartyCongressElect entity = orgPartyCongressElect.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgPartyCongressElect.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                orgPartyCongressElect.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.orgPartyCongressElectService.saveBatch(orgPartyCongressElect, orgPartyCongressElect.size());
            orgPartyCongressElect.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgRecognition");
        List<OrgRecognition> orgRecognition = xmlNode.getOrgRecognition();
        if (CollUtil.isNotEmpty(orgRecognition)) {
            OrgRecognition entity = orgRecognition.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgRecognition.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                orgRecognition.forEach((model) -> {
                    model.setId(null);

                });
            }

            this.orgRecognitionService.saveBatch(orgRecognition, orgRecognition.size());
            orgRecognition.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgRecognitionAll");
        List<OrgRecognitionAll> orgRecognitionAll = xmlNode.getOrgRecognitionAll();
        if (CollUtil.isNotEmpty(orgRecognitionAll)) {
            OrgRecognitionAll entity = orgRecognitionAll.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgRecognitionAll.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                orgRecognitionAll.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.orgRecognitionAllService.saveBatch(orgRecognitionAll, orgRecognitionAll.size());
            orgRecognitionAll.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgRecognitionData");
        List<OrgRecognitionData> orgRecognitionData = xmlNode.getOrgRecognitionData();
        if (CollUtil.isNotEmpty(orgRecognitionData)) {
            OrgRecognitionData entity = orgRecognitionData.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgRecognitionData.forEach((model) -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.orgRecognitionDataService.saveBatch(orgRecognitionData, orgRecognitionData.size());
            orgRecognitionData.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgRecognitionSituation");
        List<OrgRecognitionSituation> orgRecognitionSituation = xmlNode.getOrgRecognitionSituation();
        if (CollUtil.isNotEmpty(orgRecognitionSituation)) {
            OrgRecognitionSituation entity = orgRecognitionSituation.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgRecognitionSituation.forEach((model) -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.orgRecognitionSituationService.saveBatch(orgRecognitionSituation, orgRecognitionSituation.size());
            orgRecognitionSituation.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgReport");
        List<OrgReport> orgReport = xmlNode.getOrgReport();
        if (CollUtil.isNotEmpty(orgReport)) {
            OrgReport entity = orgReport.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgReport.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                orgReport.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.orgReportService.saveBatch(orgReport, orgReport.size());
            orgReport.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgReviewers");
        List<OrgReviewers> orgReviewers = xmlNode.getOrgReviewers();
        if (CollUtil.isNotEmpty(orgReviewers)) {
            OrgReviewers entity = orgReviewers.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgReviewers.forEach((model) -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.orgReviewersService.saveBatch(orgReviewers, orgReviewers.size());
            orgReviewers.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgReward");
        List<OrgReward> orgReward = xmlNode.getOrgReward();
        if (CollUtil.isNotEmpty(orgReward)) {
            OrgReward entity = orgReward.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgReward.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                orgReward.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.orgRewardService.saveBatch(orgReward, orgReward.size());
            orgReward.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgSlack");
        List<OrgSlack> orgSlack = xmlNode.getOrgSlack();
        if (CollUtil.isNotEmpty(orgSlack)) {
            OrgSlack entity = orgSlack.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgSlack.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                orgSlack.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.orgSlackService.saveBatch(orgSlack, orgSlack.size());
            orgSlack.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgSlackAll");
        List<OrgSlackAll> orgSlackAll = xmlNode.getOrgSlackAll();
        if (CollUtil.isNotEmpty(orgSlackAll)) {
            OrgSlackAll entity = orgSlackAll.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgSlackAll.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                orgSlackAll.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.orgSlackAllService.saveBatch(orgSlackAll, orgSlackAll.size());
            orgSlackAll.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgSlackRectification");
        List<OrgSlackRectification> orgSlackRectification = xmlNode.getOrgSlackRectification();
        if (CollUtil.isNotEmpty(orgSlackRectification)) {
            OrgSlackRectification entity = orgSlackRectification.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgSlackRectification.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                orgSlackRectification.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.orgSlackRectificationService.saveBatch(orgSlackRectification, orgSlackRectification.size());
            orgSlackRectification.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgSpecialNature");
        List<OrgSpecialNature> orgSpecialNature = xmlNode.getOrgSpecialNature();
        if (CollUtil.isNotEmpty(orgSpecialNature)) {
            OrgSpecialNature entity = orgSpecialNature.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgSpecialNature.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                orgSpecialNature.forEach((model) -> {
                    model.setId(null);

                });
            }

            this.orgSpecialNatureService.saveBatch(orgSpecialNature, orgSpecialNature.size());
            orgSpecialNature.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "orgTownshipLeaderships");
        List<OrgTownshipLeadership> orgTownshipLeaderships = xmlNode.getOrgTownshipLeaderships();
        if (CollUtil.isNotEmpty(orgTownshipLeaderships)) {
            OrgTownshipLeadership entity = orgTownshipLeaderships.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                orgTownshipLeaderships.forEach((model) -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }
            this.orgTownshipLeadershipService.saveBatch(orgTownshipLeaderships, orgTownshipLeaderships.size());
            orgTownshipLeaderships.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "representative");
        List<Representative> representative = xmlNode.getRepresentative();
        if (CollUtil.isNotEmpty(representative)) {
            Representative entity = representative.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                representative.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                representative.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.representativeService.saveBatch(representative, representative.size());
            representative.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "representativeContacts");
        List<RepresentativeContact> representativeContacts = xmlNode.getRepresentativeContacts();
        if (CollUtil.isNotEmpty(representativeContacts)) {
            RepresentativeContact entity = representativeContacts.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                representativeContacts.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                representativeContacts.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.representativeContactService.saveBatch(representativeContacts, representativeContacts.size());
            representativeContacts.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "representativeElect");
        List<RepresentativeElect> representativeElect = xmlNode.getRepresentativeElect();
        if (CollUtil.isNotEmpty(representativeElect)) {
            RepresentativeElect entity = representativeElect.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                representativeElect.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                representativeElect.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.representativeElectService.saveBatch(representativeElect, representativeElect.size());
            representativeElect.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "transferApproval");
        List<TransferApproval> transferApproval = xmlNode.getTransferApproval();
        if (CollUtil.isNotEmpty(transferApproval)) {
            TransferApproval entity = transferApproval.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                transferApproval.forEach((model) -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.transferApprovalService.saveBatch(transferApproval, transferApproval.size());
            transferApproval.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "transferEffectMem");
        List<TransferEffectMems> transferEffectMem = xmlNode.getTransferEffectMem();
        if (CollUtil.isNotEmpty(transferEffectMem)) {
            TransferEffectMems entity = transferEffectMem.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                transferEffectMem.forEach((model) -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.transferEffectMemsService.saveBatch(transferEffectMem, transferEffectMem.size());
            transferEffectMem.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "transferLog");
        List<TransferLog> transferLog = xmlNode.getTransferLog();
        if (CollUtil.isNotEmpty(transferLog)) {
            TransferLog entity = transferLog.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                transferLog.forEach((model) -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.transferLogService.saveBatch(transferLog, transferLog.size());
            transferLog.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "transferRecord");
        List<TransferRecord> transferRecord = xmlNode.getTransferRecord();
        if (CollUtil.isNotEmpty(transferRecord)) {
            TransferRecord entity = transferRecord.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                transferRecord.forEach((model) -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.transferRecordService.saveBatch(transferRecord, transferRecord.size());
            transferRecord.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "unit");
        List<Unit> unit = xmlNode.getUnit();
        if (CollUtil.isNotEmpty(unit)) {
            Unit entity = unit.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                unit.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.unitService.saveBatch(unit, unit.size());
            unit.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "unitAll");
        List<UnitAll> unitAll = xmlNode.getUnitAll();
        if (CollUtil.isNotEmpty(unitAll)) {
            UnitAll entity = unitAll.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                unitAll.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.unitAllService.saveBatch(unitAll, unitAll.size());
            unitAll.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "unitCitySituation");
        List<UnitCitySituation> unitCitySituation = xmlNode.getUnitCitySituation();
        if (CollUtil.isNotEmpty(unitCitySituation)) {
            UnitCitySituation entity = unitCitySituation.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                unitCitySituation.forEach((model) -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.unitCitySituationService.saveBatch(unitCitySituation, unitCitySituation.size());
            unitCitySituation.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "unitCollectiveEconomic");
        List<UnitCollectiveEconomic> unitCollectiveEconomic = xmlNode.getUnitCollectiveEconomic();
        if (CollUtil.isNotEmpty(unitCollectiveEconomic)) {
            UnitCollectiveEconomic entity = unitCollectiveEconomic.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                unitCollectiveEconomic.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                unitCollectiveEconomic.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.unitCollectiveEconomicService.saveBatch(unitCollectiveEconomic, unitCollectiveEconomic.size());
            unitCollectiveEconomic.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "unitCommittee");
        List<UnitCommittee> unitCommittee = xmlNode.getUnitCommittee();
        if (CollUtil.isNotEmpty(unitCommittee)) {
            UnitCommittee entity = unitCommittee.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                unitCommittee.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                unitCommittee.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.unitCommitteeService.saveBatch(unitCommittee, unitCommittee.size());
            unitCommittee.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "unitCommitteeElect");
        List<UnitCommitteeElect> unitCommitteeElect = xmlNode.getUnitCommitteeElect();
        if (CollUtil.isNotEmpty(unitCommitteeElect)) {
            UnitCommitteeElect entity = unitCommitteeElect.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                unitCommitteeElect.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                unitCommitteeElect.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.unitCommitteeElectService.saveBatch(unitCommitteeElect, unitCommitteeElect.size());
            unitCommitteeElect.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "unitCommunity");
        List<UnitCommunity> unitCommunity = xmlNode.getUnitCommunity();
        if (CollUtil.isNotEmpty(unitCommunity)) {
            UnitCommunity entity = unitCommunity.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                unitCommunity.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                unitCommunity.forEach((model) -> {
                    model.setId(null);

                });
            }

            this.unitCommunityService.saveBatch(unitCommunity, unitCommunity.size());
            unitCommunity.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "unitCountryside");
        List<UnitCountryside> unitCountryside = xmlNode.getUnitCountryside();
        if (CollUtil.isNotEmpty(unitCountryside)) {
            UnitCountryside entity = unitCountryside.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                unitCountryside.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                unitCountryside.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.unitCountrysideService.saveBatch(unitCountryside, unitCountryside.size());
            unitCountryside.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "unitExpandCollectiveEconomyList");
        List<UnitExpandCollectiveEconomy> unitExpandCollectiveEconomyList = xmlNode.getUnitExpandCollectiveEconomyList();
        if (CollUtil.isNotEmpty(unitExpandCollectiveEconomyList)) {
            UnitExpandCollectiveEconomy entity = unitExpandCollectiveEconomyList.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                unitExpandCollectiveEconomyList.forEach((model) -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.unitExpandCollectiveEconomyService.saveBatch(unitExpandCollectiveEconomyList, unitExpandCollectiveEconomyList.size());
            unitExpandCollectiveEconomyList.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "unitExtend");
        List<UnitExtend> unitExtend = xmlNode.getUnitExtend();
        if (CollUtil.isNotEmpty(unitExtend)) {
            UnitExtend entity = unitExtend.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                unitExtend.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                unitExtend.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.unitExtendService.saveBatch(unitExtend, unitExtend.size());
            unitExtend.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "unitIncome");
        List<UnitIncome> unitIncome = xmlNode.getUnitIncome();
        if (CollUtil.isNotEmpty(unitIncome)) {
            UnitIncome entity = unitIncome.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                unitIncome.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                unitIncome.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.unitIncomeService.saveBatch(unitIncome, unitIncome.size());
            unitIncome.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "unitOrgLinked");
        List<UnitOrgLinked> unitOrgLinked = xmlNode.getUnitOrgLinked();
        if (CollUtil.isNotEmpty(unitOrgLinked)) {
            UnitOrgLinked entity = unitOrgLinked.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                unitOrgLinked.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                unitOrgLinked.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.unitOrgLinkedService.saveBatch(unitOrgLinked, unitOrgLinked.size());
            unitOrgLinked.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "unitReportList");
        List<UnitReport> unitReportList = xmlNode.getUnitReportList();
        if (CollUtil.isNotEmpty(unitReportList)) {
            UnitReport entity = unitReportList.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                unitReportList.forEach((model) -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.unitReportService.saveBatch(unitReportList, unitReportList.size());
            unitReportList.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "unitResidentList");
        List<UnitResident> unitResidentList = xmlNode.getUnitResidentList();
        if (CollUtil.isNotEmpty(unitResidentList)) {
            UnitResident entity = unitResidentList.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                unitResidentList.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                unitResidentList.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.unitResidentService.saveBatch(unitResidentList, unitResidentList.size());
            unitResidentList.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "unitSecondary");
        List<UnitSecondary> unitSecondary = xmlNode.getUnitSecondary();
        if (CollUtil.isNotEmpty(unitSecondary)) {
            UnitSecondary entity = unitSecondary.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                unitSecondary.forEach((model) -> {
                    model.setId(null);
                    fieldValueDecryption(nk, model, encryptMap);
                });
            } else {
                unitSecondary.forEach((model) -> {
                    model.setId(null);
                });
            }

            this.unitSecondaryService.saveBatch(unitSecondary, unitSecondary.size());
            unitSecondary.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "unitSiteCondition");
        List<UnitSiteConditions> unitSiteCondition = xmlNode.getUnitSiteCondition();
        if (CollUtil.isNotEmpty(unitSiteCondition)) {
            UnitSiteConditions entity = unitSiteCondition.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                unitSiteCondition.forEach((model) -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.unitSiteConditionsService.saveBatch(unitSiteCondition, unitSiteCondition.size());
            unitSiteCondition.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "unitStreetsCadre");
        List<UnitStreetsCadres> unitStreetsCadre = xmlNode.getUnitStreetsCadre();
        if (CollUtil.isNotEmpty(unitStreetsCadre)) {
            UnitStreetsCadres entity = unitStreetsCadre.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                unitStreetsCadre.forEach((model) -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.unitStreetsCadresService.saveBatch(unitStreetsCadre, unitStreetsCadre.size());
            unitStreetsCadre.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "unitTownshipCadreInfoList");
        List<UnitTownshipCadreInfo> unitTownshipCadreInfoList = xmlNode.getUnitTownshipCadreInfoList();
        if (CollUtil.isNotEmpty(unitTownshipCadreInfoList)) {
            UnitTownshipCadreInfo entity = unitTownshipCadreInfoList.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                unitTownshipCadreInfoList.forEach((model) -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.unitTownshipCadreInfoService.saveBatch(unitTownshipCadreInfoList, unitTownshipCadreInfoList.size());
            unitTownshipCadreInfoList.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "activistTransferApprovalList");
        List<ActivistTransferApproval> activistTransferApprovalList = xmlNode.getActivistTransferApprovalList();
        if (CollUtil.isNotEmpty(activistTransferApprovalList)) {
            ActivistTransferApproval entity = activistTransferApprovalList.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                activistTransferApprovalList.forEach((model) -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.iActivistTransferApprovalService.saveOrUpdateBatch(activistTransferApprovalList, activistTransferApprovalList.size());
            activistTransferApprovalList.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "activistTransferLogList");
        List<ActivistTransferLog> activistTransferLogList = xmlNode.getActivistTransferLogList();
        if (CollUtil.isNotEmpty(activistTransferLogList)) {
            ActivistTransferLog entity = activistTransferLogList.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                activistTransferLogList.forEach((model) -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.iActivistTransferLogService.saveBatch(activistTransferLogList, activistTransferLogList.size());
            activistTransferLogList.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "activistTransferRecordList");
        List<ActivistTransferRecord> activistTransferRecordList = xmlNode.getActivistTransferRecordList();
        if (CollUtil.isNotEmpty(activistTransferRecordList)) {
            ActivistTransferRecord entity = activistTransferRecordList.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                activistTransferRecordList.forEach((model) -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.iActivistTransferRecordService.saveOrUpdateBatch(activistTransferRecordList, activistTransferRecordList.size());
            activistTransferRecordList.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "transferStatisticsList");
        List<TransferStatistics> transferStatisticsList = xmlNode.getTransferStatisticsList();
        if (CollUtil.isNotEmpty(transferStatisticsList)) {
            TransferStatistics entity = transferStatisticsList.get(0);
            if (encryptMap.containsKey(entity.getClass().getTypeName())) {
                transferStatisticsList.forEach((model) -> {
                    fieldValueDecryption(nk, model, encryptMap);
                });
            }

            this.transferStaticsService.saveBatch(transferStatisticsList, transferStatisticsList.size());
            transferStatisticsList.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "sysUserList");
        List<SysUser> sysUserList = xmlNode.getUserList();
        if (CollUtil.isNotEmpty(sysUserList)) {
            this.userService.saveBatch(sysUserList, sysUserList.size());
            sysUserList.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "roleList");
        List<Role> roleList = xmlNode.getRoleList();
        if (CollUtil.isNotEmpty(roleList)) {
            ArrayList<Role> roleArrayList = roleList.stream().collect(
                    Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(Role::getId))), ArrayList::new));
            List<Role> rList = this.updateReportPermission(roleArrayList);
            this.roleService.saveBatch(rList, rList.size());
            rList.clear();
        }
        log.info("开始写入节点：{}；{}", nk, "userRolePermissionList");
        List<UserRolePermission> userRolePermissionList = xmlNode.getUserRolePermissionList();
        if (CollUtil.isNotEmpty(userRolePermissionList)) {
            List<UserRolePermission> rolePermissionList = this.updateReportPermission(userRolePermissionList, null);
            this.userRolePermissionService.saveBatch(rolePermissionList, rolePermissionList.size());
            rolePermissionList.clear();
        }

        // 驻村数据保存
//        log.info("开始写入节点：{}；{}", nk, "驻村节点");
//        stSaveBatch(xmlNode, nk, encryptMap);
    }

    @SneakyThrows
    public static <T> void fieldValueDecryption(String nk, T clazz, Map<String, LinkedHashSet<String>> encryptMap) {
        // 导入时不再解密，数据已在导出时解密为明文
        return;

        // 原有解密逻辑保留但不执行
        /*
        Class<?> aClass = clazz.getClass();
        LinkedHashSet<String> set = encryptMap.get(aClass.getTypeName());
        if (CollUtil.isEmpty(set)) {
            return;
        }
        for (String name : set) {
            Field f = aClass.getDeclaredField(name);
            //设置属性的可访问性为true(暴力反射)
            f.setAccessible(true);
            //获取属性名称(map集合的键)
            Object value = f.get(clazz);
            if (value instanceof String && StringUtils.hasText(value.toString()) && nk != null) {
                try {
                    value = SM4Untils.decryptContent(nk, value.toString());
//                    value = SM4Untils.decryptContentJMJ(nk, value.toString());
                } catch (Exception e) {
                    log.error("nk:{},value:{}", nk, value);
                    log.error(e.getMessage());
                }
            }
            f.set(clazz, value);
            f.setAccessible(false);
        }
        */
    }

    /**
     * 导出时对数据进行解密处理（使用新的解密方式）
     * @param nk 节点key
     * @param clazz 实体对象
     * @param encryptMap 加密字段映射
     * @param <T> 泛型类型
     */
    @SneakyThrows
    public static <T> void fieldValueDecryptionForExport(String nk, T clazz, Map<String, LinkedHashSet<String>> encryptMap) {
        Class<?> aClass = clazz.getClass();
        LinkedHashSet<String> set = encryptMap.get(aClass.getTypeName());
        if (CollUtil.isEmpty(set)) {
            return;
        }
        for (String name : set) {
            Field f = aClass.getDeclaredField(name);
            //设置属性的可访问性为true(暴力反射)
            f.setAccessible(true);
            //获取属性名称(map集合的键)
            Object value = f.get(clazz);
            if (value instanceof String && StringUtils.hasText(value.toString()) && nk != null) {
                try {
                    // 导出时解密，使用新的解密方式
                    value = SM4Untils.decryptContentJMJ(nk, value.toString());
                } catch (Exception e) {
                    log.warn("导出解密失败，可能数据已是明文 nk:{},value:{}", nk, value);
                    // 解密失败时保持原值，可能数据已经是明文
                    throw new RuntimeException("导出解密失败，可能数据已是明文,value:"+value, e);
                }
            }
            f.set(clazz, value);
            f.setAccessible(false);
        }
    }

    /**
     * 批量处理列表数据的解密
     * @param nk 节点key
     * @param dataList 数据列表
     * @param encryptMap 加密字段映射
     * @param <T> 泛型类型
     */
    public static <T> void batchDecryptForExport(String nk, List<T> dataList, Map<String, LinkedHashSet<String>> encryptMap) {
        if (CollUtil.isEmpty(dataList)) {
            return;
        }
        T entity = dataList.get(0);
        if (encryptMap.containsKey(entity.getClass().getTypeName())) {
            dataList.forEach(model -> fieldValueDecryptionForExport(nk, model, encryptMap));
        }
    }

    /**
     * 对XmlNode中的所有数据进行解密处理
     * @param xmlNode XML节点对象
     * @param nk 节点key
     * @param encryptMap 加密字段映射
     */
    public static void decryptXmlNodeData(XmlNode xmlNode, String nk, Map<String, LinkedHashSet<String>> encryptMap) {
        try {
            Class<?> xmlNodeClass = xmlNode.getClass();
            Field[] fields = xmlNodeClass.getDeclaredFields();

            for (Field field : fields) {
                // 只处理List类型的字段
                if (List.class.isAssignableFrom(field.getType())) {
                    field.setAccessible(true);
                    List<?> dataList = (List<?>) field.get(xmlNode);

                    if (CollUtil.isNotEmpty(dataList)) {
                        Object firstEntity = dataList.get(0);
                        String entityTypeName = firstEntity.getClass().getTypeName();

                        // 检查是否需要解密
                        if (encryptMap.containsKey(entityTypeName)) {
                            // 统一使用nk作为解密key
                            batchDecryptForExport(nk, dataList, encryptMap);
                            log.debug("已解密表: {} ({}条记录)", field.getName(), dataList.size());
                        }
                    }
                    field.setAccessible(false);
                }
            }

            log.info("导出解密处理完成，节点：{}", nk);
        } catch (Exception e) {
            log.error("动态解密处理失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 更新报表权限
     *
     * @param roleList 权限集合
     * @return 更新后的权限集合
     */
    private List<Role> updateReportPermission(List<Role> roleList) {
        if (CollUtil.isEmpty(roleList)) {
            return Collections.emptyList();
        }
        for (Role role : roleList) {
            String permission = role.getPermission();
            StringBuilder builder = new StringBuilder(permission);
            //56 年度统计
            builder.replace(55, 56, "1");
            //63 年度统计、64 往年年度统计
            builder.replace(62, 64, "11");
            role.setPermission(builder.toString());
        }
        return roleList;
    }

    /**
     * 更新报表权限
     *
     * @param userRolePermissionList 权限集合
     * @param latestPermission       最新的权限
     * @return 更新后的权限集合
     */
    private List<UserRolePermission> updateReportPermission(List<UserRolePermission> userRolePermissionList, String latestPermission) {
        if (CollUtil.isEmpty(userRolePermissionList)) {
            return Collections.emptyList();
        }
        boolean flag = StringUtils.hasText(latestPermission);
        for (UserRolePermission userRolePermission : userRolePermissionList) {
            String permission = userRolePermission.getPermission();
            StringBuilder builder = new StringBuilder(permission);
            //56 年度统计
            builder.replace(55, 56, "1");
            //63 年度统计、64 往年年度统计
            builder.replace(62, 64, "11");
            if (flag) {
                if (latestPermission.length() > builder.length()) {
                    //处理内外网权限长度不一致问题
                    builder.append(latestPermission.substring(builder.length()));
                }
            }
            userRolePermission.setPermission(builder.toString());
        }
        return userRolePermissionList;
    }

    @Override
    public OutMessage<Object> clean(String id, String folder) {
        DataExchange dbDataExchange = dataExchangeMapper.selectById(id);
        if (Objects.isNull(dbDataExchange)) {
            return new OutMessage<>(Status.DATA_DOES_NOT_EXIST);
        }
        String clean = dbDataExchange.getClean();
        if (StrUtil.equals(CommonConstant.ONE, clean)) {
            return new OutMessage<>(Status.SUCCESS);
        }
        String fileUrl = dbDataExchange.getFileUrl();
        if (!FileUtil.exist(folder + fileUrl)) {
            return new OutMessage<>(Status.SUCCESS);
        }
        boolean del = FileUtil.del(folder + fileUrl);
        if (del) {
            DataExchange dataExchange = new DataExchange();
            dataExchange.setId(dbDataExchange.getId());
            dataExchange.setClean(CommonConstant.ONE);
            dataExchange.setUpdateAccount("内部管理员");
            dataExchange.setCleanTime(new Date());
            dataExchangeMapper.updateById(dataExchange);
            return new OutMessage<>(Status.SUCCESS);
        }
        return new OutMessage<>(Status.FAIL);
    }

    @Override
    public List<String> datName() {
        return dataExchangeMapper.datName(DAT_NAME_SQL);
    }

    @Override
    public void inherit(String id, String datName) {
        // 清除存在空闲超时的链接
        PgJdbcHelper.map.clear();
        List<Org> orgList = PgJdbcHelper.queryBySql(datName, sql1, Org.class, true);
        if (CollUtil.isNotEmpty(orgList)) {
            log.info("从{}数据库保存组织信息:{}条", datName, orgList.size());
            orgService.saveBatch(orgList, orgList.size());
            orgList.clear();
        }

        List<OrgAll> orgAllList = PgJdbcHelper.queryBySql(datName, sql2, OrgAll.class, true);
        if (CollUtil.isNotEmpty(orgAllList)) {
            log.info("从{}数据库保存组织信息:{}条", datName, orgAllList.size());
            orgAllService.saveBatch(orgAllList, orgAllList.size());
            orgAllList.clear();
        }

        List<OrgCommitteeElect> orgCommitteeElectList = PgJdbcHelper.queryBySql(datName, sql3, OrgCommitteeElect.class, true);
        if (CollUtil.isNotEmpty(orgCommitteeElectList)) {
            log.info("从{}数据库保存班子届次信息:{}条", datName, orgCommitteeElectList.size());
            orgCommitteeElectService.saveBatch(orgCommitteeElectList, orgCommitteeElectList.size());
            orgCommitteeElectList.clear();
        }

        List<OrgCommittee> orgCommitteeList = PgJdbcHelper.queryBySql(datName, sql4, OrgCommittee.class, true);
        if (CollUtil.isNotEmpty(orgCommitteeList)) {
            log.info("从{}数据库保存班子成员信息:{}条", datName, orgCommitteeList.size());
            orgCommitteeService.saveBatch(orgCommitteeList, orgCommitteeList.size());
            orgCommitteeList.clear();
        }

        List<OrgReward> orgRewardList = PgJdbcHelper.queryBySql(datName, sql5, OrgReward.class, true);
        if (CollUtil.isNotEmpty(orgRewardList)) {
            log.info("从{}数据库保存组织奖惩信息:{}条", datName, orgCommitteeList.size());
            orgRewardService.saveBatch(orgRewardList, orgRewardList.size());
            orgRewardList.clear();
        }

        List<OrgGroup> orgGroupList = PgJdbcHelper.queryBySql(datName, sql6, OrgGroup.class, true);
        if (CollUtil.isNotEmpty(orgGroupList)) {
            log.info("从{}数据库保存党小组信息:{}条", datName, orgGroupList.size());
            orgGroupService.saveBatch(orgGroupList, orgGroupList.size());
            orgGroupList.clear();
        }

        List<OrgGroupMember> orgGroupMemberList = PgJdbcHelper.queryBySql(datName, sql7, OrgGroupMember.class, true);
        if (CollUtil.isNotEmpty(orgGroupMemberList)) {
            log.info("从{}数据库保存党小组成员信息:{}条", datName, orgGroupMemberList.size());
            orgGroupMemberService.saveBatch(orgGroupMemberList, orgGroupMemberList.size());
            orgGroupMemberList.clear();
        }

        List<OrgRecognition> orgRecognitionList = PgJdbcHelper.queryBySql(datName, sql8, OrgRecognition.class, true);
        if (CollUtil.isNotEmpty(orgRecognitionList)) {
            log.info("从{}数据库保存党组织表彰信息:{}条", datName, orgRecognitionList.size());
            orgRecognitionService.saveBatch(orgRecognitionList, orgRecognitionList.size());
            orgRecognitionList.clear();
        }

        List<OrgRecognitionData> orgRecognitionDataList = PgJdbcHelper.queryBySql(datName, sql9, OrgRecognitionData.class, true);
        if (CollUtil.isNotEmpty(orgRecognitionDataList)) {
            log.info("从{}数据库保存党组织表彰对象信息:{}条", datName, orgRecognitionDataList.size());
            orgRecognitionDataService.saveBatch(orgRecognitionDataList, orgRecognitionDataList.size());
            orgRecognitionDataList.clear();
        }

        List<OrgRecognitionAll> orgRecognitionAllList = PgJdbcHelper.queryBySql(datName, sql10, OrgRecognitionAll.class, true);
        if (CollUtil.isNotEmpty(orgRecognitionAllList)) {
            log.info("从{}数据库保存党组织表彰信息:{}条", datName, orgRecognitionAllList.size());
            orgRecognitionAllService.saveBatch(orgRecognitionAllList, orgRecognitionAllList.size());
            orgRecognitionAllList.clear();
        }

        List<OrgRecognitionSituation> orgRecognitionSituationList = PgJdbcHelper.queryBySql(datName, sql11, OrgRecognitionSituation.class, true);
        if (CollUtil.isNotEmpty(orgRecognitionSituationList)) {
            log.info("从{}数据库保存党组织表彰追授情况信息:{}条", datName, orgRecognitionSituationList.size());
            orgRecognitionSituationService.saveBatch(orgRecognitionSituationList, orgRecognitionSituationList.size());
            orgRecognitionSituationList.clear();
        }

        List<OrgCaucus> orgCaucusList = PgJdbcHelper.queryBySql(datName, sql12, OrgCaucus.class, true);
        if (CollUtil.isNotEmpty(orgCaucusList)) {
            log.info("从{}数据库保存地方委员会情况信息:{}条", datName, orgCaucusList.size());
            orgCaucusService.saveBatch(orgCaucusList, orgCaucusList.size());
            orgCaucusList.clear();
        }

        List<OrgPartyCongressElect> orgPartyCongressElectList = PgJdbcHelper.queryBySql(datName, sql13, OrgPartyCongressElect.class, true);
        if (CollUtil.isNotEmpty(orgPartyCongressElectList)) {
            log.info("从{}数据库保存党代会届次信息:{}条", datName, orgPartyCongressElectList.size());
            orgPartyCongressElectService.saveBatch(orgPartyCongressElectList, orgPartyCongressElectList.size());
            orgPartyCongressElectList.clear();
        }

        List<OrgPartyCongressCommittee> orgPartyCongressCommitteeList = PgJdbcHelper.queryBySql(datName, sql14, OrgPartyCongressCommittee.class, true);
        if (CollUtil.isNotEmpty(orgPartyCongressCommitteeList)) {
            log.info("从{}数据库保存党代会成员信息:{}条", datName, orgPartyCongressCommitteeList.size());
            orgPartyCongressCommitteeService.saveBatch(orgPartyCongressCommitteeList, orgPartyCongressCommitteeList.size());
            orgPartyCongressCommitteeList.clear();
        }

        List<OrgPartyCongressCommitteeAll> orgPartyCongressCommitteeAllList = PgJdbcHelper.queryBySql(datName, sql15, OrgPartyCongressCommitteeAll.class, true);
        if (CollUtil.isNotEmpty(orgPartyCongressCommitteeAllList)) {
            log.info("从{}数据库保存党代会成员信息:{}条", datName, orgPartyCongressCommitteeAllList.size());
            orgPartyCongressCommitteeAllService.saveBatch(orgPartyCongressCommitteeAllList, orgPartyCongressCommitteeAllList.size());
            orgPartyCongressCommitteeAllList.clear();
        }

        List<MemTrain> memTrainList = PgJdbcHelper.queryBySql(datName, sql16, MemTrain.class, true);
        if (CollUtil.isNotEmpty(memTrainList)) {
            log.info("从{}数据库保存党员培训信息:{}条", datName, memTrainList.size());
            memTrainService.saveBatch(memTrainList, memTrainList.size());
            memTrainList.clear();
        }

        List<OrgNonPublicParty> orgNonPublicPartyList = PgJdbcHelper.queryBySql(datName, sql17, OrgNonPublicParty.class, true);
        if (CollUtil.isNotEmpty(orgNonPublicPartyList)) {
            log.info("从{}数据库保存党员培训信息:{}条", datName, memTrainList.size());
            orgNonPublicPartyService.saveBatch(orgNonPublicPartyList, orgNonPublicPartyList.size());
            orgNonPublicPartyList.clear();
        }

        List<OrgAppraisal> orgAppraisalList = PgJdbcHelper.queryBySql(datName, sql18, OrgAppraisal.class, true);
        if (CollUtil.isNotEmpty(orgAppraisalList)) {
            log.info("从{}数据库保存组织评议信息:{}条", datName, orgAppraisalList.size());
            orgAppraisalService.saveBatch(orgAppraisalList, orgAppraisalList.size());
            orgAppraisalList.clear();
        }

        List<OrgReviewers> orgReviewersList = PgJdbcHelper.queryBySql(datName, sql19, OrgReviewers.class, true);
        if (CollUtil.isNotEmpty(orgReviewersList)) {
            log.info("从{}数据库保存组织评议成员信息:{}条", datName, orgReviewersList.size());
            orgReviewersService.saveBatch(orgReviewersList, orgReviewersList.size());
            orgReviewersList.clear();
        }

        List<OrgTownshipLeadership> orgTownshipLeadershipList = PgJdbcHelper.queryBySql(datName, sql20, OrgTownshipLeadership.class, true);
        if (CollUtil.isNotEmpty(orgTownshipLeadershipList)) {
            log.info("从{}数据库保存乡镇信息:{}条", datName, orgTownshipLeadershipList.size());
            orgTownshipLeadershipService.saveBatch(orgTownshipLeadershipList, orgTownshipLeadershipList.size());
            orgTownshipLeadershipList.clear();
        }

        List<SysUser> dbUserList = PgJdbcHelper.queryBySql(datName, sql21, SysUser.class, false);
        // TODO 从gz_dj_TMP查询？
        List<String> finalAccountList = userService.findAllAccount();
        List<SysUser> userList = dbUserList.stream().filter(t -> !finalAccountList.contains(t.getAccount())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(userList)) {
            log.info("从{}数据库保存用户信息:{}条", datName, userList.size());
            userService.saveBatch(userList, userList.size());
            userList.clear();
        }

        List<UserRolePermission> dbUserRolePermissionList = PgJdbcHelper.queryBySql(datName, sql22, UserRolePermission.class, false);
        // TODO 从gz_dj_TMP查询？
        List<String> finalIdList = userRolePermissionService.findAllId();
        List<UserRolePermission> userRolePermissionList = dbUserRolePermissionList.stream().filter(t -> !finalIdList.contains(t.getId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(userRolePermissionList)) {
            LambdaQueryWrapper<Role> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Role::getRoleTypeCode, CommonConstant.ONE_INT).select(Role::getPermission).last(" LIMIT 1 ");
            // TODO 从gz_dj_TMP查询？
            Role role = roleService.getOne(queryWrapper);
            //最新的权限
            String latestPermission = Objects.nonNull(role) ? role.getPermission() : null;
            List<UserRolePermission> rolePermissionList = this.updateReportPermission(userRolePermissionList, latestPermission);
            if (CollUtil.isNotEmpty(rolePermissionList)) {
                log.info("从{}数据库保存用户权限信息:{}条", datName, rolePermissionList.size());
                userRolePermissionService.saveBatch(rolePermissionList, rolePermissionList.size());
                rolePermissionList.clear();
            }
        }
//        DataExchange dataExchange = new DataExchange();
//        dataExchange.setId(id);
//        dataExchange.setInherit(CommonConstant.ONE);
//        dataExchange.setUpdateAccount("内部管理员");
//        dataExchange.setInheritTime(new Date());
//        dataExchangeMapper.updateById(dataExchange);
        log.info("=====================已从数据库:{}继承数据完成=====================", datName);
        DynamicUtil.ds((v) -> {
            log.info("-----------start-----------执行计算省内非本组织单位党员相关信息获取-----------------------");
            jdbcTemplate.execute(SqlCommon.REPAIR_SQL1);
            log.info("-----------end-----------执行计算省内非本组织单位党员相关信息获取---------------------");

            log.info("-----------start-----------流动党员修复经济类型-----------------------");
            jdbcTemplate.execute(SqlCommon.REPAIR_SQL2);
            log.info("-----------end-----------流动党员修复经济类型-----------------------");

            log.info("-----------start-----------同步流动党员统计表-----------------------");
            memFlow1AllService.syncMemFlowStatistics();
            log.info("-----------end-----------同步流动党员统计表-----------------------");

            return null;
        }, DynamicUtil.DS_GZ_DJ_TMP);
    }

    /**
     * 驻村导入zip数据
     *
     * @param xmlNode
     * @param nk
     * @param encryptMap
     */
    @Override
    public void stSaveBatch(XmlNode xmlNode, String nk, LinkedHashMap<String, LinkedHashSet<String>> encryptMap) {
        Map<String, XmlNodeExtendEnum> xmlNodeExtendEnumMap = XmlNodeUtil.XML_NODE_REFLECT_MAP;
        xmlNodeExtendEnumMap.forEach((fieldName, xmlNodeExtendEnum) -> {
            // 获取数据
            Object val = ReflectUtil.invoke(xmlNode, "get" + StrUtil.upperFirst(fieldName));
            List<?> entityList;
            if (Objects.nonNull(val) && (entityList = (List<?>) val).size() > 0) {
                // 获取实现类
                String key = StrUtil.lowerFirst(xmlNodeExtendEnum.getImplClazz().getSimpleName());
                if (stIServiceMap.containsKey(key)) {
                    entityList.forEach((model) -> {
                        if (encryptMap.containsKey(model.getClass().getTypeName())) {
                            fieldValueDecryption(nk, model, encryptMap);
                        }
                    });
                    // 获取service实现类
                    StIService<?> iService = stIServiceMap.get(key);
                    // 调用批量保存接口
                    ReflectUtil.invoke(iService, "saveBatch", entityList, entityList.size());
                }
            }
        });
    }

    /**
     * xml导入数据到临时库
     *
     * @param xmlPath
     * @return
     */
    @Override
    public boolean newImportXml(File xmlPath) {
        String allUserPath = xmlPath.getPath() + File.separator + FileConstant.PACKAGE_NAME + File.separator + "data";
        File dirFile = new File(allUserPath);
        if (!(dirFile.exists() && dirFile.isDirectory())) {
            log.warn("文件夹{}不存在.", dirFile.getPath());
            return false;
        }
        //加载所有xml文件
        List<File> fileList = FileUtil.loopFiles(dirFile);
        // 添加all_user_tmp 临时库数据源
        boolean flag = dynamicService.add(DynamicUtil.DS_ALL_USER_TMP);
        if (!flag) {
            log.warn("数据源{}添加失败.", DynamicUtil.DS_ALL_USER_TMP);
            return false;
        }
        // 添加GZ_DJ_TMP临时库数据源
        flag = dynamicService.add(DynamicUtil.DS_GZ_DJ_TMP);
        if (!flag) {
            log.warn("数据源{}添加失败.", DynamicUtil.DS_GZ_DJ_TMP);
            return false;
        }
        final LinkedHashMap<String, LinkedHashSet<String>> encryptMap = ReflectionUtil.ENCRYPT_MAP;
        log.info("=======================================开始处理数据导入=======================================");
        fileList.parallelStream().forEach((file) -> {
                    String fileName1 = file.getName();
                    log.info("正在写入文件：{}", fileName1);
                    XmlNode xmlNode = XmlUtil.readValue(file, XmlNode.class);
                    if (Objects.nonNull(xmlNode)) {
                        final String nk = xmlNode.getNk();
                        if (fileName1.toLowerCase().startsWith("vc_pleas_destroy_holiday")) {
                            // 请销假数据，导入all_user_tmp临时库
                            log.info("文件写入数据源：{}", DynamicUtil.DS_ALL_USER_TMP);
                            DynamicUtil.ds((v) -> {
                                this.saveBatchTmp(xmlNode, nk, encryptMap);
                                return v;
                            }, DynamicUtil.DS_ALL_USER_TMP);
                        } else {
                            log.info("文件写入数据源：{}", DynamicUtil.DS_GZ_DJ_TMP);
                            // 将数据导入gz_dj_tmp临时数据源
                            DynamicUtil.ds((v) -> {
                                this.saveBatchTmp(xmlNode, nk, encryptMap);
                                return v;
                            }, DynamicUtil.DS_GZ_DJ_TMP);
                        }

                    }
                });
        log.info("=======================================结束处理数据导入=======================================");
        return true;
    }

    /**
     * 导出附件
     *
     * @param devOpsList
     */
    @Override
    public void exportFile(List<DevOps> devOpsList) {
        log.info("开始下载附件");
        String outPath = FileConstant.PACKAGE_PATH + File.separator + "file" + File.separator;
        File outFile = new File(outPath);
        if (!outFile.exists()) {
            outFile.mkdirs();
        }
        File[] files = outFile.listFiles();
        if (files != null && files.length > 0) {
            Arrays.stream(files).forEach(FileUtil::del);
        }
        Map<String, SshjSftp> sshMap = new ConcurrentHashMap<>();
        Map<String, InternalProperties.SshProperties> sshPropMap = new ConcurrentHashMap<>();
        // 多线程分组处理
        List<CompletableFuture<Object>> supplierList = new ArrayList<>();
        // 连接sftp
        try {
            int k = 0;
            // 迁移运维服务文件
            String allUserUpload = BaseController.basePath + File.separator + "/upload";
            File allUserUploadFile = new File(allUserUpload);
            if (allUserUploadFile.exists() && allUserUploadFile.listFiles().length > 0) {
                log.info("正在下载附件，节点：all_user");
                String allUserZip = outPath + File.separator + "all_user.zip";
                cn.hutool.core.util.ZipUtil.zip(allUserUpload, allUserZip, true);
            }
            List<String> devOpsIdList = devOpsList.stream().map(DevOps::getDatname).collect(Collectors.toList());
            LambdaQueryWrapper<DevOps> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(DevOps::getDatname, devOpsIdList);
            queryWrapper.select(DevOps::getDatname, DevOps::getServerLocation, DevOps::getServerDirectory);
            List<DevOps> opsList = devOpsService.list(queryWrapper);
            for (DevOps devOps : opsList) {
                k++;
                supplierList.add(CompletableFuture.supplyAsync(
                        () -> {
                            SshjSftp sshjSftp;
                            String zipName = StrUtil.format("{}_tmp_file.zip", devOps.getDatname());

                            log.info("正在下载附件，节点：{}", devOps.getDatname());
                            // 服务路径
                            String serverDirectory = devOps.getServerDirectory();
                            final String host = devOps.getServerLocation();
                            if (!sshMap.containsKey(host)) {
                                InternalProperties.SshProperties ssh = new InternalProperties.SshProperties();
                                ssh.setSshHost(host);
                                ssh.setSshPort(22);
                                if (StrUtil.containsAny(host, "37", "39", "40")) {
                                    ssh.setSshUser("root");
                                    ssh.setSshPass("Sora@2098%");
                                } else if (StrUtil.equals(host, "*************")) {
                                    ssh.setSshUser("root");
                                    ssh.setSshPass("dzgb@zt.423");
                                } else if (StrUtil.equals(host, "*************")) {
                                    ssh.setSshUser("root");
                                    ssh.setSshPass("Gzdj%1419!");
                                } else if (StrUtil.equals(host, "************")) {
                                    ssh.setSshUser("root");
                                    ssh.setSshPass("&GZY)#A8AA");
                                } else {
                                    ssh.setSshUser("dwxt");
                                    ssh.setSshPass("Sora@2098%");
                                }
                                sshMap.put(host, ShellUtil.sshjSftp(ssh));
                                sshPropMap.put(host, ssh);
                            }

                            sshjSftp = sshMap.get(host);
                            InternalProperties.SshProperties sshProperties = sshPropMap.get(host);
                            sshjSftp.reconnectIfTimeout();

                            // cd 指定目录
                            String zipParentPath = serverDirectory + ShellUtil.SERVER_CONFIG_PATH;
                            if (!ShellUtil.checkPath(sshProperties, serverDirectory + ShellUtil.SERVER_CONFIG_PATH)) {
                                log.warn("{}服务附件cd 指定目录（{}）失败", devOps.getDatname(), serverDirectory + ShellUtil.SERVER_CONFIG_PATH);
                                zipParentPath = serverDirectory + ShellUtil.CONFIG_PATH;
                                if (!ShellUtil.checkPath(sshProperties, serverDirectory + ShellUtil.CONFIG_PATH)) {
                                    log.warn("{}服务附件cd 指定目录（{}）失败", devOps.getDatname(), serverDirectory + ShellUtil.CONFIG_PATH);
                                    return null;
                                }
                            }
                            Map<String, String> zipParam = new HashMap<String, String>();
                            zipParam.put("source", "upload");
                            zipParam.put("dest", zipName);
                            zipParam.put("sourceParent", zipParentPath);

                            // 清空压缩包
                            List<String> cmdList = new CopyOnWriteArrayList<>();
                            cmdList.add(StrUtil.format(ShellUtil.CD_ZIP_SHELL, zipParam));
                            cmdList.add(StrUtil.format(ShellUtil.RM_FILE_ZIP, zipParam));
                            cmdList.add(StrUtil.format(ShellUtil.ZIP_SHELL, zipParam));
                            // 压缩upload文件
                            ShellUtil.cmdExec(sshProperties, cmdList.toArray(new String[]{}));
                            // 压缩包路径
                            String zipPath = zipParentPath + "/" + zipName;
                            // 下载文件到本地
                            sshjSftp.download(zipPath, outFile);
                            return null;
                        }, mySimpleAsync));

                if (supplierList.size() % 5 == 0 || k == devOpsList.size()) {
                    CompletableFuture.allOf(supplierList.toArray(new CompletableFuture[0])).join();
                    supplierList.clear();
                }
            }
        } finally {
            sshMap.forEach((k, v) -> {
                if (v != null) {
                    v.close();
                }
            });
        }
    }

    @Override
    public void deleteFile() {
        try {
            String outPath = FileConstant.PACKAGE_PATH + File.separator + "file" + File.separator;
            File outFile = new File(outPath);
            if (!outFile.exists()) {
                outFile.mkdirs();
            }
            File[] files = outFile.listFiles();
            if (files != null && files.length > 0) {
                Arrays.stream(files).forEach(FileUtil::del);
            }
        } catch (Exception e) {
            log.info("删除附件异常：", e);
        }
    }

    /**
     * 驻村查询数据并设置到导出对象中
     *
     * @param database
     * @param org
     */
    private List<CompletableFuture<Map<String, List<?>>>> stAsync(String database, Org org) {
        Map<String, XmlNodeExtendEnum> xmlNodeExtendEnumMap = XmlNodeUtil.XML_NODE_REFLECT_MAP;
        List<CompletableFuture<Map<String, List<?>>>> supplierList = new ArrayList<>();
        xmlNodeExtendEnumMap.forEach((fieldName, xmlNodeExtendEnum) -> {
            // 1、按机构层级码查询：org不为空 && 是否通过orgLevelCode查询 == true； 2、每个数据库全量查询一次：org为空&& 是否通过orgLevelCode查询 == false
            if ((Objects.nonNull(org) && xmlNodeExtendEnum.getIsQueryOrgLevelCode().equals(Boolean.TRUE)) ||
                    (Objects.isNull(org) && xmlNodeExtendEnum.getIsQueryOrgLevelCode().equals(Boolean.FALSE))) {
                // 首字母大写 字段
                String upperFieldName = StrUtil.upperFirst(fieldName);
                // 通过类名从容器中查找实现类对象
                String key = StrUtil.lowerFirst(xmlNodeExtendEnum.getImplClazz().getSimpleName());
                if (stIServiceMap.containsKey(key)) {
                    StIService<?> iService = stIServiceMap.get(key);
                    supplierList.add(CompletableFuture.supplyAsync(
                            () -> task(iService, upperFieldName, database, org), mySimpleAsync));
                }
            }
        });
        return supplierList;
    }

    /**
     * 执行查询语句
     *
     * @param iService
     * @param upperFieldName
     * @param database
     * @param org
     * @return
     */
    private Map<String, List<?>> task(StIService<?> iService, String upperFieldName, String database, Org org) {
        Map<String, List<?>> map = new HashMap<>();
        map.put(upperFieldName, iService.findAllList(database, org));
        return map;
    }
}
