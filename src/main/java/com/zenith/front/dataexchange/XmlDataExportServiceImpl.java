package com.zenith.front.dataexchange;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import com.fasterxml.jackson.dataformat.xml.XmlFactory;
import com.zenith.front.entity.model.*;
import com.zenith.front.untils.JsonUtil;
import com.zenith.front.untils.PgUtil;
import com.zenith.front.untils.XmlUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据导出
 *
 * <AUTHOR>
 * @date 2021/12/21
 */
public class XmlDataExportServiceImpl {

    private static final Logger log = LoggerFactory.getLogger(XmlDataExportServiceImpl.class);

    /**
     * ccp_representative_elect
     */
    public List<RepresentativeElect> getRepresentativeElect(String database, Org org) {
        return PgUtil.executeQuery(database, "SELECT t1.* from ccp_representative_elect t1 LEFT JOIN ccp_org on ccp_org.code=t1.org_code WHERE t1.delete_time is null and ccp_org.org_code like  '" + org.getOrgCode() + "%'", null)
                .stream().map(v -> JsonUtil.toBean(v, RepresentativeElect.class)).collect(Collectors.toList());
    }

    /**
     * ccp_representative_contact
     */
    public List<RepresentativeContact> getRepresentativeContact(String database, Org org) {
        return PgUtil.executeQuery(database, "SELECT t1.* from ccp_representative_contact t1 LEFT JOIN ccp_org on ccp_org.code=t1.org_code WHERE t1.delete_time is null and ccp_org.org_code like  '" + org.getOrgCode() + "%'", null)
                .stream().map(v -> JsonUtil.toBean(v, RepresentativeContact.class)).collect(Collectors.toList());
    }

    /**
     * ccp_representative
     */
    public List<Representative> getRepresentative(String database, Org org) {
        return PgUtil.executeQuery(database, "SELECT * from ccp_representative WHERE delete_time is null and representative_org_code like  '" + org.getOrgCode() + "%'", null)
                .stream().map(v -> JsonUtil.toBean(v, Representative.class)).collect(Collectors.toList());
    }

    /**
     * ccp_org_township_leadership
     */
    public List<OrgTownshipLeadership> getOrgTownshipLeadership(String database, Org org) {
        return PgUtil.executeQuery(database, "SELECT t1.* from ccp_org_township_leadership t1 LEFT JOIN ccp_org on ccp_org.code=t1.org_code WHERE t1.delete_time is null and ccp_org.org_code like '" + org.getOrgCode() + "%'", null)
                .stream().map(v -> JsonUtil.toBean(v, OrgTownshipLeadership.class)).collect(Collectors.toList());
    }

    /**
     * ccp_transfer_record
     */
    public List<TransferRecord> getTransferRecord(String database, Org org) {
        return PgUtil.executeQuery(database, "SELECT\n" +
                        "\tccp_transfer_record.ID,\n" +
                        "\tccp_transfer_record.user_id,\n" +
                        "\tccp_transfer_record.NAME,\n" +
                        "\tccp_transfer_record.mem_id,\n" +
                        "\tccp_transfer_record.src_org_id,\n" +
                        "\tccp_transfer_record.src_org_name,\n" +
                        "\tccp_transfer_record.target_org_id,\n" +
                        "\tccp_transfer_record.target_org_name,\n" +
                        "\tccp_transfer_record.common_org_id,\n" +
                        "\tccp_transfer_record.common_org_name,\n" +
                        "\tCAST ( src_org_relation AS VARCHAR ) AS src_org_relation,\n" +
                        "\tCAST ( target_org_relation AS VARCHAR ) AS target_org_relation,\n" +
                        "\tccp_transfer_record.mem_fee_end_time,\n" +
                        "\tccp_transfer_record.mem_fee_standard,\n" +
                        "\tccp_transfer_record.out_type,\n" +
                        "\tccp_transfer_record.in_type,\n" +
                        "\tccp_transfer_record.TYPE,\n" +
                        "\tccp_transfer_record.current_approval_id,\n" +
                        "\tccp_transfer_record.status,\n" +
                        "\tccp_transfer_record.reason,\n" +
                        "\tccp_transfer_record.create_time,\n" +
                        "\tccp_transfer_record.update_time,\n" +
                        "\tCAST ( extra_data AS VARCHAR ) AS extra_data,\n" +
                        "\tccp_transfer_record.letter_url,\n" +
                        "\tccp_transfer_record.effect_mems,\n" +
                        "\tccp_transfer_record.d92_code,\n" +
                        "\tccp_transfer_record.d92_name,\n" +
                        "\tccp_transfer_record.remark,\n" +
                        "\tCAST ( src_org_relation_rel AS VARCHAR ) AS src_org_relation_rel,\n" +
                        "\tCAST ( target_org_relation_rel AS VARCHAR ) AS target_org_relation_rel,\n" +
                        "\tccp_transfer_record.whether_extend_prep_period,\n" +
                        "\tccp_transfer_record.data_text,\n" +
                        "\tccp_transfer_record.transfer_out_time,\n" +
                        "\tccp_transfer_record.d146_code,\n" +
                        "\tccp_transfer_record.d146_name,\n" +
                        "\tccp_transfer_record.report_time,\n" +
                        "\tccp_transfer_record.out_d04_code,\n" +
                        "\tccp_transfer_record.letter_validity,\n" +
                        "\tccp_transfer_record.target_party_branch,\n" +
                        "\tccp_transfer_record.letter_number,\n" +
                        "\tccp_org.org_code \n" +
                        "FROM\n" +
                        "\tccp_transfer_record\n" +
                        "\tLEFT JOIN ccp_org ON ccp_transfer_record.src_org_id :: TEXT = ccp_org.code :: TEXT \n" +
                        "WHERE\n" +
                        "\tccp_org.delete_time IS NULL \n" +
                        "\tAND org_code LIKE '" + org.getOrgCode() + "%' UNION\n" +
                        "SELECT\n" +
                        "\tccp_transfer_record.ID,\n" +
                        "\tccp_transfer_record.user_id,\n" +
                        "\tccp_transfer_record.NAME,\n" +
                        "\tccp_transfer_record.mem_id,\n" +
                        "\tccp_transfer_record.src_org_id,\n" +
                        "\tccp_transfer_record.src_org_name,\n" +
                        "\tccp_transfer_record.target_org_id,\n" +
                        "\tccp_transfer_record.target_org_name,\n" +
                        "\tccp_transfer_record.common_org_id,\n" +
                        "\tccp_transfer_record.common_org_name,\n" +
                        "\tCAST ( src_org_relation AS VARCHAR ) AS src_org_relation,\n" +
                        "\tCAST ( target_org_relation AS VARCHAR ) AS target_org_relation,\n" +
                        "\tccp_transfer_record.mem_fee_end_time,\n" +
                        "\tccp_transfer_record.mem_fee_standard,\n" +
                        "\tccp_transfer_record.out_type,\n" +
                        "\tccp_transfer_record.in_type,\n" +
                        "\tccp_transfer_record.TYPE,\n" +
                        "\tccp_transfer_record.current_approval_id,\n" +
                        "\tccp_transfer_record.status,\n" +
                        "\tccp_transfer_record.reason,\n" +
                        "\tccp_transfer_record.create_time,\n" +
                        "\tccp_transfer_record.update_time,\n" +
                        "\tCAST ( extra_data AS VARCHAR ) AS extra_data,\n" +
                        "\tccp_transfer_record.letter_url,\n" +
                        "\tccp_transfer_record.effect_mems,\n" +
                        "\tccp_transfer_record.d92_code,\n" +
                        "\tccp_transfer_record.d92_name,\n" +
                        "\tccp_transfer_record.remark,\n" +
                        "\tCAST ( src_org_relation_rel AS VARCHAR ) AS src_org_relation_rel,\n" +
                        "\tCAST ( target_org_relation_rel AS VARCHAR ) AS target_org_relation_rel,\n" +
                        "\tccp_transfer_record.whether_extend_prep_period,\n" +
                        "\tccp_transfer_record.data_text,\n" +
                        "\tccp_transfer_record.transfer_out_time,\n" +
                        "\tccp_transfer_record.d146_code,\n" +
                        "\tccp_transfer_record.d146_name,\n" +
                        "\tccp_transfer_record.report_time,\n" +
                        "\tccp_transfer_record.out_d04_code,\n" +
                        "\tccp_transfer_record.letter_validity,\n" +
                        "\tccp_transfer_record.target_party_branch,\n" +
                        "\tccp_transfer_record.letter_number,\n" +
                        "\tccp_org.org_code \n" +
                        "FROM\n" +
                        "\tccp_transfer_record\n" +
                        "\tLEFT JOIN ccp_org ON ccp_transfer_record.target_org_id :: TEXT = ccp_org.code :: TEXT \n" +
                        "WHERE\n" +
                        "\tccp_org.delete_time IS NULL \n" +
                        "\tAND org_code LIKE '" + org.getOrgCode() + "%' and type in ('124','125')", null)
                .stream().map(v -> JsonUtil.toBean(v, TransferRecord.class)).collect(Collectors.toList());
    }

    /**
     * ccp_transfer_log
     */
    public List<TransferLog> getTransferLog(String database, Org org) {
        return PgUtil.executeQuery(database, "SELECT * FROM \"ccp_transfer_log\" WHERE handle_approval_id IN ( SELECT ID FROM \"ccp_transfer_approval\" WHERE record_id IN ( SELECT ID FROM ccp_transfer_record WHERE src_org_relation_rel ?? '" + org.getCode() + "' ) )", null)
                .stream().map(v -> JsonUtil.toBean(v, TransferLog.class)).collect(Collectors.toList());
    }

    /**
     * ccp_transfer_effect_mems
     */
    public List<TransferEffectMems> getTransferEffectMems(String database, Org org) {
        return PgUtil.executeQuery(database, "SELECT * from ccp_transfer_effect_mems WHERE 1=2 ", null)
                .stream().map(v -> JsonUtil.toBean(v, TransferEffectMems.class)).collect(Collectors.toList());
    }

    /**
     * ccp_transfer_approval
     */
    public List<TransferApproval> getTransferApproval(String database, Org org) {
        return PgUtil.executeQuery(database, "SELECT * FROM \"ccp_transfer_approval\" WHERE record_id IN ( SELECT ID FROM ccp_transfer_record WHERE src_org_relation_rel ?? '" + org.getCode() + "' )", null)
                .stream().map(v -> JsonUtil.toBean(v, TransferApproval.class)).collect(Collectors.toList());
    }

    /**
     * ccp_develop_step_log
     */
    public List<DevelopStepLog> getDevelopStepLog(String database, Org org) {
        return PgUtil.executeQuery(database, "SELECT t1.* from ccp_develop_step_log t1 LEFT JOIN ccp_org on ccp_org.code=t1.org_code WHERE ccp_org.org_code like '" + org.getOrgCode() + "%'", null)
                .stream().map(v -> JsonUtil.toBean(v, DevelopStepLog.class)).collect(Collectors.toList());
    }

    /**
     * ccp_develop_step_log_all
     */
    public List<DevelopStepLogAll> getDevelopStepLogAll(String database, Org org) {
        return PgUtil.executeQuery(database, "SELECT t1.* from ccp_develop_step_log_all t1 LEFT JOIN ccp_org on ccp_org.code=t1.org_code WHERE ccp_org.org_code like '" + org.getOrgCode() + "%'", null)
                .stream().map(v -> JsonUtil.toBean(v, DevelopStepLogAll.class)).collect(Collectors.toList());
    }

    /**
     * ccp_develop_plan_log
     */
    public List<DevelopPlanLog> getDevelopPlanLog(String database, Org org) {
        return PgUtil.executeQuery(database, "SELECT t1.* from ccp_develop_plan_log t1 LEFT JOIN ccp_org on ccp_org.code=t1.org_code WHERE t1.delete_time is null and ccp_org.org_code like '" + org.getOrgCode() + "%'", null)
                .stream().map(v -> JsonUtil.toBean(v, DevelopPlanLog.class)).collect(Collectors.toList());
    }

    /**
     * ccp_develop_plan
     */
    public List<DevelopPlan> getDevelopPlan(String database, Org org) {
        return PgUtil.executeQuery(database, "SELECT t1.* from ccp_develop_plan t1 LEFT JOIN ccp_org on ccp_org.code=t1.org_code WHERE t1.delete_time is null and ccp_org.org_code like '" + org.getOrgCode() + "%'", null)
                .stream().map(v -> JsonUtil.toBean(v, DevelopPlan.class)).collect(Collectors.toList());
    }

    /**
     * ccp_mem_train
     */
    public List<MemTrain> getMemTrain(String database, Org org) {
        return PgUtil.executeQuery(database, "SELECT t1.* from ccp_mem_train t1 LEFT JOIN ccp_org on ccp_org.code=t1.org_code WHERE t1.delete_time is null and ccp_org.org_code like '" + org.getOrgCode() + "%'", null)
                .stream().map(v -> JsonUtil.toBean(v, MemTrain.class)).collect(Collectors.toList());
    }

    /**
     * ccp_mem_reward
     */
    public List<MemReward> getMemReward(String database, Org org) {
        return PgUtil.executeQuery(database, "SELECT t1.* from ccp_mem_reward t1 LEFT JOIN ccp_org on ccp_org.code=t1.org_code WHERE t1.delete_time is null and ccp_org.org_code like '" + org.getOrgCode() + "%'", null)
                .stream().map(v -> JsonUtil.toBean(v, MemReward.class)).collect(Collectors.toList());
    }

    /**
     * ccp_mem_many
     */
    public List<MemMany> getMemMany(String database, Org org) {
        return PgUtil.executeQuery(database, "SELECT t1.* from ccp_mem_many t1 LEFT JOIN ccp_org on ccp_org.code=t1.mem_curr_org_code WHERE t1.delete_time is null and ccp_org.org_code like '" + org.getOrgCode() + "%'", null)
                .stream().map(v -> JsonUtil.toBean(v, MemMany.class)).collect(Collectors.toList());
    }

    /**
     * ccp_mem_log
     */
    public List<MemLog> getMemLog(String database, Org org) {
        return PgUtil.executeQuery(database, "SELECT t1.* from ccp_mem_log t1 LEFT JOIN ccp_org on ccp_org.code=t1.org_code WHERE t1.delete_time is null and ccp_org.org_code like '" + org.getOrgCode() + "%'", null)
                .stream().map(v -> JsonUtil.toBean(v, MemLog.class)).collect(Collectors.toList());
    }

    /**
     * ccp_mem_flow_log
     */
    public List<MemFlowLog> getMemFlowLog(String database, Org org) {
        return new ArrayList<>();
    }

    /**
     * ccp_mem_extend
     */
    public List<MemExtend> getMemExtend(String database, Org org) {
        return new ArrayList<>();
    }

    /**
     * ccp_mem_difficult
     */
    public List<MemDifficult> getMemDifficult(String database, Org org) {
        return PgUtil.executeQuery(database, "SELECT t1.* from ccp_mem_difficult t1 LEFT JOIN ccp_org on ccp_org.code=t1.org_code WHERE t1.delete_time is null and ccp_org.org_code like '" + org.getOrgCode() + "%'", null)
                .stream().map(v -> JsonUtil.toBean(v, MemDifficult.class)).collect(Collectors.toList());
    }

    /**
     * ccp_mem_report
     */
    public List<MemReport> getMemReport(String database, Org org) {
        return PgUtil.executeQuery(database, "SELECT t1.* from ccp_mem_report t1 LEFT JOIN ccp_org on ccp_org.code=t1.org_org_code WHERE t1.delete_time is null and ccp_org.org_code like '" + org.getOrgCode() + "%'", null)
                .stream().map(v -> JsonUtil.toBean(v, MemReport.class)).collect(Collectors.toList());
    }

    /**
     * ccp_unit_report
     */
    public List<UnitReport> getUnitReport(String database, Org org) {
        return PgUtil.executeQuery(database, "SELECT t1.* from ccp_unit_report t1 LEFT JOIN ccp_org on ccp_org.code=t1.create_org_code WHERE t1.delete_time is null and ccp_org.org_code like '" + org.getOrgCode() + "%'", null)
                .stream().map(v -> JsonUtil.toBean(v, UnitReport.class)).collect(Collectors.toList());
    }

    /**
     * ccp_mem_train_info
     */
    public List<MemTrainInfo> getMemTrainInfo(String database, Org org) {
        return PgUtil.executeQuery(database, "SELECT t1.* from ccp_mem_train_info t1 LEFT JOIN ccp_org on ccp_org.code=t1.org_code WHERE t1.delete_time is null and ccp_org.org_code like '" + org.getOrgCode() + "%'", null)
                .stream().map(v -> JsonUtil.toBean(v, MemTrainInfo.class)).collect(Collectors.toList());
    }

    /**
     * ccp_unit_resident
     */
    public List<UnitResident> getUnitResident(String database, Org org) {
        String sql = "SELECT ccp_unit_resident.* " +
                " FROM \"ccp_unit_resident\" " +
                " LEFT JOIN ccp_unit ON ccp_unit.code = ccp_unit_resident.unit_code " +
                " WHERE ccp_unit_resident.delete_time IS NULL" +
                " AND ccp_unit.delete_time IS NULL " +
                " AND ccp_unit.create_unit_org_code LIKE '" + org.getOrgCode() + "%'" +
                " ORDER BY ccp_unit_resident.id ";
        return PgUtil.executeQuery(database, sql, null)
                .stream().map(v -> JsonUtil.toBean(v, UnitResident.class)).collect(Collectors.toList());
    }

    /**
     * ccp_unit_township_cadre_info
     */
    public List<UnitTownshipCadreInfo> getUnitTownshipCadreInfo(String database, Org org) {
        String sql = "SELECT ccp_unit_township_cadre_info.* from ccp_unit_township_cadre_info"
                + " LEFT JOIN ccp_unit on ccp_unit.code = ccp_unit_township_cadre_info.unit_code and ccp_unit.delete_time is null"
                + " where ccp_unit_township_cadre_info.delete_time is null and ccp_unit.create_unit_org_code like '" + org.getOrgCode() + "%'";
        return PgUtil.executeQuery(database, sql, null)
                .stream().map(v -> JsonUtil.toBean(v, UnitTownshipCadreInfo.class)).collect(Collectors.toList());
    }

    public List<UnitExpandCollectiveEconomy> getUnitExpandCollectiveEconomy(String database, Org org) {
        String sql = "SELECT ccp_unit_expand_collective_economy.* " +
                " FROM \"ccp_unit_expand_collective_economy\" " +
                " LEFT JOIN ccp_unit ON ccp_unit.code = ccp_unit_expand_collective_economy.unit_code " +
                " WHERE ccp_unit_expand_collective_economy.delete_time IS NULL AND ccp_unit.delete_time IS NULL " +
                " AND ccp_unit.create_unit_org_code LIKE '" + org.getOrgCode() + "%' ";
        return PgUtil.executeQuery(database, sql, null)
                .stream().map(v -> JsonUtil.toBean(v, UnitExpandCollectiveEconomy.class)).collect(Collectors.toList());
    }

    /**
     * ccp_transfer_statistics
     */
    public List<TransferStatistics> getTransferStatistics(String database, Org org) {
        return PgUtil.executeQuery(database, "SELECT * from ccp_transfer_statistics WHERE delete_time is null and transfer_org_org_code like  '052%'", null)
                .stream().map(v -> JsonUtil.toBean(v, TransferStatistics.class)).collect(Collectors.toList());
    }

    /**
     * ccp_activist_transfer_approval
     */
    public List<ActivistTransferApproval> getActivistTransferApproval(String database, Org org) {
        return PgUtil.executeQuery(database, "SELECT * FROM \"ccp_activist_transfer_approval\" WHERE record_id IN ( SELECT ID FROM ccp_activist_transfer_record WHERE src_org_relation_rel ?? '" + org.getCode() + "' )", null)
                .stream().map(v -> JsonUtil.toBean(v, ActivistTransferApproval.class)).collect(Collectors.toList());
    }

    /**
     * ccp_activist_transfer_log
     */
    public List<ActivistTransferLog> getActivistTransferLog(String database, Org org) {
        String sql = "SELECT * FROM \"ccp_activist_transfer_log\" WHERE handle_approval_id IN ( SELECT ID FROM \"ccp_activist_transfer_approval\" WHERE record_id IN ( SELECT ID FROM ccp_activist_transfer_record WHERE src_org_relation_rel ?? '" + org.getCode() + "' ) )";
        return PgUtil.executeQuery(database, sql, null)
                .stream().map(v -> JsonUtil.toBean(v, ActivistTransferLog.class)).collect(Collectors.toList());
    }

    /**
     * ccp_activist_transfer_record
     */
    public List<ActivistTransferRecord> getActivistTransferRecord(String database, Org org) {
        String sql = "SELECT\n" +
                "\tccp_activist_transfer_record.\"id\",\n" +
                "\tccp_activist_transfer_record.user_id,\n" +
                "\tccp_activist_transfer_record.\"name\",\n" +
                "\tccp_activist_transfer_record.mem_id,\n" +
                "\tccp_activist_transfer_record.src_org_id,\n" +
                "\tccp_activist_transfer_record.src_org_name,\n" +
                "\tccp_activist_transfer_record.target_org_id,\n" +
                "\tccp_activist_transfer_record.target_org_name,\n" +
                "\tccp_activist_transfer_record.common_org_id,\n" +
                "\tccp_activist_transfer_record.common_org_name,\n" +
                "\tCAST ( src_org_relation AS VARCHAR ) AS src_org_relation,\n" +
                "\tCAST ( target_org_relation AS VARCHAR ) AS target_org_relation,\n" +
                "\tccp_activist_transfer_record.out_type,\n" +
                "\tccp_activist_transfer_record.in_type,\n" +
                "\tccp_activist_transfer_record.\"type\",\n" +
                "\tccp_activist_transfer_record.current_approval_id,\n" +
                "\tccp_activist_transfer_record.status,\n" +
                "\tccp_activist_transfer_record.reason,\n" +
                "\tccp_activist_transfer_record.create_time,\n" +
                "\tccp_activist_transfer_record.update_time,\n" +
                "\tCAST ( extra_data AS VARCHAR ) AS extra_data,\n" +
                "\tccp_activist_transfer_record.letter_url,\n" +
                "\tccp_activist_transfer_record.effect_mems,\n" +
                "\tccp_activist_transfer_record.d92_code,\n" +
                "\tccp_activist_transfer_record.d92_name,\n" +
                "\tccp_activist_transfer_record.remark,\n" +
                "\tCAST ( src_org_relation_rel AS VARCHAR ) AS src_org_relation_rel,\n" +
                "\tCAST ( target_org_relation_rel AS VARCHAR ) AS target_org_relation_rel,\n" +
                "\tccp_activist_transfer_record.whether_extend_prep_period,\n" +
                "\tccp_activist_transfer_record.data_text,\n" +
                "\tccp_activist_transfer_record.transfer_out_time,\n" +
                "\tccp_activist_transfer_record.d146_code,\n" +
                "\tccp_activist_transfer_record.d146_name,\n" +
                "\tccp_activist_transfer_record.report_time,\n" +
                "\tccp_activist_transfer_record.out_d04_code,\n" +
                "\tCAST ( extra_data_log AS VARCHAR ) AS extra_data_log, \n" +
                "\tCAST ( extra_data_process AS VARCHAR ) AS extra_data_process,\n" +
                "\tCAST ( extra_data_digital AS VARCHAR ) AS extra_data_digital,\n" +
                "\tCAST ( extra_data_digital_log AS VARCHAR ) AS extra_data_digital_log \n" +
                "FROM\n" +
                "\tccp_activist_transfer_record\n" +
                "\tLEFT JOIN ccp_org ON ccp_activist_transfer_record.src_org_id :: TEXT = ccp_org.code :: TEXT \n" +
                "WHERE\n" +
                "\tccp_org.delete_time IS NULL \n" +
                "\tAND org_code like '" + org.getOrgCode() + "%'";
        return PgUtil.executeQuery(database, sql, null)
                .stream().map(v -> JsonUtil.toBean(v, ActivistTransferRecord.class)).collect(Collectors.toList());
    }


    /**
     * ccp_org_flow
     */
    public List<OrgFlow2> getOrgFlow2(String database, Org org) {
        String sql = "select * from ccp_org_flow where delete_time is null";
        return PgUtil.executeQuery(database, sql, null)
                .stream().map(v -> JsonUtil.toBean(v, OrgFlow2.class)).collect(Collectors.toList());
    }

    /**
     * ccp_org_flow_audit
     */
    public List<OrgFlowAudit2> getOrgFlowAudit2(String database, Org org) {
        String sql = "select * from ccp_org_flow_audit where delete_time is null";
        return PgUtil.executeQuery(database, sql, null)
                .stream().map(v -> JsonUtil.toBean(v, OrgFlowAudit2.class)).collect(Collectors.toList());
    }

    /**
     * ccp_mem_flow_sign_audit
     *
     * @param database
     * @param org
     * @return
     */
    public List<MemFlowSignAudit> getMemFlowSignAudit(String database, Org org) {
        String sql = "select * from ccp_mem_flow_sign_audit where delete_time is null";
        return PgUtil.executeQuery(database, sql, null)
                .stream().map(v -> JsonUtil.toBean(v, MemFlowSignAudit.class)).collect(Collectors.toList());
    }

    /**
     * ccp_mem_flow_sign
     *
     * @param database
     * @param org
     * @return
     */
    public List<MemFlowSign2> getMemFlowSign2(String database, Org org) {
        String sql = "select * from ccp_mem_flow_sign where delete_time is null";
        return PgUtil.executeQuery(database, sql, null)
                .stream().map(v -> JsonUtil.toBean(v, MemFlowSign2.class)).collect(Collectors.toList());
    }

    /**
     * ccp_mem_develop_process
     */
    public List<MemDevelopProcess> getMemDevelopProcess(String database, Org org) {
        String sql = "select * from ccp_mem_develop_process where delete_time is null";
        return PgUtil.executeQuery(database, sql, null)
                .stream().map(v -> JsonUtil.toBean(v, MemDevelopProcess.class)).collect(Collectors.toList());
    }

    /**
     * ccp_mem_digital
     */
    public List<MemDigital> getMemDigital(String database, Org org) {
        String sql = "select * from ccp_mem_digital where delete_time is null";
        return PgUtil.executeQuery(database, sql, null)
                .stream().map(v -> JsonUtil.toBean(v, MemDigital.class)).collect(Collectors.toList());
    }

    /**
     * ccp_mem_digital_operation_log
     */
    public List<MemDigitalOperationLog> getMemDigitalOperationLog(String database, Org org) {
        String sql = "select * from ccp_mem_digital_operation_log where delete_time is null";
        return PgUtil.executeQuery(database, sql, null)
                .stream().map(v -> JsonUtil.toBean(v, MemDigitalOperationLog.class)).collect(Collectors.toList());
    }

    /**
     * ccp_mem_digital_count
     */
    public List<MemDigitalCount> getMemDigitalCount(String database, Org org) {
        String sql = "select * from ccp_mem_digital_count where delete_time is null";
        return PgUtil.executeQuery(database, sql, null)
                .stream().map(v -> JsonUtil.toBean(v, MemDigitalCount.class)).collect(Collectors.toList());
    }

    /**
     * 档案审核信息
     * ccp_mem_digital_audit
     */
    public List<MemDigitalAudit> getMemDigitalAudit(String database, Org org) {
        String sql = "select * from ccp_mem_digital_audit where delete_time is null";
        return PgUtil.executeQuery(database, sql, null)
                .stream().map(v -> JsonUtil.toBean(v, MemDigitalAudit.class)).collect(Collectors.toList());
    }

    /**
     * 导出档案表相关数据
     */
    public void exportMemDigitalData(String database, String nginxKey, Org org, File folder) {
        List<MemDigital> digitalList = this.getMemDigital(database, org);
        List<MemDevelopProcess> developProcessList = this.getMemDevelopProcess(database, org);
        List<MemDigitalOperationLog> digitalOperationLogList = this.getMemDigitalOperationLog(database, org);
        List<MemDigitalCount> memDigitalCount = this.getMemDigitalCount(database, org);
        List<MemDigitalAudit> memDigitalAuditList = this.getMemDigitalAudit(database, org);
        // 没有数据直接返回
        if (CollUtil.isEmpty(digitalList) && CollUtil.isEmpty(developProcessList) && CollUtil.isEmpty(digitalList) && CollUtil.isEmpty(memDigitalCount)
        && CollUtil.isEmpty(memDigitalAuditList)) {
            return;
        }
        try {
            String name = "ccp_mem_develop_process_" + DateUtil.format(new Date(), "yyyyMMdd") + "." + XmlFactory.FORMAT_NAME_XML.toLowerCase(Locale.ROOT);
            File file = new File(folder.getPath() + File.separator + name);
            if (!file.exists()) {
                FileUtil.mkParentDirs(file);
            } else {
                XmlNode node = XmlUtil.readValue(file, XmlNode.class);
                if (Objects.nonNull(node)) {
                    List<MemDigital> data1 = node.getMemDigitalList();
                    List<MemDevelopProcess> data2 = node.getMemDevelopProcessList();
                    List<MemDigitalOperationLog> data3 = node.getMemDigitalOperationLogList();
                    List<MemDigitalCount> data4 = node.getMemDigitalCountList();
                    List<MemDigitalAudit> data5 = node.getMemDigitalAuditList();


                    if (CollUtil.isEmpty(data1) && CollUtil.isEmpty(data2) && CollUtil.isEmpty(data3) && CollUtil.isEmpty(data4) && CollUtil.isEmpty(data5)) {
                        return;
                    }
                    digitalList.addAll(data1);
                    developProcessList.addAll(data2);
                    digitalOperationLogList.addAll(data3);
                    memDigitalCount.addAll(data4);
                    memDigitalAuditList.addAll(data5);
                }
            }
            XmlNode xmlNode = new XmlNode();
            xmlNode
                    .setMemDigitalList(digitalList)
                    .setMemDevelopProcessList(developProcessList)
                    .setMemDigitalOperationLogList(digitalOperationLogList)
                    .setMemDigitalCountList(memDigitalCount)
                    .setMemDigitalAuditList(memDigitalAuditList)
            ;
            XmlUtil.writeValue(file, xmlNode);
            digitalList.clear();
            developProcessList.clear();
            digitalOperationLogList.clear();
            memDigitalCount.clear();
        } catch (Exception e) {
            log.error("数据写入xml异常！", e);
        }
    }

}
