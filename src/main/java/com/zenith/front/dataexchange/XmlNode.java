package com.zenith.front.dataexchange;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.zenith.front.entity.model.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Accessors(chain = true)
@Getter
@Setter
@JacksonXmlRootElement(localName = "root")
public class XmlNode {

    /**
     * 机构码
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private String code;

    /**
     * 机构名称
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private String name;

    /**
     * 解密key
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private String nk;

    /**
     * 党支部标准化规范化建设达标尿范点情况
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgExampleSite> orgExampleSite;

    /**
     * 党支部工作开展情况
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgContactWork> orgContactWork;

    /**
     * 党支部工作联系人
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgContactMem> orgContactMem;

    /**
     *
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<DemocraticReview> democraticReview;

    /**
     *
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<DemocraticReviewLead> democraticReviewLead;

    /**
     *
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<DemocraticReviewMem> democraticReviewMem;

    /**
     *
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<DevelopPlan> developPlan;
    /**
     *
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<DevelopPlanLog> developPlanLog;

    /**
     *
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<DevelopStepLog> developStepLog;

    /**
     * ccp_develop_step_log_all
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<DevelopStepLogAll> developStepLogAll;

    /**
     * 锁定记录
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<LockFiledLog> lockFiledLog;

    /**
     * 逻辑校核忽略记录
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<LogicCheckIgnore> logicCheckIgnores;

    /**
     * 人员
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<Mem> mem;

    /**
     * 人员出国境
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<MemAbroad> memAbroad;

    /**
     * 人员all
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<MemAllInfo> memAllInfos;

    /**
     * 发展党员
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<MemDevelop> memDevelop;

    /**
     * 发展党员
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<MemDevelopAll> memDevelopAll;

    /**
     * 发展党员操作
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<MemDevelopOperation> memDevelopOperation;

    /**
     * 困难党员
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<MemDifficult> memDifficult;

    /**
     * 人员扩展表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<MemExtend> memExtend;

    /**
     * 人员流入流出
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<MemFlow2> memFlow2;

    /**
     * 人员流入流出All
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<MemFlow1All> memFlow1All;

    /**
     * 人员流入流出记录
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<MemFlowLog> memFlowLog;

    /**
     * 历史人员
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<MemHistory> memHistory;

    /**
     *
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<MemLog> memLog;

    /**
     *
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<MemMany> memMany;

    /**
     * 农村党建调度统计表 - 党员报表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<MemReport> memReportList;

    /**
     *
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<MemReward> memReward;

    /**
     *
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<MemRewardAll> memRewardAll;

    /**
     *
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<MemTrain> memTrain;

    /**
     * 党员培训
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<MemTrainInfo> memTrainInfoList;

    /**
     * 党组织
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<Org> org;

    /**
     * 党组织all
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgAll> orgAll;

    /**
     * 党组织民主评议
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgAppraisal> orgAppraisal;

    /**
     * 党组织考核信息
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgAssess> orgAssesses;

    /**
     * 党组织考核信息
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgCaucus> orgCaucus;

    /**
     * 党组织届次人员
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgCommittee> orgCommittee;

    /**
     * 党组织届次
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgCommitteeElect> orgCommitteeElect;

    /**
     *
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgDevelopRights> orgDevelopRights;

    /**
     * 党组织扩展
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgExtend> orgExtend;

    /**
     * 党组织党小组
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgGroup> orgGroup;

    /**
     * 党组织党小组成员
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgGroupMember> orgGroupMember;

    /**
     * 历史党组织
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgHistory> orgHistory;

    /**
     *
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgIndustry> orgIndustries;

    /**
     *
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgIndustryAll> orgIndustryAll;

    /**
     *
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgNonPublicParty> orgNonPublicParties;

    /**
     * 党组织
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgParty> orgParty;

    /**
     * 党代表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgPartyCongressCommittee> orgPartyCongressCommittee;

    /**
     * 党代表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgPartyCongressCommitteeAll> orgPartyCongressCommitteeAlls;

    /**
     * 党组织
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgPartyCongressElect> orgPartyCongressElect;

    /**
     * 党组织表彰
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgRecognition> orgRecognition;

    /**
     * 党组织表彰All
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgRecognitionAll> orgRecognitionAll;

    /**
     *
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgRecognitionData> orgRecognitionData;

    /**
     * 表彰追授情况
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgRecognitionSituation> orgRecognitionSituation;

    /**
     *
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgReport> orgReport;

    /**
     *
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgReviewers> orgReviewers;

    /**
     *
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgReward> orgReward;

    /**
     * 涣散组织
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgSlack> orgSlack;

    /**
     *
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgSlackAll> orgSlackAll;

    /**
     *
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgSlackRectification> orgSlackRectification;

    /**
     * 特殊性质的党组织
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgSpecialNature> orgSpecialNature;

    /**
     *
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgTownshipLeadership> orgTownshipLeaderships;

    /**
     *
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<Representative> representative;

    /**
     *
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<RepresentativeContact> representativeContacts;

    /**
     *
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<RepresentativeElect> representativeElect;

    /**
     *
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<TransferApproval> transferApproval;
    /**
     * 转接人数
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<TransferEffectMems> transferEffectMem;

    /**
     * 审批记录对应的日志
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<TransferLog> transferLog;

    /**
     * 组织关系转接
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<TransferRecord> transferRecord;

    /**
     * 单位
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<Unit> unit;
    /**
     * 单位All表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<UnitAll> unitAll;

    /**
     * 城市基层党建情况
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<UnitCitySituation> unitCitySituation;

    /**
     * 单位集体经济情况
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<UnitCollectiveEconomic> unitCollectiveEconomic;

    /**
     * 单位领导班子成员表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<UnitCommittee> unitCommittee;

    /**
     * 单位领导班子届次表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<UnitCommitteeElect> unitCommitteeElect;

    /**
     * 单位党小组
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<UnitCommunity> unitCommunity;

    /**
     * 单位班子成员
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<UnitCountryside> unitCountryside;

    /**
     * 农村党建调度表 - 财政扶持壮大村级集体经济情况
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<UnitExpandCollectiveEconomy> unitExpandCollectiveEconomyList;

    /**
     * 单位扩展属性表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<UnitExtend> unitExtend;

    /**
     * 收入情况
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<UnitIncome> unitIncome;

    /**
     * 单位组织关联表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<UnitOrgLinked> unitOrgLinked;

    /**
     * 农村党建调度表 - 单位报表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<UnitReport> unitReportList;

    /**
     * 驻村干部
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<UnitResident> unitResidentList;

    /**
     * 收入情况
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<UnitSecondary> unitSecondary;

    /**
     * 经费场地情况
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<UnitSiteConditions> unitSiteCondition;

    /**
     * 单位街道干部表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<UnitStreetsCadres> unitStreetsCadre;

    /**
     * 乡镇班子
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<UnitTownshipCadreInfo> unitTownshipCadreInfoList;
    /**
     * 关系转接统计表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<TransferStatistics> transferStatisticsList;
    /**
     * 积极分子转接
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<ActivistTransferApproval> activistTransferApprovalList;

    @JacksonXmlElementWrapper(useWrapping = false)
    private List<ActivistTransferLog> activistTransferLogList;

    @JacksonXmlElementWrapper(useWrapping = false)
    private List<ActivistTransferRecord> activistTransferRecordList;


    /**
     * 用户
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<SysUser> userList;

    /**
     * 角色权限
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<UserRolePermission> userRolePermissionList;

    /**
     * 角色
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<Role> roleList;


// 以下为驻村信息

    /**
     * 驻村用户表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcSysUser> vcSysUserList;

    /**
     * 驻村附件表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.SysFile> sysFileList;

    /**
     *
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcA01> vcA01List;

    /**
     * 家庭成员及社会关系信息集
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcA36> vcA36List;

    /**
     * 业务培训
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcBusinessTraining> vcBusinessTrainingList;

    /**
     * 干部体检
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcCadreExamination> vcCadreExaminationList;

    /**
     * 干部保险
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcCadreInsurance> vcCadreInsuranceList;

    /**
     * 干部补贴
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcCadreSubsidy> vcCadreSubsidyList;

    /**
     * 家庭成员表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcFamilyMembers> vcFamilyMembersList;

    /**
     * 流程审核主表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcFlowDetail> vcFlowDetailList;

    /**
     * 流程的详情信息
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcFlowInfo> vcFlowInfoList;

    /**
     * 谈心谈话
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcHeartTalk> vcHeartTalkList;

    /**
     * 管理考核
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcManagementAssessment> vcManagementAssessmentList;

    /**
     * 驻村干部人员信息
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcMemStation> vcMemStationList;

    /**
     * 驻村干部派驻申请、调整表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcMemStationApplyAdjust> vcMemStationApplyAdjustList;

    /**
     * 驻村干部派驻申请、调整人员明细
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcMemStationApplyAdjustDetails> vcMemStationApplyAdjustDetailsList;

    /**
     * 乡镇党委干部信息
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcMemTeamStation> vcMemTeamStationList;

    /**
     * 村党组织干部信息
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcMemVillageCadres> vcMemVillageCadresList;

    /**
     * 提拔使用
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcPromoteUse> vcPromoteUseList;

    /**
     * vc_station_org
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcStationOrg> vcStationOrgList;

    /**
     * 考核指标信息
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcTownshipAssessment> vcTownshipAssessmentList;

    /**
     * 乡（镇）情简介-集体资产情况
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcTownshipAsset> vcTownshipAssetList;

    /**
     * 集体经济组织表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcTownshipCollectiveEconomy> vcTownshipCollectiveEconomyList;

    /**
     * 教育工作
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcTownshipEducation> vcTownshipEducationList;

    /**
     * “组团式”帮扶 实体类
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcTownshipGroupHelp> vcTownshipGroupHelpList;

    /**
     * 乡（镇）情简介-所获荣誉
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcTownshipHonors> vcTownshipHonorsList;

    /**
     * 集体经济收支情况表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcTownshipIncomeExpenditure> vcTownshipIncomeExpenditureList;

    /**
     * 集体经济收入支出情况主表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcTownshipIncomeExpenditureA> vcTownshipIncomeExpenditureAList;

    /**
     * 集体经济收入支出情况明细表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcTownshipIncomeExpenditureB> vcTownshipIncomeExpenditureBList;

    /**
     *
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcTownshipIncomeMem> vcTownshipIncomeMemList;

    /**
     * 乡（镇）情简介-主要经济指标
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcTownshipIndustry> vcTownshipIndustryList;

    /**
     * 乡（镇）机构设置信息
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcTownshipJgsz> vcTownshipJgszList;

    /**
     *
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcTownshipLand> vcTownshipLandList;

    /**
     * 乡（镇）情简介-地理位置
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcTownshipLocation> vcTownshipLocationList;

    /**
     * 医疗工作
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcTownshipMedicalWork> vcTownshipMedicalWorkList;

    /**
     * 乡（镇）情简介-人口情况
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcTownshipMem> vcTownshipMemList;

    /**
     * 村级后备力量
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcTownshipReserveForce> vcTownshipReserveForceList;

    /**
     * 乡镇编制基本情况
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcTownshipStaff> vcTownshipStaffList;

    /**
     * 到村任职选调生名册
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcTownshipStudentRoster> vcTownshipStudentRosterList;

    /**
     * 中央和省级财政扶持资金使用情况表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcTownshipSupportFund> vcTownshipSupportFundList;

    /**
     * vc_township_support_main
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcTownshipSupportMain> vcTownshipSupportMainList;

    /**
     * 科技人才
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcTownshipTechnologyTalents> vcTownshipTechnologyTalentsList;

    /**
     * 科技工作
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcTownshipTechnologyWork> vcTownshipTechnologyWorkList;

    /**
     * 乡镇编制情况-抽调人员情况
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcTownshipTransferMem> vcTownshipTransferMemList;

    /**
     * 驻村干部人员信息
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcTownshipType> vcTownshipTypeList;

    /**
     * 驻村干部报酬
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VillageCadres> villageCadresList;

    /**
     * 驻村干部报酬
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VillageCadresBp> villageCadresBpList;

    /**
     * 驻村干部请销假数据
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<com.zenith.front.entity.model.st.VcPleasDestroyHoliday> vcPleasDestroyHolidayList;

    /**
     * 流动党组织
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgFlow2> orgFlow2List;

    /**
     * 流动党组织审批表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<OrgFlowAudit2> orgFlowAudit2List;

    /**
     * 流动党组织审批表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<MemFlowSignAudit> memFlowSignAuditList;

    /**
     * 流动党员登记表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<MemFlowSign2> memFlowSign2List;

    /**
     * 流动党员督察表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<MemFlowInspectionForm> memFlowInspectionList;
    /**
     * 流程表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<MemDevelopProcess> memDevelopProcessList;
    /**
     * 档案表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<MemDigital> memDigitalList;
    /**
     * 档案操作记录表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<MemDigitalOperationLog> memDigitalOperationLogList;
    /**
     * 档案统计表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<MemDigitalCount> memDigitalCountList;

    /**
     * 档案审核表
     */
    @JacksonXmlElementWrapper(useWrapping = false)
    private List<MemDigitalAudit> memDigitalAuditList;

//    /**
//     * 已纳入流入地管理活动信息
//     */
//    @JacksonXmlElementWrapper(useWrapping = false)
//    private List<MemFlowEvents2> memFlowEvents2List;

//    /**
//     * 流动党员消息表
//     */
//    @JacksonXmlElementWrapper(useWrapping = false)
//    private List<MemFlowMessage2> memFlowMessage2List;


//    public static void main(String[] args) {
//        Reflections f = new Reflections("com.zenith.front.entity.model.st");
//        Set<Class<?>> clazz =  f.getTypesAnnotatedWith(TableName.class );
//        StringBuilder sb = new StringBuilder();
//        final String jack = "@JacksonXmlElementWrapper(useWrapping = false)\n";
//        for (Class<?> aClass : clazz) {
//            ApiModel apiModel = aClass.getAnnotation(ApiModel.class);
//            sb.append("/**\n* ");
//            if(Objects.nonNull(apiModel)){
//                sb.append(apiModel.value());
//            }
//            sb.append("\n*/\n");
//            sb.append(jack)
//                    .append(StrUtil.format("private List<{}> {}List;\n\n",
//                            aClass.getName(), StrUtil.lowerFirst(aClass.getSimpleName())
//                    ));
//        }
//        System.out.println(sb.toString());
//    }

}
