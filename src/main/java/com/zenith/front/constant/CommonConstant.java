package com.zenith.front.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 通用常量类
 * @date 2019/3/20 17:54
 */
public class CommonConstant {
    /**
     * 负一,一般是树形结构的顶级节点的父id
     */
    public static final String MINUS_ZERO = "-1";
    /**
     * 零
     */
    public static final String ZERO = "0";
    /**
     * 0
     */
    public static final int ZERO_INT = 0;
    /**
     * 一
     */
    public static final String ONE = "1";
    /**
     * 1
     */
    public static final int ONE_INT = 1;
    /**
     * 二
     */
    public static final String TWO = "2";
    /**
     * 2
     */
    public static final int TWO_INT = 2;
    /**
     * 三
     */
    public static final String THREE = "3";
    /**
     * 3
     */
    public static final int THREE_INT = 3;

    /**
     * 四
     */
    public static final String FOUR = "4";

    /**
     * 4
     */
    public static final int FOUR_INT = 4;
    /**
     * 五
     */
    public static final String FIVE = "5";

    /**
     * 5
     */
    public static final int FIVE_INT = 5;
    /**
     * 六
     */
    public static final String SIX = "6";

    /**
     * 6
     */
    public static final int SIX_INT = 6;

    /**
     * 七
     */
    public static final String SEVEN = "7";

    /**
     * 7
     */
    public static final int SEVEN_INT = 7;
    /**
     * 8
     */
    public static final String EIGHT = "8";

    /**
     * 8
     */
    public static final int EIGHT_INT = 8;

    /**
     * 9
     */
    public static final String NINE = "9";

    /**
     * 9
     */
    public static final int NINE_INT = 9;

    /**
     * 10
     */
    public static final String TEN = "10";

    /**
     * 10
     */
    public static final int TEN_INT = 10;

    /**
     * 11
     */
    public static final String ELEVEN = "11";

    /**
     * 11
     */
    public static final int ELEVEN_INT = 11;
    /**
     * 12
     */
    public static final String TWELVE = "12";

    /**
     * 12
     */
    public static final int TWELVE_INT = 12;
    /**
     * 13
     */
    public static final String THIRTEEN = "13";

    /**
     * 13
     */
    public static final int THIRTEEN_INT = 13;
    /**
     * 14
     */
    public static final String FOURTEEN = "14";

    /**
     * 14
     */
    public static final int FOURTEEN_INT = 14;
    /**
     * 16
     */
    public static final String SIXTEEN = "16";

    /**
     * 16
     */
    public static final int SIXTEEN_INT = 16;
    /**
     * 20
     */
    public static final String TWENTY = "20";

    /**
     * 20
     */
    public static final int TWENTY_INT = 20;
    /**
     * 30
     */
    public static final String THIRTY = "30";

    /**
     * 30
     */
    public static final int THIRTY_INT = 30;
    /**
     * 40
     */
    public static final String FOURTY = "40";

    /**
     * 40
     */
    public static final int FOURTY_INT = 40;
    /**
     * 40
     */
    public static final String FIVETY = "50";

    /**
     * 40
     */
    public static final int FIVETY_INT = 50;
    /**
     * 70
     */
    public static final String SEVENTY = "70";

    /**
     * 40
     */
    public static final int SEVENTY_INT = 70;
    /**
     * 21
     */
    public static final String TWENTY_ONE = "21";

    /**
     * 21
     */
    public static final int TWENTY_ONE_INT = 21;

    /**
     * 33
     */
    public static final int THIRTY_THREE_INT = 33;

    /**
     * 9999
     */
    public static final int NINETY_NINETY_NINETY_NINETY = 9999;

    /**
     * 100
     */
    public static final String HUNDRED = "100";
    public static final Integer HUNDRED_INI = 100;
    /**
     * get方法
     */
    public static final String GET_STR = "get";
    /**
     * set方法
     */
    public static final String SET_STR = "set";
    /**
     * find方法
     */
    public static final String FIND_STR = "find";
    /**
     * save方法
     */
    public static final String SAVE_STR = "save";
    /**
     * add方法
     */
    public static final String ADD_STR = "add";
    /**
     * update方法
     */
    public static final String UPDATE_STR = "update";
    /**
     * del方法
     */
    public static final String DEL_STR = "del";
    /**
     * outMessage
     */
    public static final String OUT_MESSAGE = "outMessage";
    /**
     * condition
     */
    public static final String CONDITION = "condition";

    /**
     * 是
     */
    public static final String TRUE_STRING = "是";
    /**
     * 否
     */
    public static final String FALSE_STRING = "否";
    /**
     * OK  支付成功
     */
    public static final String OK = "OK";
    /**
     * FAIL  支付失败
     */
    public static final String FAIL = "FAIL";
    /**
     * SUCCESS  支付成功
     */
    public static final String SUCCESS = "SUCCESS";
    /**
     * REFUND  转入退款
     */
    public static final String REFUND = "REFUND";
    /**
     * NOTPAY  未支付
     */
    public static final String NOTPAY = "NOTPAY";
    /**
     * CLOSED  已关闭
     */
    public static final String CLOSED = "CLOSED";
    /**
     * REVOKED  已撤销（刷卡支付）
     */
    public static final String REVOKED = "REVOKED";
    /**
     * USERPAYING  用户支付中
     */
    public static final String USERPAYING = "USERPAYING";
    /**
     * PAYERROR  支付失败(其他原因，如银行返回失败)
     */
    public static final String PAYERROR = "PAYERROR";
    /**
     * 党内职务 书记
     */
    public static final String DICT_D22_SJ = "'11','21','31','41','51'";
    /**
     * 党内职务 副书记
     */
    public static final String DICT_D22_FSJ = "'12','22','32','42','52'";
    /**
     * 党内职务 委员
     */
    public static final String DICT_D22_WY = "'13','34','43','53'";

    public static final String BASE_STR = "ABCDEFGHJKMNOPQRSTUVWXYZabcdefghjkmnopqrstuvwxyz";

    public static final String RANDOM_CODE_KEY = "RANDOM_CODE_KEY";

    public static final String CLASSPATH = "classpath:";

    public static final String XZQH_GUIZHOU = "052000000001";

    public static final String ZIP = ".zip";

    public static final String TMP = "tmp";

    public static final char UNIX_SEPARATOR = '/';

    /**
     * 运维平台用户
     */
    public static final String PLATFORM_USER = "platformUser:";

    public static final String ZERO_TWO = "02";

    public static final String ZERO_ONE = "01";

    /**
     * 升序
     */
    public static final String ASCEND = "ascend";

    /**
     * 降序
     */
    public static final String DESCEND = "descend";

    /**
     * 英文逗号
     */
    public static final String COMMA_ENGLISH = ",";
    /**
     * 数据未上传交换区
     */
    public static final String SUCCESS_UPLOAD_EXCHANGE = "{\"msg\":\"数据上传成功\",\"code\":\"01\"}";
    public static final String NOT_UPLOAD_EXCHANGE = "{\"msg\":\"交换区数据交互未开启！\",\"code\":\"9999\"}";
    public static final String NOT_NEED_UPLOAD_EXCHANGE = "{\"msg\":\"自定义状态：数据本地已撤销无需上传交换区！\",\"code\":\"9998\"}";
    /**
     * 接收数据结构问题进行退回操作标识code
     */
    public static final String DOWNLOAD_EXCHANGE_ERROR_CODE = "9999";
    /**
     * 上传流动党员工作督查操作标识code
     */
    public static final String UPLOAD_EXCHANGE_STATICS_CODE = "6666";
}
