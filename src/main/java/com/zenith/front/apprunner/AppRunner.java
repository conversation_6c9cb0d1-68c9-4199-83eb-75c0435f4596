package com.zenith.front.appRunner;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.constant.CommonConstant;
import com.zenith.front.entity.model.*;
import com.zenith.front.mapper.*;
import com.zenith.front.service.relationshiptransfer.ITransferLetterService;
import com.zenith.front.service.transfer.TransferService;
import com.zenith.front.service.transfercontent.ITransferContentService;
import com.zenith.front.untils.FMSym;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;

/**
 * @author: D.watermelon
 * @date: 2022/5/5 10:40
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 */
@Component
@Slf4j
public class AppRunner implements ApplicationRunner {

    @Autowired
    private TransferService transferService;

    @Resource
    private ITransferContentService iTransferContentService;

    @Autowired
    private ITransferLetterService letterService;
    @Resource
    private CallBackMapper callBackMapper;
    @Resource
    private TransFerMapper transFerMapper;
    @Autowired
    private OrgExchangeAreaMapper orgMapper;
    @Resource
    private TransferUploadMapper transferUploadMapper;



    @Override
    public void run(ApplicationArguments args) {
        System.out.println("执行启动修复");
        FMSym.initFmSym();
        // 修复上传办理过程顺序号数据错误问题
//        repairTransferUploadDataBLGC0011();
//        transferService.createOutProcess("0362000665842022042481915");
//        transferService.createOutProcess("0430002193442022042115032");
//        TransferContent transferContent = iTransferContentService.getBaseMapper().selectOne(new QueryWrapper<TransferContent>().lambda().eq(TransferContent::getId, "45f4b41022ce44ff89d67c750c84a978"));
//        String content = transferContent.getContent();
//        JSONArray jsonArray = JSONArray.parseArray(content);
//        transferService.createProcess(jsonArray);
//
//        System.out.println("第一个修复完成----mmmm");
//        TransferContent transferContent2 = iTransferContentService.getBaseMapper().selectOne(new QueryWrapper<TransferContent>().lambda().eq(TransferContent::getId, "b5685aec8af348d9af5fa039d6a57449"));
//        String content1 = transferContent2.getContent();
//        JSONArray jsonArray1 = JSONArray.parseArray(content1);
//        transferService.createProcess(jsonArray1);

//        //从contenct 初始化到area数据修复
//        List<TransferContent> transferContents = iTransferContentService.getBaseMapper().selectList(new QueryWrapper<TransferContent>().lambda().isNull(TransferContent::getLetterNumber));
//        transferContents.forEach(transferContent -> {
//           String content1 = transferContent.getContent();
//           JSONArray jsonArray1 = JSONArray.parseArray(content1);
//           transferService.createProcess(jsonArray1);
//           System.out.println("处理数据===>"+transferContent.getId());
//        });
//
//        //修复红海林掉得数据
//        List<TransferContent> transferContents = iTransferContentService.getBaseMapper().selectList(new QueryWrapper<>());
//        for (TransferContent transferContent : transferContents) {
//            cn.hutool.json.JSONArray objects = JSONUtil.parseArray(transferContent.getContent());
//            letterService.processLetter(objects);
//        }


          //修复打包错误到测试交换区得数据
//        List<TransFerExchangeArea> transFerExchangeAreas = transferService.getBaseMapper().selectList(new QueryWrapper<TransFerExchangeArea>().isNotNull("letter_message_code")
//                .ne("letter_message_code", "").ne("letter_message_code", "0"));
//        System.out.println(transFerExchangeAreas.size());
////        TransFerExchangeArea transFerExchangeArea1 = transFerExchangeAreas.get(0);
////        List<TransFerExchangeArea> aa= new ArrayList<>();
////        aa.add(transFerExchangeArea1);
//        transFerExchangeAreas.forEach(transFerExchangeArea -> {
//            String trasFerId = transFerExchangeArea.getId();
//            Integer type = transFerExchangeArea.getType();
//            System.out.println("修复得id=="+trasFerId);
//            if (224==type){
//                JSONObject jsonData = transferService.transferOutNational(transFerExchangeArea);
//                if (ObjectUtil.isNotNull(jsonData)){
//                    System.out.println("触发了全国交换区");
//                    OutMessage outMessage = null;
//                    try {
//                        outMessage = letterService.uploadESB(jsonData);
//                    } catch (Exception e) {
//                        e.printStackTrace();
//                    }
//                    System.out.println("海林中间交换区反馈数据： "+outMessage.toString());
//                    boolean b = transferService.updateThransferState(outMessage, trasFerId, false);
//                    System.out.println("数据库更新情况====》"+b);
//                }
//            }
//        });
//        System.out.println("修复完成");


        ///修复数据删除回退(类别 124)
        //SELECT id,transfer_id,src_org_id,src_org_name,target_org_id,target_org_name,letter_number,create_time,status FROM transfer_exchange_area
        // WHERE "type" =124 and (letter_number is NULL or letter_number ='')  order by create_time limit 1
//        LambdaQueryWrapper<TransFerExchangeArea> lambdaQueryWrapper = new LambdaQueryWrapper<TransFerExchangeArea>()
//                .eq(TransFerExchangeArea::getType, 124).and(queryWrapper -> queryWrapper
//                .isNull(TransFerExchangeArea::getLetterNumber)
//                .or()
//                .eq(TransFerExchangeArea::getLetterNumber, ""));
//        List<TransFerExchangeArea> transFerExchangeAreas = transferService.getBaseMapper().selectList(lambdaQueryWrapper);
//        System.out.println("需要修复的数据"+ transFerExchangeAreas.size());
//        transFerExchangeAreas.forEach(transFerExchangeArea -> {
//            String targetOrgId = transFerExchangeArea.getTargetOrgId();
//            String srcOrgId = transFerExchangeArea.getSrcOrgId();
//
//            Integer status = transFerExchangeArea.getStatus();
//            if (status==0){
//                List<TransferLetter> transferLetters = letterService.getBaseMapper().selectList(new QueryWrapper<TransferLetter>().eq("jsx0011", srcOrgId).eq("jsx0015", targetOrgId));
//                if (transferLetters.size()==1){
//                    TransFerExchangeArea updateExec = new TransFerExchangeArea();
//                    //更新数据库
//                    updateExec.setId(transFerExchangeArea.getId());
//                    updateExec.setLetterNumber(transferLetters.get(0).getLetterNumber());
//                    transferService.getBaseMapper().updateById(updateExec);
//                    System.out.println("修复数据=====>"+transFerExchangeArea.getId());
//
//                }else {
//                    System.out.println("出现未修复数据情况==>"+transFerExchangeArea.getId());
//
//                }            }
//            if (status==1){
//                List<TransferLetter> transferLetters = letterService.getBaseMapper().selectList(new QueryWrapper<TransferLetter>().eq("jsx0011", srcOrgId));
//                if (transferLetters.size()==1){
//                    TransFerExchangeArea updateExec = new TransFerExchangeArea();
//                    //更新数据库
//                    updateExec.setId(transFerExchangeArea.getId());
//                    updateExec.setLetterNumber(transferLetters.get(0).getLetterNumber());
//                    transferService.getBaseMapper().updateById(updateExec);
//                    System.out.println("修复数据=====>"+transFerExchangeArea.getId());
//                }else {
//                    System.out.println("出现未修复数据情况==>"+transFerExchangeArea.getId());
//
//                }
//            }
//            if (status==2) {
//                List<TransferLetter> transferLetters = letterService.getBaseMapper().selectList(new QueryWrapper<TransferLetter>().eq("jsx0011", srcOrgId).eq("jsx0015", targetOrgId));
//                if (transferLetters.size() == 1) {
//                    TransFerExchangeArea updateExec = new TransFerExchangeArea();
//                    //更新数据库
//                    updateExec.setId(transFerExchangeArea.getId());
//                    updateExec.setLetterNumber(transferLetters.get(0).getLetterNumber());
//                    transferService.getBaseMapper().updateById(updateExec);
//                    System.out.println("修复数据=====>"+transFerExchangeArea.getId());
//                } else {
//                    System.out.println("出现未修复数据情况==>" + transFerExchangeArea.getId());
//
//                }
//            }
//        });



        //删除数据回退类别（224）进行中的数据
//        LambdaQueryWrapper<TransFerExchangeArea> lambdaQueryWrapper = new LambdaQueryWrapper<TransFerExchangeArea>()
//                .eq(TransFerExchangeArea::getType, 224).eq(TransFerExchangeArea::getStatus,0).and(queryWrapper -> queryWrapper
//                        .isNull(TransFerExchangeArea::getLetterNumber)
//                        .or()
//                        .eq(TransFerExchangeArea::getLetterNumber, ""));
//        List<TransFerExchangeArea> transFerExchangeAreas = transferService.getBaseMapper().selectList(lambdaQueryWrapper);
//        System.out.println("根据接收党组织修复的数据"+ transFerExchangeAreas.size());
//        transFerExchangeAreas.forEach(transFerExchangeArea -> {
//            String targetOrgId = transFerExchangeArea.getTargetOrgId();
//            String srcOrgId = transFerExchangeArea.getSrcOrgId();
//            List<TransferLetter> transferLetters = letterService.getBaseMapper().selectList(new QueryWrapper<TransferLetter>().eq("jsx0015", targetOrgId));
//            transferLetters.forEach(transferLetter -> {
//                //人员姓名
//                String jsx0004 = transferLetter.getJsx0004();
//                //人员身份证号
//                String jsx0008 = transferLetter.getJsx0008();
//                String data = transFerExchangeArea.getData();
//                JSONObject jsonObject = JSONObject.parseObject(data);
//                JSONObject extraData = jsonObject.getJSONObject("extraData");
//                String idcard = extraData.getString("idcard");
//                String name = extraData.getString("name");
//                if (jsx0004.equals(name)&&jsx0008.equals(idcard)){
//                    System.out.println("出现匹配数据===>getTransferId: "+transFerExchangeArea.getTransferId()+"  getLetterNumber: "+transferLetter.getLetterNumber());
//                    TransFerExchangeArea updateExec = new TransFerExchangeArea();
//                    //更新数据库
//                    updateExec.setId(transFerExchangeArea.getId());
//                    updateExec.setLetterNumber(transferLetter.getLetterNumber());
//                    int i = transferService.getBaseMapper().updateById(updateExec);
//                    System.out.println("修复数据=====>"+transFerExchangeArea.getId()+"   "+i);
//                }
//            });
//
//
//        });



//        System.out.println("根据发起党组织修复的数据"+ transFerExchangeAreas.size());
//        transFerExchangeAreas.forEach(transFerExchangeArea -> {
//            String targetOrgId = transFerExchangeArea.getTargetOrgId();
//            String srcOrgId = transFerExchangeArea.getSrcOrgId();
//            //根据发起组织匹配
//            List<TransferLetter> transferLetters = letterService.getBaseMapper().selectList(new QueryWrapper<TransferLetter>().eq("jsx0011", srcOrgId));
//            transferLetters.forEach(transferLetter -> {
//                //人员姓名
//                String jsx0004 = transferLetter.getJsx0004();
//                //人员身份证号
//                String jsx0008 = transferLetter.getJsx0008();
//                String data = transFerExchangeArea.getData();
//                JSONObject jsonObject = JSONObject.parseObject(data);
//                JSONObject extraData = jsonObject.getJSONObject("extraData");
//                String idcard = extraData.getString("idcard");
//                String name = extraData.getString("name");
//                System.out.println();
//                if (jsx0004.equals(name)&&jsx0008.equals(idcard)){
//                    System.out.println("出现匹配数据===>getTransferId: "+transFerExchangeArea.getTransferId()+"  getLetterNumber: "+transferLetter.getLetterNumber());
//                    TransFerExchangeArea updateExec = new TransFerExchangeArea();
//                    //更新数据库
//                    updateExec.setId(transFerExchangeArea.getId());
//                    updateExec.setLetterNumber(transferLetter.getLetterNumber());
//                    int i = transferService.getBaseMapper().updateById(updateExec);
//                    System.out.println("修复数据=====>"+transFerExchangeArea.getId()+"   "+i);
//                }
//            });
//        });



//        LambdaQueryWrapper<TransFerExchangeArea> lambdaQueryWrapper = new LambdaQueryWrapper<TransFerExchangeArea>()
//                .eq(TransFerExchangeArea::getType, 224).eq(TransFerExchangeArea::getStatus,2).and(queryWrapper -> queryWrapper
//                        .isNull(TransFerExchangeArea::getLetterNumber)
//                        .or()
//                        .eq(TransFerExchangeArea::getLetterNumber, ""));
//        List<TransFerExchangeArea> transFerExchangeAreas = transferService.getBaseMapper().selectList(lambdaQueryWrapper);
//        System.out.println("需要修复的数据"+transFerExchangeAreas.size());
//        transFerExchangeAreas.forEach(transFerExchangeArea -> {
//            String targetOrgId = transFerExchangeArea.getTargetOrgId();
//            String srcOrgId = transFerExchangeArea.getSrcOrgId();
//            //根据发起组织匹配
//            List<TransferLetter> transferLetters = letterService.getBaseMapper().selectList(new QueryWrapper<TransferLetter>().eq("jsx0015", targetOrgId));
//            transferLetters.forEach(transferLetter -> {
//                //人员姓名
//                String jsx0004 = transferLetter.getJsx0004();
//                //人员身份证号
//                String jsx0008 = transferLetter.getJsx0008();
//                String data = transFerExchangeArea.getData();
//                JSONObject jsonObject = JSONObject.parseObject(data);
//                JSONObject extraData = jsonObject.getJSONObject("extraData");
//                String idcard = extraData.getString("idcard");
//                String name = extraData.getString("name");
//                System.out.println();
//                if (jsx0004.equals(name)&&jsx0008.equals(idcard)){
//                    System.out.println("出现匹配数据===>getTransferId: "+transFerExchangeArea.getTransferId()+"  getLetterNumber: "+transferLetter.getLetterNumber());
//                    TransFerExchangeArea updateExec = new TransFerExchangeArea();
//                    //更新数据库
//                    updateExec.setId(transFerExchangeArea.getId());
//                    updateExec.setLetterNumber(transferLetter.getLetterNumber());
//                    int i = transferService.getBaseMapper().updateById(updateExec);
//                    System.out.println("修复数据=====>"+transFerExchangeArea.getId()+"   "+i);
//                }
//            });
//
//        });



//        LambdaQueryWrapper<TransFerExchangeArea> lambdaQueryWrapper = new LambdaQueryWrapper<TransFerExchangeArea>()
//                .in(TransFerExchangeArea::getType, 224,124).and(queryWrapper -> queryWrapper
//                        .isNull(TransFerExchangeArea::getLetterNumber)
//                        .or()
//                        .eq(TransFerExchangeArea::getLetterNumber, ""));
//        List<TransFerExchangeArea> transFerExchangeAreas = transferService.getBaseMapper().selectList(lambdaQueryWrapper);
//        System.out.println("需要修复的数据"+transFerExchangeAreas.size());
//        transFerExchangeAreas.forEach(transFerExchangeArea -> {
//                String data = transFerExchangeArea.getData();
//                JSONObject jsonObject = JSONObject.parseObject(data);
//                JSONObject extraData = jsonObject.getJSONObject("extraData");
//                String idcard = extraData.getString("idcard");
//                String name = extraData.getString("name");
//                List<TransferLetter> transferLetters = letterService.getBaseMapper().selectList(new QueryWrapper<TransferLetter>().eq("jsx0004", name).eq("jsx0008",idcard));
//                transferLetters.forEach(transferLetter -> {
//                    String letterNumber = transferLetter.getLetterNumber();
//                    LambdaQueryWrapper<TransFerExchangeArea> hasTransfer = new LambdaQueryWrapper<TransFerExchangeArea>().eq(TransFerExchangeArea::getLetterNumber,letterNumber);
//                    List<TransFerExchangeArea> hasTransList = transferService.getBaseMapper().selectList(hasTransfer);
//                    System.out.println("查询的数据存在的数据"+hasTransList.size());
//                    if (hasTransList.size()==0){
//                      System.out.println("出现匹配数据===>getTransferId: "+transFerExchangeArea.getTransferId()+"  getLetterNumber: "+transferLetter.getLetterNumber());
//                    TransFerExchangeArea updateExec = new TransFerExchangeArea();
//                    //更新数据库
//                    updateExec.setId(transFerExchangeArea.getId());
//                    updateExec.setLetterNumber(transferLetter.getLetterNumber());
//                    //int i = transferService.getBaseMapper().updateById(updateExec);
//                    //System.out.println("修复数据=====>"+transFerExchangeArea.getId()+"   "+i);
//                    }
//                });
//        });



//        System.out.println("开始尝试请求单个拉取列表");
//        ESBUploadAnQuerybusinessServiceImplService service = new ESBUploadAnQuerybusinessServiceImplService();
//        ESBUploadAnQuerybusinessServiceImpl serviceImplPort = service.getESBUploadAnQuerybusinessServiceImplPort();
//        //生成上传交换区的数据
//        JSONObject postJsonData=new JSONObject();
//        JSONArray dataJsonArray=new JSONArray();
//        //dataJsonArray.add("0420001420062022070844277");
//        dataJsonArray.add("0512000644282022062110761");
//        postJsonData.put("dataType", CommonConstant.FIVE);
//        postJsonData.put("accessID","052000000001");
//        postJsonData.put("uniqueKey",dataJsonArray);
//        Map<String, String> stringMap = CryptoUtil.signData(postJsonData.toString(), "1");
//        String signData = stringMap.get("signData");
//        String data = stringMap.get("data");
//        String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
//        String unicode = cnToUnicode(qianMingData);
//        String resultStr = serviceImplPort.query(unicode);
//        String responseData = new String(Base64Utils.decode(resultStr.getBytes()), "UTF-8");
//        System.out.println("请求反馈情况+ "+responseData);
//        JSXVO jsxvo = JackSonUtil.toObject(responseData, JSXVO.class);
//        String realInfo = CryptoUtil.getRealInfo(jsxvo.getInfo(), jsxvo.getSecretKey());
//        System.out.println("反馈的数据+  ："+realInfo);
//        OutMessage onUniqueCode = transferService.findOnUniqueCode("0512000644282022062110761");
//        System.out.println(onUniqueCode);


        //获取处理所有已经转接完成的关系转接
//        ESBUploadAnQuerybusinessServiceImplService service = new ESBUploadAnQuerybusinessServiceImplService();
//        ESBUploadAnQuerybusinessServiceImpl serviceImplPort = service.getESBUploadAnQuerybusinessServiceImplPort();
//        List<TransFerExchangeArea> alltransferData = transferService.findAlltransferData(null);
//        System.out.println("获取总数量===>"+alltransferData.size());
//        for (TransFerExchangeArea alltransferDatum : alltransferData) {
//            Integer status = alltransferDatum.getStatus();
//            String letterNumber = alltransferDatum.getLetterNumber();
//            List<CallBack> letter_number = callBackMapper.selectList(new QueryWrapper<CallBack>().eq("letter_number", letterNumber));
//            if (letter_number.size()>0)continue;
//            JSONObject postJsonData=new JSONObject();
//            JSONArray dataJsonArray=new JSONArray();
//            dataJsonArray.add(letterNumber);
//            postJsonData.put("dataType", CommonConstant.FIVE);
//            postJsonData.put("accessID","052000000001");
//            postJsonData.put("uniqueKey",dataJsonArray);
//            Map<String, String> stringMap = null;
//            try {
//                stringMap = CryptoUtil.signData(postJsonData.toString(), "1");
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//            String signData = stringMap.get("signData");
//            String data = stringMap.get("data");
//            String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
//            String unicode = cnToUnicode(qianMingData);
//            String resultStr =null;
//            try {
//             resultStr = serviceImplPort.query(unicode);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//            if (StrUtil.isEmpty(resultStr))continue;
//            String responseData = null;
//            try {
//                responseData = new String(Base64Utils.decode(resultStr.getBytes()), "UTF-8");
//            } catch (UnsupportedEncodingException e) {
//                e.printStackTrace();
//            }
//            JSXVO jsxvo = JackSonUtil.toObject(responseData, JSXVO.class);
//            if (jsxvo.getSecretKey()==null){
//                CallBack callBack = new CallBack();
//                callBack.setId(StrKit.getRandomUUID());
//                callBack.setLetterNumber(letterNumber);
//                callBack.setStatus(String.valueOf(status));
//                callBack.setData(jsxvo.getMsg());
//                callBackMapper.insert(callBack);
//                continue;
//            }
//            String realInfo = null;
//            try {
//                realInfo = CryptoUtil.getRealInfo(jsxvo.getInfo(), jsxvo.getSecretKey());
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//            JSONArray jsonArray = JSONArray.parseArray(realInfo);
//            //保存数据入库
//            CallBack callBack = new CallBack();
//            callBack.setId(StrKit.getRandomUUID());
//            callBack.setLetterNumber(letterNumber);
//            callBack.setStatus(String.valueOf(status));
//            callBack.setData(jsonArray.toJSONString());
//            callBackMapper.insert(callBack);
//            try {
//                Thread.sleep(500);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//            System.out.println(letterNumber+"   letternumber");
//
//        }

        //关系转接修复标记数据
        //this.repairTransferData();

        //修复关系转接完成情况
        //this.localIsFinish();

        //处理修复中组部兼容小写c01对象
        //this.deailData();
    }

    /**
     * 修复关系转接完成的，不一致的情况
     * **/
    public void  localIsFinish(){
        //处理转出省外数据情况
        //处理省外转入中的数据，本地完成是完成状态下，但是交换区状态是审核中的
        //SELECT * FROM call_back where   status ='1' AND check_mark <>'5' AND check_mark <>'4'  and check_mark='0' and letter_number not like '05200%'
        List<CallBack> notLikeFinish = callBackMapper.selectList(new QueryWrapper<CallBack>().eq("status", CommonConstant.ONE)
                .ne("check_mark", CommonConstant.FIVE).ne("check_mark", CommonConstant.FOUR)
                .eq("check_mark", CommonConstant.ZERO).notLike("letter_number", "05200%"));
        notLikeFinish.forEach(callBack -> {
            String letterNumber = callBack.getLetterNumber();
            TransferProcess transferProcess = this.endTransferProcess(callBack);
            Integer number = transferProcess.getBlgc0011();
            this.deailBLGCjson(callBack,letterNumber,number,this.endApproval(letterNumber),"5",null,"党员报道");
        });

        //处理省外转入，本地完成是完成状态下，交换区状态是撤销的
        //SELECT * FROM call_back where   status ='1' AND check_mark <>'5' AND check_mark <>'4'  and check_mark='7' and letter_number not like '05200%'
        List<CallBack> notLikeRevoke = callBackMapper.selectList(new QueryWrapper<CallBack>().eq("status", CommonConstant.ONE)
                .ne("check_mark", CommonConstant.FIVE).ne("check_mark", CommonConstant.FOUR)
                .eq("check_mark", CommonConstant.SEVEN).notLike("letter_number", "05200%"));
        notLikeRevoke.forEach(callBack -> {

        });


        //处理本地是撤销得，但是交换区状态不是撤销得
        List<CallBack> notLikeRevokeCheck = callBackMapper.selectList(new QueryWrapper<CallBack>().eq("status", CommonConstant.TWO)
                .ne("check_mark", CommonConstant.SEVEN));
        notLikeRevokeCheck.forEach(callBack -> {
            String letterNumber = callBack.getLetterNumber();
            TransferProcess transferProcess = this.endTransferProcess(callBack);
            String blgc0006 = transferProcess.getBlgc0006();
            Integer blgc0011 = transferProcess.getBlgc0011();
            //我发起的，但是交换区状态不是撤销的
            if (letterNumber.startsWith("052000")&&StrUtil.equalsAny(blgc0006,"0","1","4","5","9")){
                //进行中得要上传撤消
                if (blgc0006.equals("0")){
                    this.deailBLGCjson(callBack,letterNumber,blgc0011,this.endApproval(letterNumber),"7","1","接收党组织填写错误");
                }
                //已经拒绝的，要上传撤销
                if (blgc0006.equals("1")){
                    this.deailBLGCjson(callBack,letterNumber,blgc0011,this.endApproval(letterNumber),"7","2","接收党组织不同意接收");
                }
                //对方已经接受，和党员报道的，我这里本地已经撤销的数据留存暂时不做处理
                if (blgc0006.equals("4")||blgc0006.equals("5")){

                }
                //交换区已经过期的，我这里本地要进行撤销
                if (blgc0006.equals("9")){

                }
            }
            //不是我发起得
            if (!letterNumber.startsWith("052000")&&StrUtil.equalsAny(blgc0006,"0","1","9","12")){
                System.out.println("letterNumber======》"+letterNumber);
                //交换区进行中的， 推送拒绝
                if (blgc0006.equals("0")){
                    this.deailBLGCjson(callBack,letterNumber,blgc0011,this.endApproval(letterNumber),"1","2","接收党组织不同意接收");
                }
                //交换区已经拒绝的，不需要处理
                if (blgc0006.equals("1")){

                }
                //交换区已经超时的，不需要处理
                if (blgc0006.equals("9")){

                }
                //交换区已经退回上一步的，不需要处理
                if (blgc0006.equals("12")){

                }
            }
        });

        //处理本地是进行中， 但是和交换区不是一致的问题
        List<CallBack> notConduct = callBackMapper.selectList(new QueryWrapper<CallBack>().eq("status", CommonConstant.ZERO)
                .ne("check_mark", CommonConstant.SEVEN));
        notConduct.forEach(callBack -> {
            String letterNumber = callBack.getLetterNumber();
            TransferProcess transferProcess = this.endTransferProcess(callBack);
            String blgc0006 = transferProcess.getBlgc0006();
            Integer blgc0011 = transferProcess.getBlgc0011();
            String check = callBack.getCheck();

            //是我发起得
            if (letterNumber.startsWith("05200")){
                //我发起得，被退回得，需要推送撤销
                if (check.equals("1")){
                    this.deailBLGCjson(callBack,letterNumber,blgc0011,this.endApproval(letterNumber),"7","2","接收党组织不同意接收");
                }
                //我发起，对面是已经接收或者党员报道得,本地需要处理为完成状态，但是不需要推送交换区
                if (StrUtil.equalsAny(check,"4","5")){
                    TransFerExchangeArea transFerExchangeArea = transFerMapper.selectOne(new QueryWrapper<TransFerExchangeArea>().lambda().eq(TransFerExchangeArea::getLetterNumber, letterNumber));
                    transFerExchangeArea.setStatus(CommonConstant.ZERO_INT);
                    //处理json内部的数据为完成
                    String data = transFerExchangeArea.getData();
                    JSONObject transferObject = JSONObject.parseObject(data);
                    transferObject.put("status",CommonConstant.ONE_INT);
                    transFerExchangeArea.setData(transferObject.toJSONString());
                    callBack.setBackData("我发起，对面是已经接收或者党员报道得,本地需要处理为完成状态，但是不需要推送交换区");
                    transFerMapper.updateById(transFerExchangeArea);
                    callBackMapper.updateById(callBack);
                }
                //我发起得，交换区状态是撤销得,本地需要进行撤销，不需要推送到交换区
                if (check.equals("7")){
                    TransFerExchangeArea transFerExchangeArea = transFerMapper.selectOne(new QueryWrapper<TransFerExchangeArea>().lambda().eq(TransFerExchangeArea::getLetterNumber, letterNumber));
                    transFerExchangeArea.setStatus(CommonConstant.ZERO_INT);
                    //处理json内部的数据为完成
                    String data = transFerExchangeArea.getData();
                    JSONObject transferObject = JSONObject.parseObject(data);
                    transferObject.put("status",CommonConstant.TWO_INT);
                    transFerExchangeArea.setData(transferObject.toJSONString());
                    callBack.setBackData("我发起得，交换区状态是撤销得,本地需要进行撤销，不需要推送到交换区");
                    transFerMapper.updateById(transFerExchangeArea);
                    callBackMapper.updateById(callBack);
                }
            }
            //不是我发起得
            if (!letterNumber.startsWith("05200")){
                //非我发起，中间交换区拒绝状态，本地要撤销
                if (check.equals("1")){
                    TransFerExchangeArea transFerExchangeArea = transFerMapper.selectOne(new QueryWrapper<TransFerExchangeArea>().lambda().eq(TransFerExchangeArea::getLetterNumber, letterNumber));
                    transFerExchangeArea.setStatus(CommonConstant.ZERO_INT);
                    //处理json内部的数据为完成
                    String data = transFerExchangeArea.getData();
                    JSONObject transferObject = JSONObject.parseObject(data);
                    transferObject.put("status",CommonConstant.TWO_INT);
                    transFerExchangeArea.setData(transferObject.toJSONString());
                    callBack.setBackData("非我发起，中间交换区拒绝状态，本地要撤销");
                    transFerMapper.updateById(transFerExchangeArea);
                    callBackMapper.updateById(callBack);
                }
                //非我发起，中间交换区是4，或者5状态， 本地需要完成
                if (StrUtil.equalsAny(check,"4","5")){
                    TransFerExchangeArea transFerExchangeArea = transFerMapper.selectOne(new QueryWrapper<TransFerExchangeArea>().lambda().eq(TransFerExchangeArea::getLetterNumber, letterNumber));
                    transFerExchangeArea.setStatus(CommonConstant.ZERO_INT);
                    //处理json内部的数据为完成
                    String data = transFerExchangeArea.getData();
                    JSONObject transferObject = JSONObject.parseObject(data);
                    transferObject.put("status",CommonConstant.ONE_INT);
                    transFerExchangeArea.setData(transferObject.toJSONString());
                    callBack.setBackData("非我发起，中间交换区是4，或者5状态， 本地需要完成");
                    transFerMapper.updateById(transFerExchangeArea);
                    callBackMapper.updateById(callBack);
                }
                //非我发起，交换区是撤销状态，本地需要撤销
                if (check.equals("7")){
                    TransFerExchangeArea transFerExchangeArea = transFerMapper.selectOne(new QueryWrapper<TransFerExchangeArea>().lambda().eq(TransFerExchangeArea::getLetterNumber, letterNumber));
                    transFerExchangeArea.setStatus(CommonConstant.ZERO_INT);
                    //处理json内部的数据为完成
                    String data = transFerExchangeArea.getData();
                    JSONObject transferObject = JSONObject.parseObject(data);
                    transferObject.put("status",CommonConstant.TWO_INT);
                    transFerExchangeArea.setData(transferObject.toJSONString());
                    callBack.setBackData("非我发起，交换区是撤销状态，本地需要撤销");
                    transFerMapper.updateById(transFerExchangeArea);
                    callBackMapper.updateById(callBack);
                }
                //非我发起，交换区是超时状态，本地需要撤销
                if (check.equals("9")){
                    TransFerExchangeArea transFerExchangeArea = transFerMapper.selectOne(new QueryWrapper<TransFerExchangeArea>().lambda().eq(TransFerExchangeArea::getLetterNumber, letterNumber));
                    transFerExchangeArea.setStatus(CommonConstant.ZERO_INT);
                    //处理json内部的数据为完成
                    String data = transFerExchangeArea.getData();
                    JSONObject transferObject = JSONObject.parseObject(data);
                    transferObject.put("status",CommonConstant.TWO_INT);
                    transFerExchangeArea.setData(transferObject.toJSONString());
                    callBack.setBackData("非我发起，交换区是超时状态，本地需要撤销");
                    transFerMapper.updateById(transFerExchangeArea);
                    callBackMapper.updateById(callBack);
                }
                //非我发起，交换区是超时退回上一步状态，本地需要撤销
                if (check.equals("12")){
                    TransFerExchangeArea transFerExchangeArea = transFerMapper.selectOne(new QueryWrapper<TransFerExchangeArea>().lambda().eq(TransFerExchangeArea::getLetterNumber, letterNumber));
                    transFerExchangeArea.setStatus(CommonConstant.ZERO_INT);
                    //处理json内部的数据为完成
                    String data = transFerExchangeArea.getData();
                    JSONObject transferObject = JSONObject.parseObject(data);
                    transferObject.put("status",CommonConstant.TWO_INT);
                    transFerExchangeArea.setData(transferObject.toJSONString());
                    callBack.setBackData("非我发起，交换区是超时退回上一步状态，本地需要撤销");
                    transFerMapper.updateById(transFerExchangeArea);
                    callBackMapper.updateById(callBack);
                }
            }
        });
    }



    public TransferProcess endTransferProcess(CallBack callBack){
        String letterNumber = callBack.getLetterNumber();
        String callBackData = callBack.getData();
        JSONArray jsonArray = JSONArray.parseArray(callBackData);
        List<TransferProcess> transferProcessList =new ArrayList<>();
        jsonArray.forEach(jsonObject->{
            JSONObject dataObject= (JSONObject) jsonObject;
            JSONArray blgcJsonArray = dataObject.getJSONArray("BLGC");
            if (dataObject.getString("uniqueKey").equals(letterNumber)){
                blgcJsonArray.forEach(blgcJson->{
                    JSONObject blgcJsonObject= (JSONObject) blgcJson;
                    TransferProcess transferProcess = new TransferProcess();
                    transferProcess.setBlgc0001(blgcJsonObject.getString("BLGC0001"));
                    transferProcess.setBlgc0002(blgcJsonObject.getString("BLGC0002"));
                    transferProcess.setBlgc0003(blgcJsonObject.getString("BLGC0003"));
                    transferProcess.setBlgc0004(blgcJsonObject.getString("BLGC0004"));
                    transferProcess.setBlgc0005(blgcJsonObject.getString("BLGC0005"));
                    transferProcess.setBlgc0006(blgcJsonObject.getString("BLGC0006"));
                    transferProcess.setBlgc0007(blgcJsonObject.getString("BLGC0007"));
                    transferProcess.setBlgc0008(blgcJsonObject.getString("BLGC0008"));
                    transferProcess.setBlgc0009(blgcJsonObject.getString("BLGC0009"));
                    transferProcess.setBlgc0010(blgcJsonObject.getString("BLGC0010"));
                    transferProcess.setBlgc0011(blgcJsonObject.getInteger("BLGC0011"));
                    transferProcess.setBlgc0012(blgcJsonObject.getString("BLGC0012"));
                    transferProcessList.add(transferProcess);
                });
            }
        });
        transferProcessList.sort(comparing(TransferProcess::getBlgc0011).reversed());
        TransferProcess transferProcess = transferProcessList.get(CommonConstant.ZERO_INT);
        return transferProcess;
    }

    public void deailBLGCjson(CallBack callBack,String letterNumber,Integer number,TransferApproval transferApproval,String blgc006,String blgc007,String blgc008){
        JSONObject blgcJson=new JSONObject();
        String orgId = transferApproval.getOrgId();
        OrgExchangeArea orgByCode = orgMapper.findOrgByCode(orgId);
        //经办党组织代码
        blgcJson.put("BLGC0001",null==orgByCode?null:orgByCode.getD01001());
        System.out.println("查询的经办党组织orgid: "+orgId);
        if (StrUtil.isNotBlank(letterNumber)){
            blgcJson.put("uniqueKey",letterNumber);
        }
        //经办党组织名称
        blgcJson.put("BLGC0002",null==orgByCode?"经办党组织名称":orgByCode.getName());
        //经办人的姓名
        blgcJson.put("BLGC0003",null==orgByCode?"经办人的姓名":orgByCode.getContacter());
        //经办人的联系电话（手机）
        blgcJson.put("BLGC0004",null==orgByCode?"经办人的联系电话（手机）":orgByCode.getContactPhone());
        //办理日期
        blgcJson.put("BLGC0005", DateUtil.format(transferApproval.getCreateTime(),"yyyy-MM-dd HH:mm:ss"));
        //办理情况
        blgcJson.put("BLGC0006",blgc006);
        //办理意见
        blgcJson.put("BLGC0007",blgc007);
        //备注
        blgcJson.put("BLGC0008",blgc008);
        //与党员见面人
        blgcJson.put("BLGC0009",null);
        //党员报到日期 DateUtil.format(reportTime,"yyyy-MM-dd HH:mm:ss")
        blgcJson.put("BLGC0010",null);
        //顺序号
        blgcJson.put("BLGC0011",String.valueOf(number+1));
        //是否由上级党组织代办,1是0否
        blgcJson.put("BLGC0012",String.valueOf(transferApproval.getIsInstead()));
        callBack.setBackData(blgcJson.toJSONString());
        callBackMapper.updateById(callBack);
    }

    public TransferApproval endApproval(String letterNumber){
        TransFerExchangeArea transFerExchangeArea = transFerMapper.selectOne(new QueryWrapper<TransFerExchangeArea>().eq("letter_number", letterNumber));
        String data = transFerExchangeArea.getData();
        JSONObject jsonObject =JSONObject.parseObject(data);
        String currentApprovalId = jsonObject.getString("currentApprovalId");
        JSONArray approvalJson = jsonObject.getJSONArray("approval");
        Map<String,JSONObject> jsonMap = new HashMap<>();
        approvalJson.forEach(jsob->{
            JSONObject jsonO = (JSONObject) jsob;
            String id = jsonO.getString("id");
            jsonMap.put(id,jsonO);

        });
        JSONObject mapJsonObject = jsonMap.get(currentApprovalId);
        String parentId = mapJsonObject.getString("parentId");
        if (parentId.equals("-1")){
            for (JSONObject object : jsonMap.values()) {
                String parentId1 = object.getString("parentId");
                if (parentId1.equals(currentApprovalId)){
                    parentId=parentId1;
                }
            }

        }
        JSONObject parentJson = jsonMap.get(parentId);
        System.out.println("中间审核节点==>"+ parentJson);
        approvalJson.sort(comparing(obj -> JSONObject.toJavaObject((JSONObject) obj, TransferApproval.class).getCreateTime()).reversed());
        System.out.println("blgc数据====> "+approvalJson.toJSONString());
        TransferApproval transferApproval;
        // TODO: 2022/4/28  这里到底要取哪一个，需要根据流程步骤来看。以及流程双方节点来看
        if (approvalJson.size()>CommonConstant.THREE_INT){
            transferApproval = JSONObject.toJavaObject(parentJson, TransferApproval.class);
        }else {
            transferApproval = JSONObject.toJavaObject(parentJson, TransferApproval.class);
        }

        return transferApproval;
    }


    /**
     * 通过本地拉取情况数据修复关系转接和中组部交换区不一致得情况
     * **/
    public void  repairTransferData(){
        //获取所有本地已经完成的关系转接，确认和交换区情况
        List<TransFerExchangeArea> alltransferData = transferService.findAlltransferData(null);
        alltransferData.forEach(transFerExchangeArea -> {
            String letterNumber = transFerExchangeArea.getLetterNumber();
            System.out.println("letterNumber ---> "+letterNumber);
            CallBack letterCallBack = callBackMapper.selectOne(new QueryWrapper<CallBack>().eq("letter_number", letterNumber));
            String jsonData = letterCallBack.getData();
            if (jsonData.equals("未查询到数据"))return;
            JSONArray jsonArray = JSONArray.parseArray(jsonData);
            jsonArray.forEach(jsonObject->{
                JSONObject dataObject= (JSONObject) jsonObject;
                JSONArray blgcJsonArray = dataObject.getJSONArray("BLGC");
                if (dataObject.getString("uniqueKey").equals(letterNumber)){
                    List<TransferProcess> transferProcessList =new ArrayList<>();
                    blgcJsonArray.forEach(blgcJson->{
                        JSONObject blgcJsonObject= (JSONObject) blgcJson;
                        TransferProcess transferProcess = new TransferProcess();
                        transferProcess.setBlgc0001(blgcJsonObject.getString("BLGC0001"));
                        transferProcess.setBlgc0002(blgcJsonObject.getString("BLGC0002"));
                        transferProcess.setBlgc0003(blgcJsonObject.getString("BLGC0003"));
                        transferProcess.setBlgc0004(blgcJsonObject.getString("BLGC0004"));
                        transferProcess.setBlgc0005(blgcJsonObject.getString("BLGC0005"));
                        transferProcess.setBlgc0006(blgcJsonObject.getString("BLGC0006"));
                        transferProcess.setBlgc0007(blgcJsonObject.getString("BLGC0007"));
                        transferProcess.setBlgc0008(blgcJsonObject.getString("BLGC0008"));
                        transferProcess.setBlgc0009(blgcJsonObject.getString("BLGC0009"));
                        transferProcess.setBlgc0010(blgcJsonObject.getString("BLGC0010"));
                        transferProcess.setBlgc0011(blgcJsonObject.getInteger("BLGC0011"));
                        transferProcess.setBlgc0012(blgcJsonObject.getString("BLGC0012"));
                        transferProcessList.add(transferProcess);
                    });
                    List<TransferProcess> dataList = transferProcessList.stream().sorted(Comparator.comparing(TransferProcess::getBlgc0011).reversed()).collect(Collectors.toList());
                    if (dataList.size()>0){
                        TransferProcess transferProcess = dataList.get(CommonConstant.ZERO_INT);
                        System.out.println("letterNumber最后一个数据状态 ---> " + transferProcess.toString());
                        CallBack updateCallBack =new CallBack();
                        updateCallBack.setId(letterCallBack.getId());
                        updateCallBack.setCheck(transferProcess.getBlgc0006());
                        callBackMapper.updateById(updateCallBack);
                    }
                }
            });
        });
    }

    @Autowired
    private TransferContentMapper contentMapper;
    /**
     * 处理修复兼容中组部大小写不一致的情况情况
     * **/
    public void  deailData(){
        //获取所有包含小写c02的
        LambdaQueryWrapper<TransferContent> c02 = new QueryWrapper<TransferContent>().lambda()
                .like(TransferContent::getContent, "c02");
        List<TransferContent> transferContents = contentMapper.selectList(c02);
        for (TransferContent transferContent : transferContents) {
            String content = transferContent.getContent();
            JSONArray objects = JSONArray.parseArray(content);
            for (Object object : objects) {
                JSONObject dataJsonObject= (JSONObject) object;
                JSONArray dyxx = dataJsonObject.getJSONArray("DYXX");
                JSONObject jSXJson = dataJsonObject.getJSONObject("JSX");
                String uniqueKey = jSXJson.getString("uniqueKey");
                //if (!uniqueKey.equals("0510001049632023112305110"))continue;
                //查看是否包含小写c02
                for (Object json : dyxx) {
                    JSONObject jsonObjectpanduan = (JSONObject) json;
                    if (jsonObjectpanduan.containsKey("c01")){
                        Mem mem = this.decryptMem(dyxx);
                        if (ObjectUtil.isEmpty(mem))continue;
                        LambdaQueryWrapper<TransFerExchangeArea> transFerExchangeAreaLambdaQueryWrapper = new QueryWrapper<TransFerExchangeArea>().lambda()
                                .eq(TransFerExchangeArea::getLetterNumber,uniqueKey).isNull(TransFerExchangeArea::getDeleteTime);
                        TransFerExchangeArea transFerExchangeArea = transFerMapper.selectOne(transFerExchangeAreaLambdaQueryWrapper);
                        if (ObjectUtil.isNull(transFerExchangeArea))continue;
                        String data = transFerExchangeArea.getData();
                        JSONObject jsonObject = JSONObject.parseObject(data);
                        jsonObject.put("name",mem.getName());
                        jsonObject.remove("extraData");
                        JSONObject memJson = JSONObject.parseObject(JSONObject.toJSONString(mem));
                        jsonObject.put("extraData",memJson);
                        transFerExchangeArea.setData(jsonObject.toJSONString());
                        System.out.println("修复数据++++===>  "+uniqueKey);
                        transFerMapper.updateById(transFerExchangeArea);
                    }
                }
            }
        }
    }

    public Mem decryptMem(JSONArray dyxxJson){
        Mem mem= new Mem();
        for (Object json : dyxxJson) {
            JSONObject jsonObject = (JSONObject) json;
            JSONObject c01=null;
            if (jsonObject.containsKey("C01")){
                c01= jsonObject.getJSONObject("C01");
            }
            if (jsonObject.containsKey("c01")){
                c01= jsonObject.getJSONObject("c01");
            }
            if (jsonObject.containsKey("A3")){
                c01= jsonObject.getJSONObject("A3");
            }
            if (ObjectUtil.isNotNull(c01)){
                // TODO: 2022/4/19 处理党员基本信息
                //党员基础信息
                //党员唯一标识
                // c01Json.put("C01ID",transFerMem.getCode());
                //党员唯一标识
                //jSXJson.put("JSX0001",transFerMem.getCode());
                String c01ID = c01.getString("C01ID");
                if (StrUtil.isEmpty(c01ID)){
                    c01ID = c01.getString("c01ID");
                }
                mem.setCode(c01ID);
                //党员联系电话（手机）
                //jSXJson.put("JSX0022",transFerMem.getPhone());
                String c01016 = c01.getString("C01016");
                if (StrUtil.isEmpty(c01016)){
                    c01016 = c01.getString("c01016");
                }
                mem.setPhone(c01016);
                //党员其他联系方式
                //党组织唯一标识
                //c01Json.put("D01ID",transFerMem.getOrgCode());
                //转出党组织ID
                //jSXJson.put("JSX0011",transFerMem.getOrgCode());
                //姓名
                //c01Json.put("C01002",transFerMem.getName());
                String c01002 = c01.getString("C01002");
                if (StrUtil.isEmpty(c01002)){
                    c01002 = c01.getString("c01002");
                }
                mem.setName(c01002);
                //性别
                //c01Json.put("C01003",transFerMem.getSexCode());
                String c01003 = c01.getString("C01003");
                if (StrUtil.isEmpty(c01002)){
                    c01003 = c01.getString("c01003");
                }
                mem.setSexCode(c01003);
                //公民身份号码
                //c01Json.put("C01004",transFerMem.getIdcard());
                String c01004 = c01.getString("C01004");
                if (StrUtil.isEmpty(c01002)){
                    c01004 = c01.getString("c01004");
                }
                mem.setIdcard(c01004);
                //出生日期
                //c01Json.put("C01005",DateUtil.format(transFerMem.getBirthday(),"yyyy-MM-dd HH:mm:ss"));
                Date c01005 = c01.getDate("C01005");
                if (ObjectUtil.isNull(c01005)){
                    c01005 = c01.getDate("c01005");
                }
                mem.setBirthday(c01005);
                //学历
                //c01Json.put("C01006",transFerMem.getd07Code());
                String c01006 = c01.getString("C01006");
                if (StrUtil.isEmpty(c01006)){
                    c01006 = c01.getString("c01006");
                }
                mem.setd07Code(c01006);
                //学位
                //c01Json.put("C01007",transFerMem.getD145Code());
                String c01007 = c01.getString("C01007");
                if (StrUtil.isEmpty(c01007)){
                    c01007 = c01.getString("c01007");
                }
                mem.setD145Code(c01007);
                //民族
                //c01Json.put("C01008",transFerMem.getd06Code());
                String c01008 = c01.getString("C01008");
                if (StrUtil.isEmpty(c01007)){
                    c01008 = c01.getString("c01008");
                }
                mem.setd06Code(c01008);
                //入党日期
                //c01Json.put("C01009",DateUtil.format(transFerMem.getJoinOrgDate(),"yyyy-MM-dd HH:mm:ss"));
                Date c01009 = c01.getDate("C01009");
                if (ObjectUtil.isNull(c01009)){
                    c01009= c01.getDate("c01009");
                }
                mem.setJoinOrgDate(c01009);
                //转正日期
                //c01Json.put("C01010",DateUtil.format(transFerMem.getFullMemberDate(),"yyyy-MM-dd HH:mm:ss"));
                Date c01010 = c01.getDate("C01010");
                if (ObjectUtil.isNull(c01010)){
                    c01010 = c01.getDate("c01010");
                }
                mem.setFullMemberDate(c01010);

                //人员类别
                //c01Json.put("C01001",transFerMem.getd08Code());
                String c01001 = c01.getString("C01001");
                if (StrUtil.isEmpty(c01001)){
                    c01001 = c01.getString("c01001");
                }
                mem.setd08Code(c01001);
                mem.setd08Code(CommonConstant.ONE);
                if (ObjectUtil.isNotNull(c01009)){
                    mem.setd08Code(CommonConstant.TWO);
                    mem.setd08Name("预备党员");
                }
                if (ObjectUtil.isNotNull(c01009)&&ObjectUtil.isNotNull(c01010)){
                    mem.setd08Code(CommonConstant.ONE);
                    mem.setd08Name("正式党员");
                }
                if (mem.getd08Code().equals(CommonConstant.TWO)){
                    mem.setExtendPreparDate(DateUtil.offset( mem.getJoinOrgDate(), DateField.YEAR,CommonConstant.ONE_INT));
                }
                //党龄校正值
                //c01Json.put("C01011","0");
                //工作岗位
                //c01Json.put("C01012",transFerMem.getd09Code());
                String c01012 = c01.getString("C01012");
                if (StrUtil.isEmpty(c01012)){
                    c01012 = c01.getString("c01012");
                }
                mem.setd09Code(c01012);
                //新社会阶层类型
                //c01Json.put("C01013",transFerMem.getd20Code());
                String c01013 = c01.getString("C01013");
                if (StrUtil.isEmpty(c01013)){
                    c01013 = c01.getString("c01013");
                }
                mem.setd20Code(c01013);
                //从事专业技术职务
                //c01Json.put("C01014",transFerMem.getd19Code());
                String c01014 = c01.getString("C01014");
                if (StrUtil.isEmpty(c01014)){
                    c01014 = c01.getString("c01014");
                }
                mem.setd19Code(c01014);
                //是否农民工
                //c01Json.put("C01015",transFerMem.getIsFarmer());
                mem.setIsFarmer(CommonConstant.ZERO_INT);
                //手机号码
                //c01Json.put("C01016",transFerMem.getPhone());
                //联合支部所在单位
                //c01Json.put("C01017",null);
                //户籍所在地
                //c01Json.put("C01018",transFerMem.getHouseholdRegister());
                String c01018 = c01.getString("C01018");
                if (StrUtil.isEmpty(c01018)){
                    c01018 = c01.getString("c01018");
                }
                mem.setHouseholdRegister(c01018);
                //现居住地
                //c01Json.put("C01019",transFerMem.getHomeAddress());
                String c01019 = c01.getString("C01019");
                if (StrUtil.isEmpty(c01019)){
                    c01019 = c01.getString("c01019");
                }
                mem.setHomeAddress(c01019);
                //进入本信息系统类型
                //c01Json.put("C01023",transFerMem.getd11Code());
                String c01023 = c01.getString("C01023");
                if (StrUtil.isEmpty(c01023)){
                    c01023 = c01.getString("c01023");
                }
                mem.setd11Code(c01023);
                //进入本信息系统日期
                //c01Json.put("C01024",DateUtil.format(transFerMem.getJoinOrgPartyDate(),"yyyy-MM-dd HH:mm:ss"));
                Date c01024 = c01.getDate("C01024");
                if (ObjectUtil.isNull(c01024)){
                    c01024 = c01.getDate("c01024");
                }
                mem.setJoinOrgPartyDate(c01024);
                //进入本信息系统操作党组织
                //c01Json.put("C01025",transFerMem.getOrgCode());
                //离开本信息系统类型//跨省转出DM25
                //c01Json.put("C01026","3");
                //离开本信息系统日期
                //c01Json.put("C01027",DateUtil.format(transferRecord.getCreateTime(),"yyyy-MM-dd HH:mm:ss"));
                mem.setCreateTime(new Date());
                //离开本信息系统操作党组织
                //c01Json.put("C01028",transFerMem.getOrgCode());
                //人员状态//当前人员DM36
                //c01Json.put("C01UP1","1");
                //更新时间戳
                //c01Json.put("C01UP2",DateUtil.format(transFerMem.getUpdateTime(),"yyyy-MM-dd HH:mm:ss"));
                mem.setUpdateTime(new Date());
                //操作党组织
                //c01Json.put("C01UP3",transFerMem.getOrgCode());
            }
        }
        return mem;
    }

    /**
     * 修复上传办理过程顺序号数据错误问题
     */
    private void repairTransferUploadDataBLGC0011(){
        // 不用关联转接程序处理省外转入的
        log.warn("修复上传办理过程数据错误问题执行开始==========================");
        int no = 1;
        int size = 1000;
        // 查询顺序号问题的上传数据
        Page<TransferUploadData> page = new Page<>();
        LambdaQueryWrapper<TransferUploadData> wrapper = Wrappers.lambdaQuery();
        wrapper.select(TransferUploadData::getId, TransferUploadData::getUploadData);
        wrapper
                // 办理过程
                .eq(TransferUploadData::getDataType, CommonConstant.TWO_INT)
                // 未成功
                .eq(TransferUploadData::getIsSuccess, CommonConstant.ZERO_INT)
                // 指定问题
                .like(TransferUploadData::getBackMessage, "办理过程顺序号为空或者长度超过2位")
                // 删除时间为空
                .isNull(TransferUploadData::getDeleteTime)
                .orderByAsc(TransferUploadData::getCreateTime);
        // 分页处理
        do {
            page.setCurrent(no).setSize(size);
            page = transferUploadMapper.selectPage(page, wrapper);
            log.warn("修复上传办理过程数据错误问题执行：[总数据：{}；当前页：{}]", page.getTotal(), no);

            if(CollUtil.isNotEmpty(page.getRecords())){
                List<TransferUploadData> list = page.getRecords();
                // 修改顺序号
                for (TransferUploadData transferUploadData : list) {
                    String uploadData = transferUploadData.getUploadData();
                    log.warn("修复上传办理过程数据错误问题执行-原数据：[id：{}；uploadData：{}]", transferUploadData.getId(), uploadData);
                    JSONArray jsonArray = JSONObject.parseArray(uploadData);
                    JSONArray saveArray = new JSONArray();
                    int i = 7;
                    for (Object object : jsonArray) {
                        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(object));
                        Object obj = jsonObject.get("BLGC0011");
                        if(Objects.isNull(obj) || Integer.parseInt(obj.toString()) > 99){
                            jsonObject.put("BLGC0011", String.valueOf(i));
                            i++;
                        }
                        saveArray.add(jsonObject);
                    }
                    String data = saveArray.toJSONString();
                    log.warn("修复上传办理过程数据错误问题执行-修复后数据：[id：{}；uploadData：{}]", transferUploadData.getId(), data);
                    if(StrUtil.isNotBlank(data)){
                        transferUploadData.setUploadData(data);
                        transferUploadMapper.updateById(transferUploadData);
                    }

                }
            }
            no++;
        } while (page.hasNext());
    }
}
