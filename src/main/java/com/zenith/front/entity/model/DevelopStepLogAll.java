package com.zenith.front.entity.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.zenith.front.annotation.DecryptField;
import com.zenith.front.annotation.DynamicTable;
import com.zenith.front.annotation.EnabledDecrypt;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-03
 */
@EnabledDecrypt
@DynamicTable
@TableName("ccp_develop_step_log_all")
public class DevelopStepLogAll extends Model<DevelopStepLogAll> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @TableField("code")
    private String code;

    @TableField("es_id")
    private String esId;

    /**
     * 姓名
     */
    @DecryptField
    @TableField("name")
    private String name;

    /**
     * 组织名称
     */
    @TableField("org_name")
    private String orgName;

    /**
     * 组织zb_code
     */
    @TableField("org_zb_code")
    private String orgZbCode;

    /**
     * 组织org_code
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 组织层级码
     */
    @TableField("log_org_code")
    private String logOrgCode;

    /**
     * 人员code唯一标识
     */
    @TableField("mem_code")
    private String memCode;

    /**
     * 入党介绍人（发展对象=》预备党员）
     */
    @TableField("topre_introduction_mem")
    private String topreIntroductionMem;

    /**
     * 支委会会议记录扫描件（发展对象=》预备党员）
     */
    @TableField("topre_file_url")
    private String topreFileUrl;

    /**
     * 入党志愿书扫描件（发展对象=》预备党员）
     */
    @TableField("topre_join_book_url")
    private String topreJoinBookUrl;

    /**
     * 上级党委审批日期（发展对象=》预备党员）
     */
    @TableField("topre_committee_date")
    private Date topreCommitteeDate;

    /**
     * 入党志愿书编号（发展对象=》预备党员）
     */
    @TableField("topre_join_book_num")
    private String topreJoinBookNum;

    /**
     * 召开支委会日期(成为预备党员日期)
     */
    @TableField("topre_join_org_date")
    private Date topreJoinOrgDate;

    /**
     * 上级党委审批通知扫描件（发展对象=》预备党员）
     */
    @TableField("topre_committee_file_url")
    private String topreCommitteeFileUrl;

    /**
     * 人员类别
     */
    @TableField("d08_name")
    private String d08Name;

    /**
     * 人员类别
     */
    @TableField("d08_code")
    private String d08Code;

    /**
     * 加入共产党类型code（发展对象=》预备党员）
     */
    @TableField("join_org_code")
    private String joinOrgCode;

    /**
     * 加入共产党类型（发展对象=》预备党员）d27
     */
    @TableField("join_org_name")
    private String joinOrgName;

    /**
     * 进入支部类型code（发展对象=》预备党员）
     */
    @TableField("d11_code")
    private String d11Code;

    /**
     * 进入支部类型（发展对象=》预备党员）
     */
    @TableField("d11_name")
    private String d11Name;

    /**
     * 入党介绍人(预备党员=》正式党员）
     */
    @TableField("topart_introduction_mem")
    private String topartIntroductionMem;

    /**
     * 支委会会议记录扫描件(预备党员=》正式党员）
     */
    @TableField("topart_file_url")
    private String topartFileUrl;

    /**
     * 召开支委会日期(成为正式党员日期)
     */
    @TableField("topart_turn_party_date")
    private Date topartTurnPartyDate;

    /**
     * 审批结果(预备党员=》正式党员）
     */
    @TableField("d28_name")
    private String d28Name;

    /**
     * 审批结果code(预备党员=》正式党员）
     */
    @TableField("d28_code")
    private String d28Code;

    /**
     * 上级党委审批通知扫描件(预备党员=》正式党员）
     */
    @TableField("topart_committee_file_url")
    private String topartCommitteeFileUrl;

    /**
     * 上级党委审批日期(预备党员=》正式党员）
     */
    @TableField("topart_committee_date")
    private Date topartCommitteeDate;

    /**
     * 入党宣誓日期(预备党员=》正式党员）
     */
    @TableField("topart_oath_date")
    private Date topartOathDate;

    /**
     * 延长预备期到的时间
     */
    @TableField("extend_prepar_date")
    private Date extendPreparDate;

    /**
     * 支委会会议记录扫描件(申请人=》积极分子）
     */
    @TableField("toactive_apply_scan_file")
    private String toactiveApplyScanFile;

    /**
     * 入党申请人联系人(申请人=》积极分子）
     */
    @TableField("toactive_context_person")
    private String toactiveContextPerson;

    /**
     * 召开支委会日期(成为积极分子日期)
     */
    @TableField("active_date")
    private Date activeDate;

    /**
     * 支委会会议记录扫描件(积极分子=》发展对象）
     */
    @TableField("toobj_file_url")
    private String toobjFileUrl;

    /**
     * 培养教育考察时间(积极分子=》发展对象）
     */
    @TableField("toobj_cultivate_date")
    private Date toobjCultivateDate;

    /**
     * 考察材料扫描件(积极分子=》发展对象）
     */
    @TableField("toobj_check_file_url")
    private String toobjCheckFileUrl;

    /**
     * 培养联系人(积极分子=》发展对象）
     */
    @TableField("toobj_context_mem")
    private String toobjContextMem;

    /**
     * 召开支委会日期(成为发展对象时间
     */
    @TableField("object_date")
    private Date objectDate;

    /**
     * 取消资格方式(1=退回入党申请人阶段,2=退回积极分子阶段)
     */
    @TableField("canncel_date")
    private Date canncelDate;

    @TableField("canncel_code")
    private String canncelCode;

    @TableField("canncel_name")
    private String canncelName;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 入党宣誓照片（预备党员=》正式党员）
     */
    @TableField("topart_oath_date_url")
    private String topartOathDateUrl;

    @TableField("is_history")
    private Integer isHistory;

    @TableField("update_account")
    private String updateAccount;

    /**
     * 工作岗位(发展对象>>预备党员)
     */
    @TableField("d09_code")
    private String d09Code;

    @TableField("d09_name")
    private String d09Name;

    /**
     * 是否系统外,1-系统外,0--系统内
     */
    @TableField("is_out_system")
    private Integer isOutSystem;

    /**
     * 系统外发展时所在党支部name
     */
    @TableField("out_branch_org_name")
    private String outBranchOrgName;

    @TableField("legacy_data")
    private Integer legacyData;

    /**
     * 发展党员身份证
     */
    @DecryptField
    @TableField("idcard")
    private String idcard;

    /**
     * 民族code
     */
    @TableField("d06_code")
    private String d06Code;

    /**
     * 民族名称
     */
    @TableField("d06_name")
    private String d06Name;

    /**
     * 籍贯
     */
    @TableField("d48_code")
    private String d48Code;

    /**
     * 籍贯名称
     */
    @TableField("d48_name")
    private String d48Name;

    /**
     * 性别
     */
    @TableField("sex_code")
    private String sexCode;

    /**
     * 性别name
     */
    @TableField("sex_name")
    private String sexName;

    /**
     * 出生日期
     */
    @TableField("birthday")
    private Date birthday;

    /**
     * 电话
     */
    @DecryptField
    @TableField("phone")
    private String phone;

    /**
     * 学历code
     */
    @TableField("d07_code")
    private String d07Code;

    /**
     * 学历名称
     */
    @TableField("d07_name")
    private String d07Name;

    /**
     * 一线情况code
     */
    @TableField("d21_code")
    private String d21Code;

    /**
     * 一线情况名称
     */
    @TableField("d21_name")
    private String d21Name;
    /**
     * 是否农民工
     */
    @TableField("is_farmer")
    private Integer isFarmer;
    /**
     * 发展党员年龄
     */
    @TableField("age")
    private Integer age;
    /**
     * 是否劳务派遣
     */
    @TableField("is_dispatch")
    private Integer isDispatch;
    /**
     * 是否是先进模范人物
     */
    @TableField("advanced_model_code")
    private String advancedModelCode;
    /**
     * 聘任专业技术职务名称
     */
    @TableField("d19_name")
    private String d19Name;
    /**
     * 聘任专业技术职务code
     */
    @TableField("d19_code")
    private String d19Code;
    /**
     * 政治面貌code
     */
    @TableField("d89_code")
    private String d89Code;
    /**
     * 政治面貌name
     */
    @TableField("d89_name")
    private String d89Name;
    /**
     * 追认时间
     */
    @TableField("ratification_time")
    private Date ratificationTime;

    /**
     * 新社会阶层类型名称
     */
    @TableField("d20_name")
    private String d20Name;

    /**
     * 新社会阶层类型code
     */
    @TableField("d20_code")
    private String d20Code;
    /**
     * 单位类别_code
     */
    @TableField("d04_code")
    private String d04Code;
    /**
     * 单位类别_name
     */
    @TableField("d04_name")
    private String d04Name;
    /**
     * 成为预备党员年度
     */
    @TableField("topre_join_org_date_year")
    private Integer topreJoinOrgDateYear;

    /**
     * 乡镇，街道情况（1是乡，2是镇，3是街道）
     */
    @TableField("is_towns")
    private Integer isTowns;
    /***
     * 行政村，城市社区，乡镇社区情况（1行政村，2城市社区，3是乡镇社区）
     * */
    @TableField("is_community")
    private Integer isCommunity;

    /**
     * 工作性质
     */
    @TableField("job_nature_code")
    private String jobNatureCode;
    /**
     * 毕业院校（专科及以上填写）
     */
    @TableField("byyx")
    private String byyx;
    /**
     * 毕业专业（专科及以上填写）
     */
    @TableField("d88_code")
    private String d88Code;

    @TableField("d88_name")
    private String d88Name;
    /**
     * 人员单位code
     */
    @TableField("unit_code")
    private String unitCode;

    /**
     * 人员单位名称
     */
    @TableField("unit_name")
    private String unitName;

    @TableField("unit_information")
    private String unitInformation;

    @TableField("has_unit_province")
    private Integer hasUnitProvince;

    @TableField("\"has_unit_statistics\"")
    private Integer hasUnitStatistics;

    @TableField("statistical_unit")
    private String statisticalUnit;
    /**
     * 申请入党日期
     */
    @TableField("apply_date")
    private Date applyDate;

    /**
     * 家庭住址
     */
    @DecryptField
    @TableField("home_address")
    private String homeAddress;

    /**
     * 是否 在关系转接中
     */
    @TableField("is_transfer")
    private Integer isTransfer;

    /**
     * 离开党组织日期
     */
    @TableField("leave_org_date")
    private Date leaveOrgDate;

    /**
     * 是否产业工人
     */
    @TableField("has_worker")
    private Integer hasWorker;

    /**
     * 是否高知识群体
     */
    @TableField("has_high_knowledge")
    private Integer hasHighKnowledge;

    /**
     * 是否高层次人才
     */
    @TableField("has_high_level_talents")
    private Integer hasHighLevelTalents;

    /**
     * 是否民族院校
     */
    @TableField("has_national_colleges")
    private Integer hasNationalColleges;

    /**
     * 知识分子情况
     */
    @TableField("d154_code")
    private String d154Code;

    @TableField("d154_name")
    private String d154Name;

    /**
     * 是否需要自动计算年级（0 否 ，1 是）
     */
    @TableField("has_calculation_grade")
    private Integer hasCalculationGrade;

    /**
     * 学制
     */
    @TableField("educational_system")
    private String educationalSystem;

    /**
     * 入学时间
     */
    @TableField("enter_school_date")
    private Date enterSchoolDate;

    /**
     * 国民经济行业CODE
     */
    @TableField("d194_code")
    private String d194Code;

    /**
     * 国民经济行业name
     */
    @TableField("d194_name")
    private String d194Name;

    /**
     * 生产性服务行业CODE
     */
    @TableField("d195_code")
    private String d195Code;

    /**
     * 生产性服务行业name
     */
    @TableField("d195_name")
    private String d195Name;

    /**
     * 第几产业（1，2，3）
     */
    @TableField("industry")
    private String industry;

    /**
     * 生产性服务行业所属的产业。有一、二、三产业，分别对应1、2、3；统计出数使用
     */
    @TableField("industry_d195")
    private Integer industryD195;

    public Integer getIndustryD195() {
        return industryD195;
    }

    public void setIndustryD195(Integer industryD195) {
        this.industryD195 = industryD195;
    }

    public String getD194Code() {
        return d194Code;
    }

    public void setD194Code(String d194Code) {
        this.d194Code = d194Code;
    }

    public String getD194Name() {
        return d194Name;
    }

    public void setD194Name(String d194Name) {
        this.d194Name = d194Name;
    }

    public String getD195Code() {
        return d195Code;
    }

    public void setD195Code(String d195Code) {
        this.d195Code = d195Code;
    }

    public String getD195Name() {
        return d195Name;
    }

    public void setD195Name(String d195Name) {
        this.d195Name = d195Name;
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public Integer getIsTowns() {
        return isTowns;
    }

    public void setIsTowns(Integer isTowns) {
        this.isTowns = isTowns;
    }

    public Integer getIsCommunity() {
        return isCommunity;
    }

    public void setIsCommunity(Integer isCommunity) {
        this.isCommunity = isCommunity;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEsId() {
        return esId;
    }

    public void setEsId(String esId) {
        this.esId = esId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgZbCode() {
        return orgZbCode;
    }

    public void setOrgZbCode(String orgZbCode) {
        this.orgZbCode = orgZbCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getLogOrgCode() {
        return logOrgCode;
    }

    public void setLogOrgCode(String logOrgCode) {
        this.logOrgCode = logOrgCode;
    }

    public String getMemCode() {
        return memCode;
    }

    public void setMemCode(String memCode) {
        this.memCode = memCode;
    }

    public String getTopreIntroductionMem() {
        return topreIntroductionMem;
    }

    public void setTopreIntroductionMem(String topreIntroductionMem) {
        this.topreIntroductionMem = topreIntroductionMem;
    }

    public String getTopreFileUrl() {
        return topreFileUrl;
    }

    public void setTopreFileUrl(String topreFileUrl) {
        this.topreFileUrl = topreFileUrl;
    }

    public String getTopreJoinBookUrl() {
        return topreJoinBookUrl;
    }

    public void setTopreJoinBookUrl(String topreJoinBookUrl) {
        this.topreJoinBookUrl = topreJoinBookUrl;
    }

    public Date getTopreCommitteeDate() {
        return topreCommitteeDate;
    }

    public void setTopreCommitteeDate(Date topreCommitteeDate) {
        this.topreCommitteeDate = topreCommitteeDate;
    }

    public String getTopreJoinBookNum() {
        return topreJoinBookNum;
    }

    public void setTopreJoinBookNum(String topreJoinBookNum) {
        this.topreJoinBookNum = topreJoinBookNum;
    }

    public Date getTopreJoinOrgDate() {
        return topreJoinOrgDate;
    }

    public void setTopreJoinOrgDate(Date topreJoinOrgDate) {
        this.topreJoinOrgDate = topreJoinOrgDate;
    }

    public String getTopreCommitteeFileUrl() {
        return topreCommitteeFileUrl;
    }

    public void setTopreCommitteeFileUrl(String topreCommitteeFileUrl) {
        this.topreCommitteeFileUrl = topreCommitteeFileUrl;
    }

    public String getD08Name() {
        return d08Name;
    }

    public void setD08Name(String d08Name) {
        this.d08Name = d08Name;
    }

    public String getD08Code() {
        return d08Code;
    }

    public void setD08Code(String d08Code) {
        this.d08Code = d08Code;
    }

    public String getJoinOrgCode() {
        return joinOrgCode;
    }

    public void setJoinOrgCode(String joinOrgCode) {
        this.joinOrgCode = joinOrgCode;
    }

    public String getJoinOrgName() {
        return joinOrgName;
    }

    public void setJoinOrgName(String joinOrgName) {
        this.joinOrgName = joinOrgName;
    }

    public String getD11Code() {
        return d11Code;
    }

    public void setD11Code(String d11Code) {
        this.d11Code = d11Code;
    }

    public String getD11Name() {
        return d11Name;
    }

    public void setD11Name(String d11Name) {
        this.d11Name = d11Name;
    }

    public String getTopartIntroductionMem() {
        return topartIntroductionMem;
    }

    public void setTopartIntroductionMem(String topartIntroductionMem) {
        this.topartIntroductionMem = topartIntroductionMem;
    }

    public String getTopartFileUrl() {
        return topartFileUrl;
    }

    public void setTopartFileUrl(String topartFileUrl) {
        this.topartFileUrl = topartFileUrl;
    }

    public Date getTopartTurnPartyDate() {
        return topartTurnPartyDate;
    }

    public void setTopartTurnPartyDate(Date topartTurnPartyDate) {
        this.topartTurnPartyDate = topartTurnPartyDate;
    }

    public String getD28Name() {
        return d28Name;
    }

    public void setD28Name(String d28Name) {
        this.d28Name = d28Name;
    }

    public String getD28Code() {
        return d28Code;
    }

    public void setD28Code(String d28Code) {
        this.d28Code = d28Code;
    }

    public String getTopartCommitteeFileUrl() {
        return topartCommitteeFileUrl;
    }

    public void setTopartCommitteeFileUrl(String topartCommitteeFileUrl) {
        this.topartCommitteeFileUrl = topartCommitteeFileUrl;
    }

    public Date getTopartCommitteeDate() {
        return topartCommitteeDate;
    }

    public void setTopartCommitteeDate(Date topartCommitteeDate) {
        this.topartCommitteeDate = topartCommitteeDate;
    }

    public Date getTopartOathDate() {
        return topartOathDate;
    }

    public void setTopartOathDate(Date topartOathDate) {
        this.topartOathDate = topartOathDate;
    }

    public Date getExtendPreparDate() {
        return extendPreparDate;
    }

    public void setExtendPreparDate(Date extendPreparDate) {
        this.extendPreparDate = extendPreparDate;
    }

    public String getToactiveApplyScanFile() {
        return toactiveApplyScanFile;
    }

    public void setToactiveApplyScanFile(String toactiveApplyScanFile) {
        this.toactiveApplyScanFile = toactiveApplyScanFile;
    }

    public String getToactiveContextPerson() {
        return toactiveContextPerson;
    }

    public void setToactiveContextPerson(String toactiveContextPerson) {
        this.toactiveContextPerson = toactiveContextPerson;
    }

    public Date getActiveDate() {
        return activeDate;
    }

    public void setActiveDate(Date activeDate) {
        this.activeDate = activeDate;
    }

    public String getToobjFileUrl() {
        return toobjFileUrl;
    }

    public void setToobjFileUrl(String toobjFileUrl) {
        this.toobjFileUrl = toobjFileUrl;
    }

    public Date getToobjCultivateDate() {
        return toobjCultivateDate;
    }

    public void setToobjCultivateDate(Date toobjCultivateDate) {
        this.toobjCultivateDate = toobjCultivateDate;
    }

    public String getToobjCheckFileUrl() {
        return toobjCheckFileUrl;
    }

    public void setToobjCheckFileUrl(String toobjCheckFileUrl) {
        this.toobjCheckFileUrl = toobjCheckFileUrl;
    }

    public String getToobjContextMem() {
        return toobjContextMem;
    }

    public void setToobjContextMem(String toobjContextMem) {
        this.toobjContextMem = toobjContextMem;
    }

    public Date getObjectDate() {
        return objectDate;
    }

    public void setObjectDate(Date objectDate) {
        this.objectDate = objectDate;
    }

    public Date getCanncelDate() {
        return canncelDate;
    }

    public void setCanncelDate(Date canncelDate) {
        this.canncelDate = canncelDate;
    }

    public String getCanncelCode() {
        return canncelCode;
    }

    public void setCanncelCode(String canncelCode) {
        this.canncelCode = canncelCode;
    }

    public String getCanncelName() {
        return canncelName;
    }

    public void setCanncelName(String canncelName) {
        this.canncelName = canncelName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public String getTopartOathDateUrl() {
        return topartOathDateUrl;
    }

    public void setTopartOathDateUrl(String topartOathDateUrl) {
        this.topartOathDateUrl = topartOathDateUrl;
    }

    public Integer getIsHistory() {
        return isHistory;
    }

    public void setIsHistory(Integer isHistory) {
        this.isHistory = isHistory;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    public String getD09Code() {
        return d09Code;
    }

    public void setD09Code(String d09Code) {
        this.d09Code = d09Code;
    }

    public String getD09Name() {
        return d09Name;
    }

    public void setD09Name(String d09Name) {
        this.d09Name = d09Name;
    }

    public Integer getIsOutSystem() {
        return isOutSystem;
    }

    public void setIsOutSystem(Integer isOutSystem) {
        this.isOutSystem = isOutSystem;
    }

    public String getOutBranchOrgName() {
        return outBranchOrgName;
    }

    public void setOutBranchOrgName(String outBranchOrgName) {
        this.outBranchOrgName = outBranchOrgName;
    }

    public Integer getLegacyData() {
        return legacyData;
    }

    public void setLegacyData(Integer legacyData) {
        this.legacyData = legacyData;
    }

    public String getIdcard() {
        return idcard;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }

    public String getD06Code() {
        return d06Code;
    }

    public void setD06Code(String d06Code) {
        this.d06Code = d06Code;
    }

    public String getD06Name() {
        return d06Name;
    }

    public void setD06Name(String d06Name) {
        this.d06Name = d06Name;
    }

    public String getD48Code() {
        return d48Code;
    }

    public void setD48Code(String d48Code) {
        this.d48Code = d48Code;
    }

    public String getD48Name() {
        return d48Name;
    }

    public void setD48Name(String d48Name) {
        this.d48Name = d48Name;
    }

    public String getSexCode() {
        return sexCode;
    }

    public void setSexCode(String sexCode) {
        this.sexCode = sexCode;
    }

    public String getSexName() {
        return sexName;
    }

    public void setSexName(String sexName) {
        this.sexName = sexName;
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getD07Code() {
        return d07Code;
    }

    public void setD07Code(String d07Code) {
        this.d07Code = d07Code;
    }

    public String getD07Name() {
        return d07Name;
    }

    public void setD07Name(String d07Name) {
        this.d07Name = d07Name;
    }

    public String getD21Code() {
        return d21Code;
    }

    public void setD21Code(String d21Code) {
        this.d21Code = d21Code;
    }

    public String getD21Name() {
        return d21Name;
    }

    public void setD21Name(String d21Name) {
        this.d21Name = d21Name;
    }

    public Integer getIsFarmer() {
        return isFarmer;
    }

    public void setIsFarmer(Integer isFarmer) {
        this.isFarmer = isFarmer;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public Integer getIsDispatch() {
        return isDispatch;
    }

    public void setIsDispatch(Integer isDispatch) {
        this.isDispatch = isDispatch;
    }

    public String getAdvancedModelCode() {
        return advancedModelCode;
    }

    public void setAdvancedModelCode(String advancedModelCode) {
        this.advancedModelCode = advancedModelCode;
    }

    public String getD19Name() {
        return d19Name;
    }

    public void setD19Name(String d19Name) {
        this.d19Name = d19Name;
    }

    public String getD19Code() {
        return d19Code;
    }

    public void setD19Code(String d19Code) {
        this.d19Code = d19Code;
    }

    public String getD89Code() {
        return d89Code;
    }

    public void setD89Code(String d89Code) {
        this.d89Code = d89Code;
    }

    public String getD89Name() {
        return d89Name;
    }

    public void setD89Name(String d89Name) {
        this.d89Name = d89Name;
    }

    public Date getRatificationTime() {
        return ratificationTime;
    }

    public void setRatificationTime(Date ratificationTime) {
        this.ratificationTime = ratificationTime;
    }

    public String getD20Name() {
        return d20Name;
    }

    public void setD20Name(String d20Name) {
        this.d20Name = d20Name;
    }

    public String getD20Code() {
        return d20Code;
    }

    public void setD20Code(String d20Code) {
        this.d20Code = d20Code;
    }

    public String getD04Code() {
        return d04Code;
    }

    public void setD04Code(String d04Code) {
        this.d04Code = d04Code;
    }

    public String getD04Name() {
        return d04Name;
    }

    public void setD04Name(String d04Name) {
        this.d04Name = d04Name;
    }

    public Integer getTopreJoinOrgDateYear() {
        return topreJoinOrgDateYear;
    }

    public void setTopreJoinOrgDateYear(Integer topreJoinOrgDateYear) {
        this.topreJoinOrgDateYear = topreJoinOrgDateYear;
    }

    public String getJobNatureCode() {
        return jobNatureCode;
    }

    public void setJobNatureCode(String jobNatureCode) {
        this.jobNatureCode = jobNatureCode;
    }

    public String getByyx() {
        return byyx;
    }

    public void setByyx(String byyx) {
        this.byyx = byyx;
    }

    public String getD88Code() {
        return d88Code;
    }

    public void setD88Code(String d88Code) {
        this.d88Code = d88Code;
    }

    public String getD88Name() {
        return d88Name;
    }

    public void setD88Name(String d88Name) {
        this.d88Name = d88Name;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getUnitInformation() {
        return unitInformation;
    }

    public void setUnitInformation(String unitInformation) {
        this.unitInformation = unitInformation;
    }

    public Integer getHasUnitProvince() {
        return hasUnitProvince;
    }

    public void setHasUnitProvince(Integer hasUnitProvince) {
        this.hasUnitProvince = hasUnitProvince;
    }

    public Integer getHasUnitStatistics() {
        return hasUnitStatistics;
    }

    public void setHasUnitStatistics(Integer hasUnitStatistics) {
        this.hasUnitStatistics = hasUnitStatistics;
    }

    public String getStatisticalUnit() {
        return statisticalUnit;
    }

    public void setStatisticalUnit(String statisticalUnit) {
        this.statisticalUnit = statisticalUnit;
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public String getHomeAddress() {
        return homeAddress;
    }

    public void setHomeAddress(String homeAddress) {
        this.homeAddress = homeAddress;
    }

    public Integer getIsTransfer() {
        return isTransfer;
    }

    public void setIsTransfer(Integer isTransfer) {
        this.isTransfer = isTransfer;
    }

    public Date getLeaveOrgDate() {
        return leaveOrgDate;
    }

    public void setLeaveOrgDate(Date leaveOrgDate) {
        this.leaveOrgDate = leaveOrgDate;
    }

    public Integer getHasWorker() {
        return hasWorker;
    }

    public void setHasWorker(Integer hasWorker) {
        this.hasWorker = hasWorker;
    }

    public Integer getHasHighKnowledge() {
        return hasHighKnowledge;
    }

    public void setHasHighKnowledge(Integer hasHighKnowledge) {
        this.hasHighKnowledge = hasHighKnowledge;
    }

    public Integer getHasHighLevelTalents() {
        return hasHighLevelTalents;
    }

    public void setHasHighLevelTalents(Integer hasHighLevelTalents) {
        this.hasHighLevelTalents = hasHighLevelTalents;
    }

    public Integer getHasNationalColleges() {
        return hasNationalColleges;
    }

    public void setHasNationalColleges(Integer hasNationalColleges) {
        this.hasNationalColleges = hasNationalColleges;
    }

    public String getD154Code() {
        return d154Code;
    }

    public void setD154Code(String d154Code) {
        this.d154Code = d154Code;
    }

    public String getD154Name() {
        return d154Name;
    }

    public void setD154Name(String d154Name) {
        this.d154Name = d154Name;
    }

    public Integer getHasCalculationGrade() {
        return hasCalculationGrade;
    }

    public void setHasCalculationGrade(Integer hasCalculationGrade) {
        this.hasCalculationGrade = hasCalculationGrade;
    }

    public String getEducationalSystem() {
        return educationalSystem;
    }

    public void setEducationalSystem(String educationalSystem) {
        this.educationalSystem = educationalSystem;
    }

    public Date getEnterSchoolDate() {
        return enterSchoolDate;
    }

    public void setEnterSchoolDate(Date enterSchoolDate) {
        this.enterSchoolDate = enterSchoolDate;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "DevelopStepLogAll{" +
                "id=" + id +
                ", code=" + code +
                ", esId=" + esId +
                ", name=" + name +
                ", orgName=" + orgName +
                ", orgZbCode=" + orgZbCode +
                ", orgCode=" + orgCode +
                ", logOrgCode=" + logOrgCode +
                ", memCode=" + memCode +
                ", topreIntroductionMem=" + topreIntroductionMem +
                ", topreFileUrl=" + topreFileUrl +
                ", topreJoinBookUrl=" + topreJoinBookUrl +
                ", topreCommitteeDate=" + topreCommitteeDate +
                ", topreJoinBookNum=" + topreJoinBookNum +
                ", topreJoinOrgDate=" + topreJoinOrgDate +
                ", topreCommitteeFileUrl=" + topreCommitteeFileUrl +
                ", d08Name=" + d08Name +
                ", d08Code=" + d08Code +
                ", joinOrgCode=" + joinOrgCode +
                ", joinOrgName=" + joinOrgName +
                ", d11Code=" + d11Code +
                ", d11Name=" + d11Name +
                ", topartIntroductionMem=" + topartIntroductionMem +
                ", topartFileUrl=" + topartFileUrl +
                ", topartTurnPartyDate=" + topartTurnPartyDate +
                ", d28Name=" + d28Name +
                ", d28Code=" + d28Code +
                ", topartCommitteeFileUrl=" + topartCommitteeFileUrl +
                ", topartCommitteeDate=" + topartCommitteeDate +
                ", topartOathDate=" + topartOathDate +
                ", extendPreparDate=" + extendPreparDate +
                ", toactiveApplyScanFile=" + toactiveApplyScanFile +
                ", toactiveContextPerson=" + toactiveContextPerson +
                ", activeDate=" + activeDate +
                ", toobjFileUrl=" + toobjFileUrl +
                ", toobjCultivateDate=" + toobjCultivateDate +
                ", toobjCheckFileUrl=" + toobjCheckFileUrl +
                ", toobjContextMem=" + toobjContextMem +
                ", objectDate=" + objectDate +
                ", canncelDate=" + canncelDate +
                ", canncelCode=" + canncelCode +
                ", canncelName=" + canncelName +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deleteTime=" + deleteTime +
                ", topartOathDateUrl=" + topartOathDateUrl +
                ", isHistory=" + isHistory +
                ", updateAccount=" + updateAccount +
                ", d09Code=" + d09Code +
                ", d09Name=" + d09Name +
                ", isOutSystem=" + isOutSystem +
                ", outBranchOrgName=" + outBranchOrgName +
                ", legacyData=" + legacyData +
                ", idcard=" + idcard +
                ", d06Code=" + d06Code +
                ", d06Name=" + d06Name +
                ", d48Code=" + d48Code +
                ", d48Name=" + d48Name +
                ", sexCode=" + sexCode +
                ", sexName=" + sexName +
                ", birthday=" + birthday +
                ", phone=" + phone +
                ", d07Code=" + d07Code +
                ", d07Name=" + d07Name +
                ", d21Code=" + d21Code +
                ", d21Name=" + d21Name +
                "}";
    }
}
