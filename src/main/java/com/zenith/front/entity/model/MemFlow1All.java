package com.zenith.front.entity.model;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.zenith.front.annotation.EnabledDecrypt;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-24
 */
@EnabledDecrypt
@TableName("mem_flow_all")
public class MemFlow1All extends Model<MemFlow1All> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 流动主键
     */
    @TableField("code")
    private String code;

    /**
     * 省外流动党员唯一码
     */
    @TableField("flow_uq_code")
    private String flowUqCode;

    /**
     * 党员主键
     */
    @TableField("mem_code")
    private String memCode;

    /**
     * 党员姓名
     */
    @TableField("mem_name")
    private String memName;

    /**
     * 党员性别
     */
    @TableField("mem_sex_code")
    private String memSexCode;

    @TableField("mem_sex_name")
    private String memSexName;

    /**
     * 党员身份证号码
     */
    @TableField("mem_idcard")
    private String memIdcard;

    /**
     * 党员联系电话
     */
    @TableField("mem_phone")
    private String memPhone;

    /**
     * 党员所在组织
     */
    @TableField("mem_org_code")
    private String memOrgCode;

    @TableField("mem_org_name")
    private String memOrgName;

    /**
     * 党员所在组织层级码
     */
    @TableField("mem_org_org_code")
    private String memOrgOrgCode;

    /**
     * 所在党支部联系电话
     */
    @TableField("mem_org_phone")
    private String memOrgPhone;

    /**
     * 党员工作岗位
     */
    @TableField("mem_d09_code")
    private String memD09Code;

    @TableField("mem_d09_name")
    private String memD09Name;

    /**
     * 是否省内外 省内 1 省外 0
     */
    @TableField("is_province")
    private String isProvince;

    /**
     * 是否跨节点（1 是 0 否）
     */
    @TableField("cross_node")
    private String crossNode;

    /**
     * 外出地点 	1 流向基层党（工）委	2 流向县（市、区、旗)	3 无固定地点	4 不掌握流向
     */
    @TableField("out_place_code")
    private String outPlaceCode;

    @TableField("out_place_name")
    private String outPlaceName;

    /**
     * 流动类型	1 跨省（区、市）流动	2 省(区、市)内跨市（地、州、盟)流动	3 市(地、州、盟)内跨县（(市、区、旗)流动
     */
    @TableField("flow_type_code")
    private String flowTypeCode;

    @TableField("flow_type_name")
    private String flowTypeName;

    /**
     * 流动原因	1 外出务工经商	2 外出居住	3 下岗失业	4 未就业的毕业学生	5 自主就业的退伍军人	9 其他
     */
    @TableField("flow_reason_code")
    private String flowReasonCode;

    @TableField("flow_reason_name")
    private String flowReasonName;

    /**
     * 外出日期
     */
    @TableField("out_time")
    private Date outTime;

    /**
     * 登记时间 | 重新登记时间
     */
    @TableField("register_time")
    private Date registerTime;

    /**
     * 流动党员活动证
     */
    @TableField("is_hold")
    private String isHold;

    /**
     * 流回时间
     */
    @TableField("flow_back_time")
    private Date flowBackTime;

    /**
     * 撤销时间
     */
    @TableField("cancel_time")
    private Date cancelTime;

    /**
     * 终止时间
     */
    @TableField("stop_time")
    private Date stopTime;

    /**
     * 1流出，2流入
     */
    @TableField("flow_out_in")
    private String flowOutIn;

    /**
     * 流动状态 1已流出（未纳入管理） 2已纳入支部管理 3流出被退回 4流出终止 5流动完成 6撤销流出
     */
    @TableField("flow_step")
    private String flowStep;

    /**
     * 接收时间
     */
    @TableField("in_receiving_time")
    private Date inReceivingTime;

    /**
     * 移至县级库时间
     */
    @TableField("move_to_county_time")
    private Date moveToCountyTime;

    /**
     * 是否移入县级库（1是）
     */
    @TableField("has_county_library")
    private String hasCountyLibrary;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 操作用户
     */
    @TableField("update_account")
    private String updateAccount;

    /**
     * 原工作岗位
     */
    @TableField("d09_code")
    private String d09Code;

    @TableField("d09_name")
    private String d09Name;

    /**
     * 新社会阶层类型
     */
    @TableField("d20_code")
    private String d20Code;

    @TableField("d20_name")
    private String d20Name;

    /**
     * 是否中介组织从业人员（0否，1是）
     */
    @TableField("has_inter")
    private String hasInter;

    /**
     * 单位类别
     */
    @TableField("d04_code")
    private String d04Code;

    @TableField("d04_name")
    private String d04Name;

    /**
     * 经济控制类型
     */
    @TableField("d16_code")
    private String d16Code;

    /**
     * 接受时--流入地党支部名称
     */
    @TableField("in_org_name")
    private String inOrgName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getFlowUqCode() {
        return flowUqCode;
    }

    public void setFlowUqCode(String flowUqCode) {
        this.flowUqCode = flowUqCode;
    }

    public String getMemCode() {
        return memCode;
    }

    public void setMemCode(String memCode) {
        this.memCode = memCode;
    }

    public String getMemName() {
        return memName;
    }

    public void setMemName(String memName) {
        this.memName = memName;
    }

    public String getMemSexCode() {
        return memSexCode;
    }

    public void setMemSexCode(String memSexCode) {
        this.memSexCode = memSexCode;
    }

    public String getMemSexName() {
        return memSexName;
    }

    public void setMemSexName(String memSexName) {
        this.memSexName = memSexName;
    }

    public String getMemIdcard() {
        return memIdcard;
    }

    public void setMemIdcard(String memIdcard) {
        this.memIdcard = memIdcard;
    }

    public String getMemPhone() {
        return memPhone;
    }

    public void setMemPhone(String memPhone) {
        this.memPhone = memPhone;
    }

    public String getMemOrgCode() {
        return memOrgCode;
    }

    public void setMemOrgCode(String memOrgCode) {
        this.memOrgCode = memOrgCode;
    }

    public String getMemOrgName() {
        return memOrgName;
    }

    public void setMemOrgName(String memOrgName) {
        this.memOrgName = memOrgName;
    }

    public String getMemOrgOrgCode() {
        return memOrgOrgCode;
    }

    public void setMemOrgOrgCode(String memOrgOrgCode) {
        this.memOrgOrgCode = memOrgOrgCode;
    }

    public String getMemOrgPhone() {
        return memOrgPhone;
    }

    public void setMemOrgPhone(String memOrgPhone) {
        this.memOrgPhone = memOrgPhone;
    }

    public String getMemD09Code() {
        return memD09Code;
    }

    public void setMemD09Code(String memD09Code) {
        this.memD09Code = memD09Code;
    }

    public String getMemD09Name() {
        return memD09Name;
    }

    public void setMemD09Name(String memD09Name) {
        this.memD09Name = memD09Name;
    }

    public String getIsProvince() {
        return isProvince;
    }

    public void setIsProvince(String isProvince) {
        this.isProvince = isProvince;
    }

    public String getCrossNode() {
        return crossNode;
    }

    public void setCrossNode(String crossNode) {
        this.crossNode = crossNode;
    }

    public String getOutPlaceCode() {
        return outPlaceCode;
    }

    public void setOutPlaceCode(String outPlaceCode) {
        this.outPlaceCode = outPlaceCode;
    }

    public String getOutPlaceName() {
        return outPlaceName;
    }

    public void setOutPlaceName(String outPlaceName) {
        this.outPlaceName = outPlaceName;
    }

    public String getFlowTypeCode() {
        return flowTypeCode;
    }

    public void setFlowTypeCode(String flowTypeCode) {
        this.flowTypeCode = flowTypeCode;
    }

    public String getFlowTypeName() {
        return flowTypeName;
    }

    public void setFlowTypeName(String flowTypeName) {
        this.flowTypeName = flowTypeName;
    }

    public String getFlowReasonCode() {
        return flowReasonCode;
    }

    public void setFlowReasonCode(String flowReasonCode) {
        this.flowReasonCode = flowReasonCode;
    }

    public String getFlowReasonName() {
        return flowReasonName;
    }

    public void setFlowReasonName(String flowReasonName) {
        this.flowReasonName = flowReasonName;
    }

    public Date getOutTime() {
        return outTime;
    }

    public void setOutTime(Date outTime) {
        this.outTime = outTime;
    }

    public Date getRegisterTime() {
        return registerTime;
    }

    public void setRegisterTime(Date registerTime) {
        this.registerTime = registerTime;
    }

    public String getIsHold() {
        return isHold;
    }

    public void setIsHold(String isHold) {
        this.isHold = isHold;
    }

    public Date getFlowBackTime() {
        return flowBackTime;
    }

    public void setFlowBackTime(Date flowBackTime) {
        this.flowBackTime = flowBackTime;
    }

    public Date getCancelTime() {
        return cancelTime;
    }

    public void setCancelTime(Date cancelTime) {
        this.cancelTime = cancelTime;
    }

    public Date getStopTime() {
        return stopTime;
    }

    public void setStopTime(Date stopTime) {
        this.stopTime = stopTime;
    }

    public String getFlowOutIn() {
        return flowOutIn;
    }

    public void setFlowOutIn(String flowOutIn) {
        this.flowOutIn = flowOutIn;
    }

    public String getFlowStep() {
        return flowStep;
    }

    public void setFlowStep(String flowStep) {
        this.flowStep = flowStep;
    }

    public Date getInReceivingTime() {
        return inReceivingTime;
    }

    public void setInReceivingTime(Date inReceivingTime) {
        this.inReceivingTime = inReceivingTime;
    }

    public Date getMoveToCountyTime() {
        return moveToCountyTime;
    }

    public void setMoveToCountyTime(Date moveToCountyTime) {
        this.moveToCountyTime = moveToCountyTime;
    }

    public String getHasCountyLibrary() {
        return hasCountyLibrary;
    }

    public void setHasCountyLibrary(String hasCountyLibrary) {
        this.hasCountyLibrary = hasCountyLibrary;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    public String getD09Code() {
        return d09Code;
    }

    public void setD09Code(String d09Code) {
        this.d09Code = d09Code;
    }

    public String getD09Name() {
        return d09Name;
    }

    public void setD09Name(String d09Name) {
        this.d09Name = d09Name;
    }

    public String getD20Code() {
        return d20Code;
    }

    public void setD20Code(String d20Code) {
        this.d20Code = d20Code;
    }

    public String getD20Name() {
        return d20Name;
    }

    public void setD20Name(String d20Name) {
        this.d20Name = d20Name;
    }

    public String getHasInter() {
        return hasInter;
    }

    public void setHasInter(String hasInter) {
        this.hasInter = hasInter;
    }

    public String getD04Code() {
        return d04Code;
    }

    public void setD04Code(String d04Code) {
        this.d04Code = d04Code;
    }

    public String getD04Name() {
        return d04Name;
    }

    public void setD04Name(String d04Name) {
        this.d04Name = d04Name;
    }


    public String getD16Code() {
        return d16Code;
    }

    public void setD16Code(String d16Code) {
        this.d16Code = d16Code;
    }

    public String getInOrgName() {
        return inOrgName;
    }

    public void setInOrgName(String inOrgName) {
        this.inOrgName = inOrgName;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
