package com.zenith.front.entity.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @author: D.watermelon
 * @date: 2022/4/18 0:23
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("transfer_org_temp")
public class TransferOrgTemp extends Model<TransferOrgTemp> {
    @TableId(value = "id", type = IdType.UUID)
    private String id;
    @TableField("jddm")
    private String jddm;
    @TableField("d01id")
    private String d01id;
    @TableField("d01uid")
    private String d01uid;
    @TableField("d01001")
    private String d01001;
    @TableField("d01002")
    private String d01002;
    @TableField("d01003")
    private String d01003;
    @TableField("d01004")
    private String d01004;
    @TableField("d01005")
    private String d01005;
    @TableField("d01006")
    private String d01006;
    @TableField("d01007")
    private String d01007;
    @TableField("d01008")
    private String d01008;
    @TableField("d01009")
    private String d01009;
    @TableField("op01")
    private String op01;
    @TableField("d01up1")
    private String d01up1;
    @TableField("d01up2")
    private String d01up2;
    @TableField("d01up3")
    private String d01up3;
    @TableField("updateTime")
    private Date updateTime;
}
