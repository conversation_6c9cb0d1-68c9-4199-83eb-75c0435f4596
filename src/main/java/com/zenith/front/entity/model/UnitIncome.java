package com.zenith.front.entity.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.zenith.front.annotation.DynamicTable;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 收入情况
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-10
 */
@DynamicTable
@TableName("ccp_unit_income")
public class UnitIncome extends Model<UnitIncome> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增长主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 唯一标识符code
     */
    @TableField("code")
    private String code;

    /**
     * 集体经济code
     */
    @TableField("economic_code")
    private String economicCode;

    /**
     * 收入情况
     */
    @TableField("income")
    private String income;

    /**
     * 收入金额（元）
     */
    @TableField("income_amount")
    private BigDecimal incomeAmount;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    @TableField("incur_debts_amount")
    private BigDecimal incurDebtsAmount;

    @TableField("outlay")
    private String outlay;

    @TableField("property")
    private String property;

    @TableField("ownership")
    private String ownership;

    @TableField("unit_code")
    private String unitCode;

    @TableField("has_secretary_economy")
    private Integer hasSecretaryEconomy;

    @TableField("collective_economic_liabilities")
    private BigDecimal collectiveEconomicLiabilities;
    /**
     * 是否获中央和省级财政扶持资金（1是 0否）
     */
    @TableField("has_financial_support")
    private Integer hasFinancialSupport;

    /**
     * 中央和省级财政扶持资金
     */
    @TableField("financial_support_enforced")
    private BigDecimal financialSupportEnforced;

    /**
     * 中央和省级财政扶持资金执行率
     */
    @TableField("enforced")
    private String enforced;

    /**
     * 已完工验收项目数字
     */
    @TableField("completed_acceptance_projects")
    private Integer completedAcceptanceProjects;

    /**
     * 已获得收益
     */
    @TableField("income_obtained")
    private BigDecimal incomeObtained;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEconomicCode() {
        return economicCode;
    }

    public void setEconomicCode(String economicCode) {
        this.economicCode = economicCode;
    }

    public String getIncome() {
        return income;
    }

    public void setIncome(String income) {
        this.income = income;
    }

    public BigDecimal getIncomeAmount() {
        return incomeAmount;
    }

    public void setIncomeAmount(BigDecimal incomeAmount) {
        this.incomeAmount = incomeAmount;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public BigDecimal getIncurDebtsAmount() {
        return incurDebtsAmount;
    }

    public void setIncurDebtsAmount(BigDecimal incurDebtsAmount) {
        this.incurDebtsAmount = incurDebtsAmount;
    }

    public String getOutlay() {
        return outlay;
    }

    public void setOutlay(String outlay) {
        this.outlay = outlay;
    }

    public String getProperty() {
        return property;
    }

    public void setProperty(String property) {
        this.property = property;
    }

    public String getOwnership() {
        return ownership;
    }

    public void setOwnership(String ownership) {
        this.ownership = ownership;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public Integer getHasSecretaryEconomy() {
        return hasSecretaryEconomy;
    }

    public void setHasSecretaryEconomy(Integer hasSecretaryEconomy) {
        this.hasSecretaryEconomy = hasSecretaryEconomy;
    }

    public BigDecimal getCollectiveEconomicLiabilities() {
        return collectiveEconomicLiabilities;
    }

    public void setCollectiveEconomicLiabilities(BigDecimal collectiveEconomicLiabilities) {
        this.collectiveEconomicLiabilities = collectiveEconomicLiabilities;
    }

    public Integer getHasFinancialSupport() {
        return hasFinancialSupport;
    }

    public void setHasFinancialSupport(Integer hasFinancialSupport) {
        this.hasFinancialSupport = hasFinancialSupport;
    }

    public BigDecimal getFinancialSupportEnforced() {
        return financialSupportEnforced;
    }

    public void setFinancialSupportEnforced(BigDecimal financialSupportEnforced) {
        this.financialSupportEnforced = financialSupportEnforced;
    }

    public String getEnforced() {
        return enforced;
    }

    public void setEnforced(String enforced) {
        this.enforced = enforced;
    }

    public Integer getCompletedAcceptanceProjects() {
        return completedAcceptanceProjects;
    }

    public void setCompletedAcceptanceProjects(Integer completedAcceptanceProjects) {
        this.completedAcceptanceProjects = completedAcceptanceProjects;
    }

    public BigDecimal getIncomeObtained() {
        return incomeObtained;
    }

    public void setIncomeObtained(BigDecimal incomeObtained) {
        this.incomeObtained = incomeObtained;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "UnitIncome{" +
                "id=" + id +
                ", code=" + code +
                ", economicCode=" + economicCode +
                ", income=" + income +
                ", incomeAmount=" + incomeAmount +
                ", deleteTime=" + deleteTime +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", remark=" + remark +
                ", incurDebtsAmount=" + incurDebtsAmount +
                ", outlay=" + outlay +
                ", property=" + property +
                ", ownership=" + ownership +
                ", unitCode=" + unitCode +
                ", hasSecretaryEconomy=" + hasSecretaryEconomy +
                ", collectiveEconomicLiabilities=" + collectiveEconomicLiabilities +
                "}";
    }
}
