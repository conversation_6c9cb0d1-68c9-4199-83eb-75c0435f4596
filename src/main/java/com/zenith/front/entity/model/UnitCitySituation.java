package com.zenith.front.entity.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.zenith.front.annotation.DynamicTable;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-10
 */
@DynamicTable
@TableName("ccp_unit_city_situation")
public class UnitCitySituation extends Model<UnitCitySituation> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "code", type = IdType.ASSIGN_UUID)
    private String code;

    @TableField("es_id")
    private String esId;

    @TableField("unit_code")
    private String unitCode;

    @TableField("org_code")
    private String orgCode;

    @TableField("has_complete_internal_institutions")
    private Integer hasCompleteInternalInstitutions;

    @TableField("has_achieve_team_law")
    private Integer hasAchieveTeamLaw;

    @TableField("has_draw_clear_power")
    private Integer hasDrawClearPower;

    @TableField("has_legally_endowed")
    private Integer hasLegallyEndowed;

    @TableField("has_cancel_task")
    private Integer hasCancelTask;

    @TableField("has_build_committee")
    private Integer hasBuildCommittee;

    @TableField("has_establish_system")
    private Integer hasEstablishSystem;

    @TableField("has_establish_work_system")
    private Integer hasEstablishWorkSystem;

    @TableField("has_grant_power")
    private Integer hasGrantPower;

    @TableField("d131_code")
    private String d131Code;

    @TableField("d131_name")
    private String d131Name;

    @TableField("community_service_organizations")
    private Integer communityServiceOrganizations;

    @TableField("community_volunteers")
    private Integer communityVolunteers;

    @TableField("resident_population_community")
    private Integer residentPopulationCommunity;

    @TableField("has_establish_service_center")
    private Integer hasEstablishServiceCenter;

    @TableField("community_service_stations")
    private Integer communityServiceStations;

    @TableField("has_implement_service_management")
    private Integer hasImplementServiceManagement;

    @TableField("has_build_smart_community")
    private Integer hasBuildSmartCommunity;

    @TableField("community_institutions_enterprises")
    private Integer communityInstitutionsEnterprises;

    @TableField("community_reporting_services")
    private Integer communityReportingServices;

    @TableField("non_public_community_organizations")
    private Integer nonPublicCommunityOrganizations;

    @TableField("non_public_organizations_services")
    private Integer nonPublicOrganizationsServices;

    @TableField("members_on_public_organizations")
    private Integer membersOnPublicOrganizations;

    @TableField("commercial_buildings")
    private Integer commercialBuildings;

    @TableField("organizations_commercial_buildings")
    private Integer organizationsCommercialBuildings;

    @TableField("commercial_buildings_workers")
    private Integer commercialBuildingsWorkers;

    @TableField("commercial_buildings_instructor")
    private Integer commercialBuildingsInstructor;

    @TableField("five_to_one_buildings")
    private Integer fiveToOneBuildings;

    @TableField("one_more_than_buildings")
    private Integer oneMoreThanBuildings;

    @TableField("grids")
    private Integer grids;

    @TableField("grid_members")
    private Integer gridMembers;

    @TableField("organization_party_grid")
    private Integer organizationPartyGrid;

    @TableField("has_complete_net")
    private Integer hasCompleteNet;

    @TableField("has_large_party_community")
    private Integer hasLargePartyCommunity;

    @TableField("has_meet_system_community")
    private Integer hasMeetSystemCommunity;

    @TableField("residential_areas")
    private Integer residentialAreas;

    @TableField("tube_plots")
    private Integer tubePlots;

    @TableField("organization_companies")
    private Integer organizationCompanies;

    @TableField("industry_authority_community")
    private Integer industryAuthorityCommunity;

    @TableField("industry_authority_organization")
    private Integer industryAuthorityOrganization;

    @TableField("three_parties_communities")
    private Integer threePartiesCommunities;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    @TableField("delete_time")
    private Date deleteTime;

    @TableField("commercial_buildings_party_organizations")
    private Integer commercialBuildingsPartyOrganizations;

    /**
     * 在居民住宅小区、楼院等划分的网格数
     */
    @TableField("jmzzlyWgs")
    private Integer jmzzlyWgs;

    /**
     * 在居民住宅小区、楼院等划分已建立党组织的网格数
     */
    @TableField("jmzzlyDzWgs")
    private Integer jmzzlyDzWgs;

    /**
     * 在商务楼宇、商圈市场等单独划定的网格数
     */
    @TableField("swsqWgs")
    private Integer swsqWgs;

    /**
     * 在商务楼宇、商圈市场等单独划定已建立党组织的网格数
     */
    @TableField("swsqDzWgs")
    private Integer swsqDzWgs;

    /**
     * 街道（乡镇）领导班子成员直接联系的网格数
     */
    @TableField("jdldWgs")
    private Integer jdldWgs;

    /**
     * 由社区工作者担任的专职网格员数
     */
    @TableField("zzWgys")
    private Integer zzWgys;

    /**
     * 全部专职网格员年工资总额（万元）
     */
    @TableField("zzNgzze")
    private Integer zzNgzze;

    /**
     * 配备兼职网格员数
     */
    @TableField("jzWgys")
    private Integer jzWgys;


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEsId() {
        return esId;
    }

    public void setEsId(String esId) {
        this.esId = esId;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Integer getHasCompleteInternalInstitutions() {
        return hasCompleteInternalInstitutions;
    }

    public void setHasCompleteInternalInstitutions(Integer hasCompleteInternalInstitutions) {
        this.hasCompleteInternalInstitutions = hasCompleteInternalInstitutions;
    }

    public Integer getHasAchieveTeamLaw() {
        return hasAchieveTeamLaw;
    }

    public void setHasAchieveTeamLaw(Integer hasAchieveTeamLaw) {
        this.hasAchieveTeamLaw = hasAchieveTeamLaw;
    }

    public Integer getHasDrawClearPower() {
        return hasDrawClearPower;
    }

    public void setHasDrawClearPower(Integer hasDrawClearPower) {
        this.hasDrawClearPower = hasDrawClearPower;
    }

    public Integer getHasLegallyEndowed() {
        return hasLegallyEndowed;
    }

    public void setHasLegallyEndowed(Integer hasLegallyEndowed) {
        this.hasLegallyEndowed = hasLegallyEndowed;
    }

    public Integer getHasCancelTask() {
        return hasCancelTask;
    }

    public void setHasCancelTask(Integer hasCancelTask) {
        this.hasCancelTask = hasCancelTask;
    }

    public Integer getHasBuildCommittee() {
        return hasBuildCommittee;
    }

    public void setHasBuildCommittee(Integer hasBuildCommittee) {
        this.hasBuildCommittee = hasBuildCommittee;
    }

    public Integer getHasEstablishSystem() {
        return hasEstablishSystem;
    }

    public void setHasEstablishSystem(Integer hasEstablishSystem) {
        this.hasEstablishSystem = hasEstablishSystem;
    }

    public Integer getHasEstablishWorkSystem() {
        return hasEstablishWorkSystem;
    }

    public void setHasEstablishWorkSystem(Integer hasEstablishWorkSystem) {
        this.hasEstablishWorkSystem = hasEstablishWorkSystem;
    }

    public Integer getHasGrantPower() {
        return hasGrantPower;
    }

    public void setHasGrantPower(Integer hasGrantPower) {
        this.hasGrantPower = hasGrantPower;
    }

    public String getd131Code() {
        return d131Code;
    }

    public void setd131Code(String d131Code) {
        this.d131Code = d131Code;
    }

    public String getd131Name() {
        return d131Name;
    }

    public void setd131Name(String d131Name) {
        this.d131Name = d131Name;
    }

    public Integer getCommunityServiceOrganizations() {
        return communityServiceOrganizations;
    }

    public void setCommunityServiceOrganizations(Integer communityServiceOrganizations) {
        this.communityServiceOrganizations = communityServiceOrganizations;
    }

    public Integer getCommunityVolunteers() {
        return communityVolunteers;
    }

    public void setCommunityVolunteers(Integer communityVolunteers) {
        this.communityVolunteers = communityVolunteers;
    }

    public Integer getResidentPopulationCommunity() {
        return residentPopulationCommunity;
    }

    public void setResidentPopulationCommunity(Integer residentPopulationCommunity) {
        this.residentPopulationCommunity = residentPopulationCommunity;
    }

    public Integer getHasEstablishServiceCenter() {
        return hasEstablishServiceCenter;
    }

    public void setHasEstablishServiceCenter(Integer hasEstablishServiceCenter) {
        this.hasEstablishServiceCenter = hasEstablishServiceCenter;
    }

    public Integer getCommunityServiceStations() {
        return communityServiceStations;
    }

    public void setCommunityServiceStations(Integer communityServiceStations) {
        this.communityServiceStations = communityServiceStations;
    }

    public Integer getHasImplementServiceManagement() {
        return hasImplementServiceManagement;
    }

    public void setHasImplementServiceManagement(Integer hasImplementServiceManagement) {
        this.hasImplementServiceManagement = hasImplementServiceManagement;
    }

    public Integer getHasBuildSmartCommunity() {
        return hasBuildSmartCommunity;
    }

    public void setHasBuildSmartCommunity(Integer hasBuildSmartCommunity) {
        this.hasBuildSmartCommunity = hasBuildSmartCommunity;
    }

    public Integer getCommunityInstitutionsEnterprises() {
        return communityInstitutionsEnterprises;
    }

    public void setCommunityInstitutionsEnterprises(Integer communityInstitutionsEnterprises) {
        this.communityInstitutionsEnterprises = communityInstitutionsEnterprises;
    }

    public Integer getCommunityReportingServices() {
        return communityReportingServices;
    }

    public void setCommunityReportingServices(Integer communityReportingServices) {
        this.communityReportingServices = communityReportingServices;
    }

    public Integer getNonPublicCommunityOrganizations() {
        return nonPublicCommunityOrganizations;
    }

    public void setNonPublicCommunityOrganizations(Integer nonPublicCommunityOrganizations) {
        this.nonPublicCommunityOrganizations = nonPublicCommunityOrganizations;
    }

    public Integer getNonPublicOrganizationsServices() {
        return nonPublicOrganizationsServices;
    }

    public void setNonPublicOrganizationsServices(Integer nonPublicOrganizationsServices) {
        this.nonPublicOrganizationsServices = nonPublicOrganizationsServices;
    }

    public Integer getMembersOnPublicOrganizations() {
        return membersOnPublicOrganizations;
    }

    public void setMembersOnPublicOrganizations(Integer membersOnPublicOrganizations) {
        this.membersOnPublicOrganizations = membersOnPublicOrganizations;
    }

    public Integer getCommercialBuildings() {
        return commercialBuildings;
    }

    public void setCommercialBuildings(Integer commercialBuildings) {
        this.commercialBuildings = commercialBuildings;
    }

    public Integer getOrganizationsCommercialBuildings() {
        return organizationsCommercialBuildings;
    }

    public void setOrganizationsCommercialBuildings(Integer organizationsCommercialBuildings) {
        this.organizationsCommercialBuildings = organizationsCommercialBuildings;
    }

    public Integer getCommercialBuildingsWorkers() {
        return commercialBuildingsWorkers;
    }

    public void setCommercialBuildingsWorkers(Integer commercialBuildingsWorkers) {
        this.commercialBuildingsWorkers = commercialBuildingsWorkers;
    }

    public Integer getCommercialBuildingsInstructor() {
        return commercialBuildingsInstructor;
    }

    public void setCommercialBuildingsInstructor(Integer commercialBuildingsInstructor) {
        this.commercialBuildingsInstructor = commercialBuildingsInstructor;
    }

    public Integer getFiveToOneBuildings() {
        return fiveToOneBuildings;
    }

    public void setFiveToOneBuildings(Integer fiveToOneBuildings) {
        this.fiveToOneBuildings = fiveToOneBuildings;
    }

    public Integer getOneMoreThanBuildings() {
        return oneMoreThanBuildings;
    }

    public void setOneMoreThanBuildings(Integer oneMoreThanBuildings) {
        this.oneMoreThanBuildings = oneMoreThanBuildings;
    }

    public Integer getGrids() {
        return grids;
    }

    public void setGrids(Integer grids) {
        this.grids = grids;
    }

    public Integer getGridMembers() {
        return gridMembers;
    }

    public void setGridMembers(Integer gridMembers) {
        this.gridMembers = gridMembers;
    }

    public Integer getOrganizationPartyGrid() {
        return organizationPartyGrid;
    }

    public void setOrganizationPartyGrid(Integer organizationPartyGrid) {
        this.organizationPartyGrid = organizationPartyGrid;
    }

    public Integer getHasCompleteNet() {
        return hasCompleteNet;
    }

    public void setHasCompleteNet(Integer hasCompleteNet) {
        this.hasCompleteNet = hasCompleteNet;
    }

    public Integer getHasLargePartyCommunity() {
        return hasLargePartyCommunity;
    }

    public void setHasLargePartyCommunity(Integer hasLargePartyCommunity) {
        this.hasLargePartyCommunity = hasLargePartyCommunity;
    }

    public Integer getHasMeetSystemCommunity() {
        return hasMeetSystemCommunity;
    }

    public void setHasMeetSystemCommunity(Integer hasMeetSystemCommunity) {
        this.hasMeetSystemCommunity = hasMeetSystemCommunity;
    }

    public Integer getResidentialAreas() {
        return residentialAreas;
    }

    public void setResidentialAreas(Integer residentialAreas) {
        this.residentialAreas = residentialAreas;
    }

    public Integer getTubePlots() {
        return tubePlots;
    }

    public void setTubePlots(Integer tubePlots) {
        this.tubePlots = tubePlots;
    }

    public Integer getOrganizationCompanies() {
        return organizationCompanies;
    }

    public void setOrganizationCompanies(Integer organizationCompanies) {
        this.organizationCompanies = organizationCompanies;
    }

    public Integer getIndustryAuthorityCommunity() {
        return industryAuthorityCommunity;
    }

    public void setIndustryAuthorityCommunity(Integer industryAuthorityCommunity) {
        this.industryAuthorityCommunity = industryAuthorityCommunity;
    }

    public Integer getIndustryAuthorityOrganization() {
        return industryAuthorityOrganization;
    }

    public void setIndustryAuthorityOrganization(Integer industryAuthorityOrganization) {
        this.industryAuthorityOrganization = industryAuthorityOrganization;
    }

    public Integer getThreePartiesCommunities() {
        return threePartiesCommunities;
    }

    public void setThreePartiesCommunities(Integer threePartiesCommunities) {
        this.threePartiesCommunities = threePartiesCommunities;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Integer getCommercialBuildingsPartyOrganizations() {
        return commercialBuildingsPartyOrganizations;
    }

    public void setCommercialBuildingsPartyOrganizations(Integer commercialBuildingsPartyOrganizations) {
        this.commercialBuildingsPartyOrganizations = commercialBuildingsPartyOrganizations;
    }

    public Integer getJmzzlyWgs() {
        return jmzzlyWgs;
    }

    public void setJmzzlyWgs(Integer jmzzlyWgs) {
        this.jmzzlyWgs = jmzzlyWgs;
    }

    public Integer getJmzzlyDzWgs() {
        return jmzzlyDzWgs;
    }

    public void setJmzzlyDzWgs(Integer jmzzlyDzWgs) {
        this.jmzzlyDzWgs = jmzzlyDzWgs;
    }

    public Integer getSwsqWgs() {
        return swsqWgs;
    }

    public void setSwsqWgs(Integer swsqWgs) {
        this.swsqWgs = swsqWgs;
    }

    public Integer getSwsqDzWgs() {
        return swsqDzWgs;
    }

    public void setSwsqDzWgs(Integer swsqDzWgs) {
        this.swsqDzWgs = swsqDzWgs;
    }

    public Integer getJdldWgs() {
        return jdldWgs;
    }

    public void setJdldWgs(Integer jdldWgs) {
        this.jdldWgs = jdldWgs;
    }

    public Integer getZzWgys() {
        return zzWgys;
    }

    public void setZzWgys(Integer zzWgys) {
        this.zzWgys = zzWgys;
    }

    public Integer getZzNgzze() {
        return zzNgzze;
    }

    public void setZzNgzze(Integer zzNgzze) {
        this.zzNgzze = zzNgzze;
    }

    public Integer getJzWgys() {
        return jzWgys;
    }

    public void setJzWgys(Integer jzWgys) {
        this.jzWgys = jzWgys;
    }

    @Override
    protected Serializable pkVal() {
        return this.code;
    }

    @Override
    public String toString() {
        return "UnitCitySituation{" +
                "code=" + code +
                ", esId=" + esId +
                ", unitCode=" + unitCode +
                ", orgCode=" + orgCode +
                ", hasCompleteInternalInstitutions=" + hasCompleteInternalInstitutions +
                ", hasAchieveTeamLaw=" + hasAchieveTeamLaw +
                ", hasDrawClearPower=" + hasDrawClearPower +
                ", hasLegallyEndowed=" + hasLegallyEndowed +
                ", hasCancelTask=" + hasCancelTask +
                ", hasBuildCommittee=" + hasBuildCommittee +
                ", hasEstablishSystem=" + hasEstablishSystem +
                ", hasEstablishWorkSystem=" + hasEstablishWorkSystem +
                ", hasGrantPower=" + hasGrantPower +
                ", d131Code=" + d131Code +
                ", d131Name=" + d131Name +
                ", communityServiceOrganizations=" + communityServiceOrganizations +
                ", communityVolunteers=" + communityVolunteers +
                ", residentPopulationCommunity=" + residentPopulationCommunity +
                ", hasEstablishServiceCenter=" + hasEstablishServiceCenter +
                ", communityServiceStations=" + communityServiceStations +
                ", hasImplementServiceManagement=" + hasImplementServiceManagement +
                ", hasBuildSmartCommunity=" + hasBuildSmartCommunity +
                ", communityInstitutionsEnterprises=" + communityInstitutionsEnterprises +
                ", communityReportingServices=" + communityReportingServices +
                ", nonPublicCommunityOrganizations=" + nonPublicCommunityOrganizations +
                ", nonPublicOrganizationsServices=" + nonPublicOrganizationsServices +
                ", membersOnPublicOrganizations=" + membersOnPublicOrganizations +
                ", commercialBuildings=" + commercialBuildings +
                ", organizationsCommercialBuildings=" + organizationsCommercialBuildings +
                ", commercialBuildingsWorkers=" + commercialBuildingsWorkers +
                ", commercialBuildingsInstructor=" + commercialBuildingsInstructor +
                ", fiveToOneBuildings=" + fiveToOneBuildings +
                ", oneMoreThanBuildings=" + oneMoreThanBuildings +
                ", grids=" + grids +
                ", gridMembers=" + gridMembers +
                ", organizationPartyGrid=" + organizationPartyGrid +
                ", hasCompleteNet=" + hasCompleteNet +
                ", hasLargePartyCommunity=" + hasLargePartyCommunity +
                ", hasMeetSystemCommunity=" + hasMeetSystemCommunity +
                ", residentialAreas=" + residentialAreas +
                ", tubePlots=" + tubePlots +
                ", organizationCompanies=" + organizationCompanies +
                ", industryAuthorityCommunity=" + industryAuthorityCommunity +
                ", industryAuthorityOrganization=" + industryAuthorityOrganization +
                ", threePartiesCommunities=" + threePartiesCommunities +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deleteTime=" + deleteTime +
                ", commercialBuildingsPartyOrganizations=" + commercialBuildingsPartyOrganizations +
                "}";
    }
}
