package com.zenith.front.entity.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-08
 */
@TableName("unit_exchange_area")
@Data
public class UnitExchangeArea extends Model<UnitExchangeArea> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("code")
    private String code;

    /**
     * 是否建立组织
     */
    @TableField("is_create_org")
    private Integer isCreateOrg;

    @TableField("name")
    private String name;

    /**
     * 单位类别_code
     */
    @TableField("d04_code")
    private String d04Code;

    /**
     * 党建指导组织_code
     */
    @TableField("manage_org_code")
    private String manageOrgCode;

    /**
     * 党建指导组织_name
     */
    @TableField("manage_org_name")
    private String manageOrgName;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    @TableField("delete_time")
    private Date deleteTime;
    /**
     * 主单位标示的组织code
     */
    @TableField("main_org_code")
    private String mainOrgCode;

    /**
     * 主单位标示的组织name
     */
    @TableField("main_org_name")
    private String mainOrgName;

    /**
     * 主单位标示的组织类别
     */
    @TableField("main_org_type")
    private String mainOrgType;

    /**
     * 主单位标示的组织大类
     */
    @TableField("main_org_type_code")
    private String mainOrgTypeCode;

    /**
     * 主单位标示的组织层级码
     */
    @TableField("main_unit_org_code")
    private String mainUnitOrgCode;

    /**
     * 国民经济行业CODE
     */
    @TableField("d194_code")
    private String d194Code;
    /**
     * 国民经济行业name
     */
    @TableField("d194_name")
    private String d194Name;

    /**
     * 生产性服务行业CODE
     */
    @TableField("d195_code")
    private String d195Code;
    /**
     * 生产性服务行业name
     */
    @TableField("d195_name")
    private String d195Name;


}
