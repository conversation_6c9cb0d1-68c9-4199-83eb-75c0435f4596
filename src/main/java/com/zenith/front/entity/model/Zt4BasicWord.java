package com.zenith.front.entity.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 城市基层党建工作有关情况
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-10
 */
@TableName("ccp_zt4_basic_word")
public class Zt4BasicWord extends Model<Zt4BasicWord> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
      @TableId(value = "code", type = IdType.ASSIGN_UUID)
    private String code;

    /**
     * 单位code
     */
    @TableField("unit_code")
    private String unitCode;

    /**
     * 赋予区域综合管理权、对上级部门派驻机构负责人的人事考核权等权力的街道
     */
    @TableField("has_power_streets")
    private Integer hasPowerStreets;

    /**
     * 取消招商引资等职能的街道
     */
    @TableField("has_cancel_functional_street")
    private Integer hasCancelFunctionalStreet;

    /**
     * 整合职能统筹设置党政内设工作机构的街道 
     */
    @TableField("has_integration_institution_street")
    private Integer hasIntegrationInstitutionStreet;

    /**
     * 组织委员纳入县级党委管理的街道
     */
    @TableField("has_managed_street")
    private Integer hasManagedStreet;

    /**
     * 由县级党委和政府统筹安排检查考核的街道
     */
    @TableField("has_assessment_street")
    private Integer hasAssessmentStreet;

    /**
     * 建立党群服务中心
     */
    @TableField("has_set_service_center")
    private Integer hasSetServiceCenter;

    /**
     * 实行与驻区单位党建联建共建
     */
    @TableField("has_implement_factory_build")
    private Integer hasImplementFactoryBuild;

    /**
     * 市、区、街道、社区均建立党建联席会议制度的城市
     */
    @TableField("has_establish_joint_system")
    private Integer hasEstablishJointSystem;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public Integer getHasPowerStreets() {
        return hasPowerStreets;
    }

    public void setHasPowerStreets(Integer hasPowerStreets) {
        this.hasPowerStreets = hasPowerStreets;
    }

    public Integer getHasCancelFunctionalStreet() {
        return hasCancelFunctionalStreet;
    }

    public void setHasCancelFunctionalStreet(Integer hasCancelFunctionalStreet) {
        this.hasCancelFunctionalStreet = hasCancelFunctionalStreet;
    }

    public Integer getHasIntegrationInstitutionStreet() {
        return hasIntegrationInstitutionStreet;
    }

    public void setHasIntegrationInstitutionStreet(Integer hasIntegrationInstitutionStreet) {
        this.hasIntegrationInstitutionStreet = hasIntegrationInstitutionStreet;
    }

    public Integer getHasManagedStreet() {
        return hasManagedStreet;
    }

    public void setHasManagedStreet(Integer hasManagedStreet) {
        this.hasManagedStreet = hasManagedStreet;
    }

    public Integer getHasAssessmentStreet() {
        return hasAssessmentStreet;
    }

    public void setHasAssessmentStreet(Integer hasAssessmentStreet) {
        this.hasAssessmentStreet = hasAssessmentStreet;
    }

    public Integer getHasSetServiceCenter() {
        return hasSetServiceCenter;
    }

    public void setHasSetServiceCenter(Integer hasSetServiceCenter) {
        this.hasSetServiceCenter = hasSetServiceCenter;
    }

    public Integer getHasImplementFactoryBuild() {
        return hasImplementFactoryBuild;
    }

    public void setHasImplementFactoryBuild(Integer hasImplementFactoryBuild) {
        this.hasImplementFactoryBuild = hasImplementFactoryBuild;
    }

    public Integer getHasEstablishJointSystem() {
        return hasEstablishJointSystem;
    }

    public void setHasEstablishJointSystem(Integer hasEstablishJointSystem) {
        this.hasEstablishJointSystem = hasEstablishJointSystem;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    @Override
    protected Serializable pkVal() {
        return this.code;
    }

    @Override
    public String toString() {
        return "Zt4BasicWord{" +
        "code=" + code +
        ", unitCode=" + unitCode +
        ", hasPowerStreets=" + hasPowerStreets +
        ", hasCancelFunctionalStreet=" + hasCancelFunctionalStreet +
        ", hasIntegrationInstitutionStreet=" + hasIntegrationInstitutionStreet +
        ", hasManagedStreet=" + hasManagedStreet +
        ", hasAssessmentStreet=" + hasAssessmentStreet +
        ", hasSetServiceCenter=" + hasSetServiceCenter +
        ", hasImplementFactoryBuild=" + hasImplementFactoryBuild +
        ", hasEstablishJointSystem=" + hasEstablishJointSystem +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        ", deleteTime=" + deleteTime +
        "}";
    }
}
