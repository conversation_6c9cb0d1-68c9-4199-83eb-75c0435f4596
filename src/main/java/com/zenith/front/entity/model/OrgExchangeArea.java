package com.zenith.front.entity.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("org_exchange_area")
public class OrgExchangeArea extends Model<OrgExchangeArea> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增长主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 组织唯一标识符
     */
    @TableField(exist = false)
    private String orgId;

    /**
     * 上级唯一标识符
     */
    @TableField("parent_code")
    private String parentCode;

    /**
     * 组织全称
     */
    @TableField("name")
    private String name;

    /**
     * 组织简称
     */
    @TableField("short_name")
    private String shortName;

    /**
     * 组织层级码
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 党组织类型code
     */
    @TableField("d01_code")
    private String d01Code;

    /**
     * 党组织大分类,1--党委,2--党总支,3--党支部,4--联合党支部,5--党组
     */
    @TableField("org_type")
    private Integer orgType;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 数据uuid
     */
    @TableField("code")
    private String code;

    /**
     * 上级机构的名称
     */
    @TableField("parent_name")
    private String parentName;


    /**
     * esId
     */
    @TableField("es_id")
    private String esId;

    /**
     * 组织zbcode
     */
    @TableField("zb_code")
    private String zbCode;

    /**
     * 组织拼音
     */
    @TableField("pinyin")
    private String pinyin;

    /**
     * 联系人
     */
    @TableField("contacter")
    private String contacter;

    /**
     * 联系电话
     */
    @TableField("contact_phone")
    private String contactPhone;

    /***
     * 交换区key
     * */
    @TableField("exchange_Key")
    private String exchangeKey;

    /***
     * 行政区划代码
     * */
    @TableField("administrative_division")
    private String administrativeDivision;

    /***
     * 是否具有预备党员审批权限
     * */
    @TableField("is_approval_mem")
    private Integer isApprovalMem;


    /**
     * 党组织编码
     */
    @TableField("D01001")
    private String d01001;

    /**
     * 流动党员对应的行政区划
     */
    @TableField("administrative_region")
    private String administrativeRegion;

}