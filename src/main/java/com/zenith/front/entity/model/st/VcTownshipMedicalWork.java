package com.zenith.front.entity.model.st;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* <p>
* 医疗工作 实体类
* </p>
*
* <AUTHOR>
* @date 2023-05-23 16:55:46
*/
@Data
@TableName("vc_township_medical_work")
@ApiModel("医疗工作")
public class VcTownshipMedicalWork implements Serializable {

    private static final long serialVersionUID = 7089804540508980388L;

    /**
    *主键
    */
    @ApiModelProperty(value = "主键")
    @TableId(value = "code",type = IdType.ASSIGN_UUID)
    private String code;
    /**
    *组织code
    */
    @ApiModelProperty(value = "组织code")
    @TableField("org_code")
    private String orgCode;
    /**
    *组织名称
    */
    @ApiModelProperty(value = "组织名称")
    @TableField("org_name")
    private String orgName;
    /**
    *组织层级
    */
    @ApiModelProperty(value = "组织层级")
    @TableField("org_level_code")
    private String orgLevelCode;
    /**
    *单位code
    */
    @ApiModelProperty(value = "单位code")
    @TableField("unit_code")
    private String unitCode;
    /**
    *单位名称
    */
    @ApiModelProperty(value = "单位名称")
    @TableField("unit_name")
    private String unitName;
    /**
    *建成科室名称（列举）
    */
    @ApiModelProperty(value = "建成科室名称（列举）")
    @TableField("department")
    private String department;
    /**
    *省级（个）
    */
    @ApiModelProperty(value = "省级（个）")
    @TableField("department_provincial")
    private String departmentProvincial;
    /**
    *市级（个）
    */
    @ApiModelProperty(value = "市级（个）")
    @TableField("department_municipal")
    private String departmentMunicipal;
    /**
    *正在创建科室名称（列举）
    */
    @ApiModelProperty(value = "正在创建科室名称（列举）")
    @TableField("creating_department")
    private String creatingDepartment;
    /**
    *省级（个）
    */
    @ApiModelProperty(value = "省级（个）")
    @TableField("creating_department_provincial")
    private String creatingDepartmentProvincial;
    /**
    *市级（个）
    */
    @ApiModelProperty(value = "市级（个）")
    @TableField("creating_department_municipal")
    private String creatingDepartmentMunicipal;
    /**
    *已建成中心数量（个）
    */
    @ApiModelProperty(value = "已建成中心数量（个）")
    @TableField("completed_center")
    private String completedCenter;
    /**
    *名称（列举）
    */
    @ApiModelProperty(value = "名称（列举）")
    @TableField("completed_center_name")
    private String completedCenterName;
    /**
    *2023年计划建成中心名称（列举
    */
    @ApiModelProperty(value = "2023年计划建成中心名称（列举")
    @TableField("twenty_three_planned_completion")
    private String twentyThreePlannedCompletion;
    /**
    *数量（个）
    */
    @ApiModelProperty(value = "数量（个）")
    @TableField("twenty_three_planned_completion_num")
    private String twentyThreePlannedCompletionNum;
    /**
    *2024年计划建成中心名称（列举
    */
    @ApiModelProperty(value = "2024年计划建成中心名称（列举")
    @TableField("twenty_four_planned_completion")
    private String twentyFourPlannedCompletion;
    /**
    *数量（个）
    */
    @ApiModelProperty(value = "数量（个）")
    @TableField("twenty_four_planned_completion_num")
    private String twentyFourPlannedCompletionNum;
    /**
    *2025年计划建成中心名称（列举
    */
    @ApiModelProperty(value = "2025年计划建成中心名称（列举")
    @TableField("twenty_five_planned_completion")
    private String twentyFivePlannedCompletion;
    /**
    *数量（个）
    */
    @ApiModelProperty(value = "数量（个）")
    @TableField("twenty_five_planned_completion_num")
    private String twentyFivePlannedCompletionNum;
    /**
    *2025年以后计划建成中心名称（列举
    */
    @ApiModelProperty(value = "2025年以后计划建成中心名称（列举")
    @TableField("twenty_five_after_planned_completion")
    private String twentyFiveAfterPlannedCompletion;
    /**
    *数量（个）
    */
    @ApiModelProperty(value = "数量（个）")
    @TableField("twenty_five_after_planned_completion_num")
    private String twentyFiveAfterPlannedCompletionNum;
    /**
    *帮扶期间开展新技术（项）（列举）
    */
    @ApiModelProperty(value = "帮扶期间开展新技术（项）（列举）")
    @TableField("developing_new_technologies")
    private String developingNewTechnologies;
    /**
    *帮扶期间引进新项目（项）（列举）
    */
    @ApiModelProperty(value = "帮扶期间引进新项目（项）（列举）")
    @TableField("introducing_new_projects")
    private String introducingNewProjects;
    /**
    *帮扶期间修订完善医院管理制度（项）
    */
    @ApiModelProperty(value = "帮扶期间修订完善医院管理制度（项）")
    @TableField("improve_management_system")
    private String improveManagementSystem;
    /**
    *制度名称
    */
    @ApiModelProperty(value = "制度名称")
    @TableField("system_name")
    private String systemName;
    /**
    *门急诊接诊患者（人次）
    */
    @ApiModelProperty(value = "门急诊接诊患者（人次）")
    @TableField("emergency_treatment")
    private String emergencyTreatment;
    /**
    *同比变化（%）
    */
    @ApiModelProperty(value = "同比变化（%）")
    @TableField("emergency_treatment_change")
    private String emergencyTreatmentChange;
    /**
    *接诊住院患者（人次）
    */
    @ApiModelProperty(value = "接诊住院患者（人次）")
    @TableField("be_hospital")
    private String beHospital;
    /**
    *同比变化（%）
    */
    @ApiModelProperty(value = "同比变化（%）")
    @TableField("be_hospital_change")
    private String beHospitalChange;
    /**
    *县域内就诊率
    */
    @ApiModelProperty(value = "县域内就诊率")
    @TableField("county_visiting_rate")
    private String countyVisitingRate;
    /**
    *同比变化（%）
    */
    @ApiModelProperty(value = "同比变化（%）")
    @TableField("county_visiting_rate_change")
    private String countyVisitingRateChange;
    /**
    *县域内住院人次占比
    */
    @ApiModelProperty(value = "县域内住院人次占比")
    @TableField("county_proportion_inpatients")
    private String countyProportionInpatients;
    /**
    *同比变化（%）
    */
    @ApiModelProperty(value = "同比变化（%）")
    @TableField("county_proportion_inpatients_change")
    private String countyProportionInpatientsChange;
    /**
    *健康管理中心是否建成
    */
    @ApiModelProperty(value = "健康管理中心是否建成")
    @TableField("has_health_management_center")
    private String hasHealthManagementCenter;
    /**
    *主要开展的业务包括（列举）
    */
    @ApiModelProperty(value = "主要开展的业务包括（列举）")
    @TableField("doing_business")
    private String doingBusiness;
    /**
    *具有专职医生（人）
    */
    @ApiModelProperty(value = "具有专职医生（人）")
    @TableField("professional_doctors")
    private String professionalDoctors;
    /**
    *其中专职副高及以上医师（人）
    */
    @ApiModelProperty(value = "其中专职副高及以上医师（人）")
    @TableField("deputy_senior_ranks_doctors")
    private String deputySeniorRanksDoctors;
    /**
    *专职护士（人）
    */
    @ApiModelProperty(value = "专职护士（人）")
    @TableField("professional_nurse")
    private String professionalNurse;
    /**
    *其中专职主管护师及以上（人）
    */
    @ApiModelProperty(value = "其中专职主管护师及以上（人）")
    @TableField("nurse_in_charge")
    private String nurseInCharge;
    /**
    *对健康管理中心发现的慢性病患者建档(%)
    */
    @ApiModelProperty(value = "对健康管理中心发现的慢性病患者建档(%)")
    @TableField("filing_of_chronic_disease_patients")
    private String filingOfChronicDiseasePatients;
    /**
    *开展健康教育基本理论与技能、行为危险因素等知识培训覆盖率（%）
    */
    @ApiModelProperty(value = "开展健康教育基本理论与技能、行为危险因素等知识培训覆盖率（%）")
    @TableField("knowledge_training_coverage_rate")
    private String knowledgeTrainingCoverageRate;
    /**
    *落实“一病两方”（患者就诊时提供临床治疗处方和健康教育处方）的科室（个）
    */
    @ApiModelProperty(value = "落实“一病两方”（患者就诊时提供临床治疗处方和健康教育处方）的科室（个）")
    @TableField("one_disease_two_prescriptions")
    private String oneDiseaseTwoPrescriptions;
    /**
    *提供健康教育印刷材料（种
    */
    @ApiModelProperty(value = "提供健康教育印刷材料（种")
    @TableField("health_education_printing")
    private String healthEducationPrinting;
    /**
    *制作健康教育音像资料（种）
    */
    @ApiModelProperty(value = "制作健康教育音像资料（种）")
    @TableField("health_education_audiovisual")
    private String healthEducationAudiovisual;
    /**
    *是否设置健康教育宣传栏
    */
    @ApiModelProperty(value = "是否设置健康教育宣传栏")
    @TableField("has_health_promotion_board")
    private String hasHealthPromotionBoard;
    /**
    *开展公众健康咨询活动（期）
    */
    @ApiModelProperty(value = "开展公众健康咨询活动（期）")
    @TableField("health_consultation_activities")
    private String healthConsultationActivities;
    /**
    *举办健康知识讲座（期）
    */
    @ApiModelProperty(value = "举办健康知识讲座（期）")
    @TableField("health_knowledge_lecture")
    private String healthKnowledgeLecture;
    /**
    *通过“市管县用”引进人数
    */
    @ApiModelProperty(value = "通过“市管县用”引进人数")
    @TableField("city_county_import")
    private String cityCountyImport;
    /**
    *通过“定向评价、定向使用”取得职称人数
    */
    @ApiModelProperty(value = "通过“定向评价、定向使用”取得职称人数")
    @TableField("obtain_title")
    private String obtainTitle;
    /**
    *通过调剂编制补充县医院的编制数
    */
    @ApiModelProperty(value = "通过调剂编制补充县医院的编制数")
    @TableField("coordination_staffing_hospital")
    private String coordinationStaffingHospital;
    /**
    *引进人才（人）
    */
    @ApiModelProperty(value = "引进人才（人）")
    @TableField("import_talent")
    private String importTalent;
    /**
    *硕士及以上（人）
    */
    @ApiModelProperty(value = "硕士及以上（人）")
    @TableField("import_talent_master")
    private String importTalentMaster;
    /**
    *本科（人）
    */
    @ApiModelProperty(value = "本科（人）")
    @TableField("import_talent_undergraduate")
    private String importTalentUndergraduate;
    /**
    *专科（人）
    */
    @ApiModelProperty(value = "专科（人）")
    @TableField("import_talent_junior_college_education")
    private String importTalentJuniorCollegeEducation;
    /**
    *医疗帮扶干部人才人
    */
    @ApiModelProperty(value = "医疗帮扶干部人才人")
    @TableField("medical_assistance_talent")
    private String medicalAssistanceTalent;
    /**
    *按1：3比例“师带徒”结对的    帮扶专家人数
    */
    @ApiModelProperty(value = "按1：3比例“师带徒”结对的    帮扶专家人数")
    @TableField("pairing_experts")
    private String pairingExperts;
    /**
    *“师带徒”跟学人数（人）
    */
    @ApiModelProperty(value = "“师带徒”跟学人数（人）")
    @TableField("follow_learning")
    private String followLearning;
    /**
    *通过考核评估跟学人员不合格人数
    */
    @ApiModelProperty(value = "通过考核评估跟学人员不合格人数")
    @TableField("follow_learning_unqualified")
    private String followLearningUnqualified;
    /**
    *帮扶医院接收学习、进修    人数（人）
    */
    @ApiModelProperty(value = "帮扶医院接收学习、进修    人数（人）")
    @TableField("learning_further_education")
    private String learningFurtherEducation;
    /**
    *通过跟踪考核不合格人数
    */
    @ApiModelProperty(value = "通过跟踪考核不合格人数")
    @TableField("track_unqualified")
    private String trackUnqualified;
    /**
    *公共卫生科（人）
    */
    @ApiModelProperty(value = "公共卫生科（人）")
    @TableField("public_health_department")
    private String publicHealthDepartment;
    /**
    *编制人员（人）
    */
    @ApiModelProperty(value = "编制人员（人）")
    @TableField("prepared_by")
    private String preparedBy;
    /**
    *开展学术交流（次）
    */
    @ApiModelProperty(value = "开展学术交流（次）")
    @TableField("academic_exchange")
    private String academicExchange;
    /**
    *专题讲座（次）
    */
    @ApiModelProperty(value = "专题讲座（次）")
    @TableField("special_lectures")
    private String specialLectures;
    /**
    *手术带教（次）
    */
    @ApiModelProperty(value = "手术带教（次）")
    @TableField("surgical_teaching")
    private String surgicalTeaching;
    /**
    *教学查房（次）
    */
    @ApiModelProperty(value = "教学查房（次）")
    @TableField("teaching_rounds")
    private String teachingRounds;
    /**
    *疑难病例讨论（次）
    */
    @ApiModelProperty(value = "疑难病例讨论（次）")
    @TableField("discussion_of_difficult_cases")
    private String discussionOfDifficultCases;
    /**
    *接受帮扶医院远程会诊（次）
    */
    @ApiModelProperty(value = "接受帮扶医院远程会诊（次）")
    @TableField("remote_consultation")
    private String remoteConsultation;
    /**
    *新进专业技术人员（人）
    */
    @ApiModelProperty(value = "新进专业技术人员（人）")
    @TableField("professional")
    private String professional;
    /**
    *其中编制（人）
    */
    @ApiModelProperty(value = "其中编制（人）")
    @TableField("professional_organization")
    private String professionalOrganization;
    /**
    *新晋职称（人）
    */
    @ApiModelProperty(value = "新晋职称（人）")
    @TableField("professional_title")
    private String professionalTitle;
    /**
    *中级（人）
    */
    @ApiModelProperty(value = "中级（人）")
    @TableField("intermediate")
    private String intermediate;
    /**
    *副高（人）
    */
    @ApiModelProperty(value = "副高（人）")
    @TableField("deputy_senior_ranks")
    private String deputySeniorRanks;
    /**
    *正高（人）
    */
    @ApiModelProperty(value = "正高（人）")
    @TableField("orthometric_height")
    private String orthometricHeight;
    /**
    *每季度开展群众对医院    满意度调查（%）
    */
    @ApiModelProperty(value = "每季度开展群众对医院    满意度调查（%）")
    @TableField("satisfaction_survey")
    private String satisfactionSurvey;
    /**
    *医疗服务改进措施（条）
    */
    @ApiModelProperty(value = "医疗服务改进措施（条）")
    @TableField("improvement_measures")
    private String improvementMeasures;
    /**
    *修订完善医院管理服务制度（项）
    */
    @ApiModelProperty(value = "修订完善医院管理服务制度（项）")
    @TableField("perfect_manage_service")
    private String perfectManageService;
    /**
    *东西部协作资金总额（万元）
    */
    @ApiModelProperty(value = "东西部协作资金总额（万元）")
    @TableField("east_west_collaboration")
    private String eastWestCollaboration;
    /**
    *医院获得（万元）
    */
    @ApiModelProperty(value = "医院获得（万元）")
    @TableField("hospitals_obtain")
    private String hospitalsObtain;
    /**
    *电子病历建设是否制定规划并印发
    */
    @ApiModelProperty(value = "电子病历建设是否制定规划并印发")
    @TableField("has_planning_issuance")
    private String hasPlanningIssuance;
    /**
    *医院信息建设资金投入（万元）
    */
    @ApiModelProperty(value = "医院信息建设资金投入（万元）")
    @TableField("construction_fund")
    private String constructionFund;
    /**
    *电子病历级别
    */
    @ApiModelProperty(value = "电子病历级别")
    @TableField("electronic_medical_record")
    private String electronicMedicalRecord;
    /**
    *医疗盈余率（%）
    */
    @ApiModelProperty(value = "医疗盈余率（%）")
    @TableField("medical_surplus_ratio")
    private String medicalSurplusRatio;
    /**
    *医务性收入占比（%）
    */
    @ApiModelProperty(value = "医务性收入占比（%）")
    @TableField("medical_income_proportion")
    private String medicalIncomeProportion;
    /**
    *管理费用占比（%）
    */
    @ApiModelProperty(value = "管理费用占比（%）")
    @TableField("overhead_proportion")
    private String overheadProportion;
    /**
    *人员费用占比（%）
    */
    @ApiModelProperty(value = "人员费用占比（%）")
    @TableField("personnel_costs_proportion")
    private String personnelCostsProportion;
    /**
    *医院资产总额（万元）
    */
    @ApiModelProperty(value = "医院资产总额（万元）")
    @TableField("hospital_assets")
    private String hospitalAssets;
    /**
    *负债总额（万元）
    */
    @ApiModelProperty(value = "负债总额（万元）")
    @TableField("liabilities")
    private String liabilities;
    /**
    *负债率
    */
    @ApiModelProperty(value = "负债率")
    @TableField("debt")
    private String debt;
    /**
    *医院是否建立完善符合实际的绩效考核办法和薪酬分配办法
    */
    @ApiModelProperty(value = "医院是否建立完善符合实际的绩效考核办法和薪酬分配办法")
    @TableField("has_salary_distribution")
    private String hasSalaryDistribution;
    /**
    *绩效总额（万元）
    */
    @ApiModelProperty(value = "绩效总额（万元）")
    @TableField("total_performance")
    private String totalPerformance;
    /**
    *人员薪酬占比（%）
    */
    @ApiModelProperty(value = "人员薪酬占比（%）")
    @TableField("personnel_compensation_proportion")
    private String personnelCompensationProportion;
    /**
    *是否执行院长年薪制
    */
    @ApiModelProperty(value = "是否执行院长年薪制")
    @TableField("has_annual_salary_system")
    private String hasAnnualSalarySystem;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableField("create_time")
    private Date createTime;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableField("update_time")
    private Date updateTime;
    /**
    *
    */
    @ApiModelProperty(value = "")
    @TableField("delete_time")
    @TableLogic(value = "null",delval = "now()")
    private Date deleteTime;
}
