package com.zenith.front.entity.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.zenith.front.annotation.DecryptField;
import com.zenith.front.annotation.DynamicTable;
import com.zenith.front.annotation.EnabledDecrypt;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-10
 */
@EnabledDecrypt
@DynamicTable
@TableName("ccp_unit_committee")
public class UnitCommittee extends Model<UnitCommittee> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("code")
    private String code;

    @TableField("es_id")
    private String esId;

    /**
     * 人员唯一标识
     */
    @TableField("mem_code")
    private String memCode;

    @DecryptField
    @TableField("mem_name")
    private String memName;

    /**
     * 班子成员职务代码  dict_d25
     */
    @TableField("d25_code")
    private String d25Code;

    /**
     * 班子成员职务代码  dict_d25
     */
    @TableField("d25_name")
    private String d25Name;

    /**
     * 批准的文号或文件
     */
    @TableField("file_number")
    private String fileNumber;

    /**
     * 任职开始时间
     */
    @TableField("start_date")
    private Date startDate;

    /**
     * 任职结束时间
     */
    @TableField("end_date")
    private Date endDate;

    /**
     * 是否在任
     */
    @TableField("is_incumbent")
    private Integer isIncumbent;

    /**
     * 兼任情况代码 dict_d26
     */
    @TableField("d26_code")
    private String d26Code;

    /**
     * 兼任情况代码 dict_d26
     */
    @TableField("d26_name")
    private String d26Name;

    /**
     * 人员类型情况（是否是党员）
     */
    @TableField("mem_type_code")
    private String memTypeCode;

    /**
     * 人员类型(党员，群众)
     */
    @TableField("mem_type_name")
    private String memTypeName;

    @DecryptField
    @TableField("mem_idcard")
    private String memIdcard;

    /**
     * 人员学历代码说明
     */
    @TableField("d07_code")
    private String d07Code;

    /**
     * 人员学历代码说明
     */
    @TableField("d07_name")
    private String d07Name;

    /**
     * 人员来源代码
     */
    @TableField("d0121_code")
    private String d0121Code;

    /**
     * 人员来源说明
     */
    @TableField("d0121_name")
    private String d0121Name;

    /**
     * 出生日期
     */
    @TableField("birthday")
    private Date birthday;

    /**
     * 性别代码
     */
    @TableField("sex_code")
    private String sexCode;

    /**
     * 性别代码说明
     */
    @TableField("sex_name")
    private String sexName;

    @TableField("unit_code")
    private String unitCode;

    @TableField("unit_name")
    private String unitName;

    /**
     * 人员名称,非党员情况下
     */
    @TableField("person_name")
    private String personName;

    /**
     * 行政职务说明
     */
    @TableField("remark")
    private String remark;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    @TableField("delete_time")
    private Date deleteTime;

    @TableField("timestamp")
    private Date timestamp;

    @TableField("is_history")
    private Integer isHistory;

    @TableField("update_account")
    private String updateAccount;

    /**
     * 届次code
     */
    @TableField("elect_code")
    private String electCode;
    /**
     * 是否村任职选调生（选择框、必填）(1 是 0 否)
     */
    @TableField("has_village_transfer_student")
    private String hasVillageTransferStudent;
    /**
     * 报酬（万元/年）（数字[保留两位小数]、必填）
     */
    @TableField("reward")
    private BigDecimal reward;

    /**
     * 选调单位层级 dict_d144
     */
    @TableField("d144_code")
    private String d144Code;

    @TableField("d144_name")
    private String d144Name;

    /**
     * 是否参加城镇职工养老保险
     */
    @TableField("endowment_insurance_for_urban_employees")
    private Integer endowmentInsuranceForUrbanEmployees;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 是否双一流大学生
     */
    @TableField("is_double_first")
    private Integer isDoubleFirst;

    /**
     * 政治面貌 dict_d89
     */
    @TableField("d89_code")
    private String d89Code;

    @TableField("d89_name")
    private String d89Name;

    /**
     * 任职结束时间  组织换届自动将时间设置成新届次创建时间
     */
    @TableField("emp_end_date")
    private Date empEndDate;

    public Date getEmpEndDate() {
        return empEndDate;
    }

    public void setEmpEndDate(Date empEndDate) {
        this.empEndDate = empEndDate;
    }

    public String getD89Code() {
        return d89Code;
    }

    public void setD89Code(String d89Code) {
        this.d89Code = d89Code;
    }

    public String getD89Name() {
        return d89Name;
    }

    public void setD89Name(String d89Name) {
        this.d89Name = d89Name;
    }

    public Integer getIsDoubleFirst() {
        return isDoubleFirst;
    }

    public void setIsDoubleFirst(Integer isDoubleFirst) {
        this.isDoubleFirst = isDoubleFirst;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEsId() {
        return esId;
    }

    public void setEsId(String esId) {
        this.esId = esId;
    }

    public String getMemCode() {
        return memCode;
    }

    public void setMemCode(String memCode) {
        this.memCode = memCode;
    }

    public String getMemName() {
        return memName;
    }

    public void setMemName(String memName) {
        this.memName = memName;
    }

    public String getD25Code() {
        return d25Code;
    }

    public void setD25Code(String d25Code) {
        this.d25Code = d25Code;
    }

    public String getD25Name() {
        return d25Name;
    }

    public void setD25Name(String d25Name) {
        this.d25Name = d25Name;
    }

    public String getFileNumber() {
        return fileNumber;
    }

    public void setFileNumber(String fileNumber) {
        this.fileNumber = fileNumber;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Integer getIsIncumbent() {
        return isIncumbent;
    }

    public void setIsIncumbent(Integer isIncumbent) {
        this.isIncumbent = isIncumbent;
    }

    public String getD26Code() {
        return d26Code;
    }

    public void setD26Code(String d26Code) {
        this.d26Code = d26Code;
    }

    public String getD26Name() {
        return d26Name;
    }

    public void setD26Name(String d26Name) {
        this.d26Name = d26Name;
    }

    public String getMemTypeCode() {
        return memTypeCode;
    }

    public void setMemTypeCode(String memTypeCode) {
        this.memTypeCode = memTypeCode;
    }

    public String getMemTypeName() {
        return memTypeName;
    }

    public void setMemTypeName(String memTypeName) {
        this.memTypeName = memTypeName;
    }

    public String getMemIdcard() {
        return memIdcard;
    }

    public void setMemIdcard(String memIdcard) {
        this.memIdcard = memIdcard;
    }

    public String getD07Code() {
        return d07Code;
    }

    public void setD07Code(String d07Code) {
        this.d07Code = d07Code;
    }

    public String getD07Name() {
        return d07Name;
    }

    public void setD07Name(String d07Name) {
        this.d07Name = d07Name;
    }

    public String getD0121Code() {
        return d0121Code;
    }

    public void setD0121Code(String d0121Code) {
        this.d0121Code = d0121Code;
    }

    public String getD0121Name() {
        return d0121Name;
    }

    public void setD0121Name(String d0121Name) {
        this.d0121Name = d0121Name;
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public String getSexCode() {
        return sexCode;
    }

    public void setSexCode(String sexCode) {
        this.sexCode = sexCode;
    }

    public String getSexName() {
        return sexName;
    }

    public void setSexName(String sexName) {
        this.sexName = sexName;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public Integer getIsHistory() {
        return isHistory;
    }

    public void setIsHistory(Integer isHistory) {
        this.isHistory = isHistory;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    public String getElectCode() {
        return electCode;
    }

    public void setElectCode(String electCode) {
        this.electCode = electCode;
    }

    public String getHasVillageTransferStudent() {
        return hasVillageTransferStudent;
    }

    public void setHasVillageTransferStudent(String hasVillageTransferStudent) {
        this.hasVillageTransferStudent = hasVillageTransferStudent;
    }

    public BigDecimal getReward() {
        return reward;
    }

    public void setReward(BigDecimal reward) {
        this.reward = reward;
    }

    public String getD144Code() {
        return d144Code;
    }

    public void setD144Code(String d144Code) {
        this.d144Code = d144Code;
    }

    public String getD144Name() {
        return d144Name;
    }

    public void setD144Name(String d144Name) {
        this.d144Name = d144Name;
    }

    public Integer getEndowmentInsuranceForUrbanEmployees() {
        return endowmentInsuranceForUrbanEmployees;
    }

    public void setEndowmentInsuranceForUrbanEmployees(Integer endowmentInsuranceForUrbanEmployees) {
        this.endowmentInsuranceForUrbanEmployees = endowmentInsuranceForUrbanEmployees;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "UnitCommittee{" +
                "id=" + id +
                ", code=" + code +
                ", esId=" + esId +
                ", memCode=" + memCode +
                ", memName=" + memName +
                ", d25Code=" + d25Code +
                ", d25Name=" + d25Name +
                ", fileNumber=" + fileNumber +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", isIncumbent=" + isIncumbent +
                ", d26Code=" + d26Code +
                ", d26Name=" + d26Name +
                ", memTypeCode=" + memTypeCode +
                ", memTypeName=" + memTypeName +
                ", memIdcard=" + memIdcard +
                ", d07Code=" + d07Code +
                ", d07Name=" + d07Name +
                ", birthday=" + birthday +
                ", sexCode=" + sexCode +
                ", sexName=" + sexName +
                ", unitCode=" + unitCode +
                ", unitName=" + unitName +
                ", personName=" + personName +
                ", remark=" + remark +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deleteTime=" + deleteTime +
                ", timestamp=" + timestamp +
                ", isHistory=" + isHistory +
                ", updateAccount=" + updateAccount +
                ", electCode=" + electCode +
                "}";
    }
}
