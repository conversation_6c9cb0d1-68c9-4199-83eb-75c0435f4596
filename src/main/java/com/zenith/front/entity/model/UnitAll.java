package com.zenith.front.entity.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.zenith.front.annotation.DynamicTable;
import com.zenith.front.config.JsonbTypeHandler;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-10
 */
@DynamicTable
@TableName("ccp_unit_all")
public class UnitAll extends Model<UnitAll> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    /**
     * 中中央一级机关党组数量
     */
    @TableField("unit_party_central")
    private String unitPartyCentral;

    /**
     * 省（区、市）一级机关党组
     */
    @TableField("unit_party_province")
    private String unitPartyProvince;

    /**
     * 市（地、州、盟）一级机关党组
     */
    @TableField("unit_party_city")
    private String unitPartyCity;

    /**
     * 县（市、区、旗）一级机关党组
     */
    @TableField("unit_party_county")
    private String unitPartyCounty;

    /**
     * 单位code
     */
    @TableField("code")
    private String code;

    @TableField("es_id")
    private String esId;

    /**
     * 单位代码
     */
    @TableField("credit_code")
    private String creditCode;

    /**
     * 创建单位的组织层级码
     */
    @TableField("create_unit_org_code")
    private String createUnitOrgCode;

    /**
     * 创建单位的组织code
     */
    @TableField("create_org_zb_code")
    private String createOrgZbCode;

    /**
     * 创建单位的组织code
     */
    @TableField("create_org_code")
    private String createOrgCode;

    /**
     * 单位名称
     */
    @TableField("name")
    private String name;

    @TableField("pinyin")
    private String pinyin;

    /**
     * 所在地区_code
     */
    @TableField("d48_code")
    private String d48Code;

    /**
     * 所在地区_name
     */
    @TableField("d48_name")
    private String d48Name;

    /**
     * 单位类别_code
     */
    @TableField("d04_code")
    private String d04Code;

    /**
     * 单位类别_name
     */
    @TableField("d04_name")
    private String d04Name;

    /**
     * 建立党员服务机构 1是，0否
     * 建立党群服务中心（城市社区乡镇社区用）
     */
    @TableField("is_org_service")
    private Integer isOrgService;

    /**
     * 建立党员志愿者队伍 1是，0否
     */
    @TableField("is_vol_team")
    private Integer isVolTeam;
    /**
     * 是否建立志愿者服务机构 1是，0否
     */
    @TableField("has_volunteer_organization")
    private Integer hasVolunteerOrganization;

    /**
     * 经济控制类型code
     */
    @TableField("d16_code")
    private String d16Code;

    /**
     * 经济类型name
     */
    @TableField("d16_name")
    private String d16Name;

    /**
     * 企业规模code
     */
    @TableField("d17_code")
    private String d17Code;

    /**
     * 企业规模name
     */
    @TableField("d17_name")
    private String d17Name;

    /**
     * 在岗职工数（人）
     */
    @TableField("on_post_num")
    private Integer onPostNum;

    /**
     * 是否配备专职党务工作人员  1是，0否
     */
    @TableField("has_party_work")
    private Integer hasPartyWork;

    /**
     * 配备专职副书记 1是，0否
     */
    @TableField("has_major_deputy_secretary")
    private Integer hasMajorDeputySecretary;

    /**
     * 法定代表人是否党员 1是，0否
     */
    @TableField("has_representative")
    private Integer hasRepresentative;

    /**
     * 是否建立工会或共青团组织  1是，0否
     */
    @TableField("has_union_organization")
    private Integer hasUnionOrganization;

    /**
     * 吸收未转入组织关系的党员建立党组织数
     */
    @TableField("absorbed_tissue_number")
    private Integer absorbedTissueNumber;

    /**
     * 未转组织关系党员数
     */
    @TableField("not_turned_party")
    private Integer notTurnedParty;

    /**
     * 党建工作指导员数
     */
    @TableField("\"bZT6_10\"")
    private Integer bzt610;

    /**
     * 在岗专业技术人员数（人）
     */
    @TableField("tec_num")
    private Integer tecNum;

    /**
     * 在岗专业技术人员（高级职称）（人）
     */
    @TableField("zaigang_gaoji")
    private Integer zaigangGaoji;
    /**
     * 专业技术人员数（表34，表35）
     */
    @TableField("technical_personnel")
    private Integer technicalPersonnel;
    /**
     * 党员中高级职称人员数（表34，表35）
     */
    @TableField("party_senior_title")
    private Integer partySeniorTitle;

    /**
     * 单位隶属关系_code
     */
    @TableField("d35_code")
    private String d35Code;

    /**
     * 单位隶属关系_name
     */
    @TableField("d35_name")
    private String d35Name;

    /**
     * 所长负责制情况code
     */
    @TableField("d112_code")
    private String d112Code;

    /**
     * 所长负责制情况name
     */
    @TableField("d112_name")
    private String d112Name;

    /**
     * 公益分类code
     */
    @TableField("d81_code")
    private String d81Code;

    /**
     * 公益分类name
     */
    @TableField("d81_name")
    private String d81Name;

    /**
     * 办院类型_code
     */
    @TableField("d111_code")
    private String d111Code;

    /**
     * 办院类型_name
     */
    @TableField("d111_name")
    private String d111Name;

    /**
     * 党政机关工作人员
     */
    @TableField("b30_a12")
    private Integer b30A12;

    /**
     * 是否党建工作指导员联系 1是，0否
     */
    @TableField("has_instructor_contact")
    private Integer hasInstructorContact;

    /**
     * 是否主要负责人担任党组织书记 1是，0否
     */
    @TableField("has_organization_secretary")
    private Integer hasOrganizationSecretary;

    /**
     * 从业人员数
     */
    @TableField("employees_number")
    private Integer employeesNumber;

    /**
     * 是否脱钩行业协会商会 1是，0否
     */
    @TableField("is_decoupl_industry")
    private Integer isDecouplIndustry;

    /**
     * 建 立 分 党 组 数字
     */
    @TableField("create_party_group")
    private Integer createPartyGroup;

    /**
     * 建立党组数字
     */
    @TableField("create_party_team")
    private Integer createPartyTeam;

    /**
     * 建立党组性质党委数字
     */
    @TableField("create_party_committee")
    private Integer createPartyCommittee;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 最新第一书记年份
     */
    @TableField("year")
    private Date year;

    /**
     * 今年选派第一书记（人）
     */
    @TableField("first_secretary_select")
    private String firstSecretarySelect;

    /**
     * 本年各级培训第一书记
     */
    @TableField("secretary_training_num")
    private Integer secretaryTrainingNum;

    /**
     * 是否为第一书记安排不低于1万元工作经费
     */
    @TableField("has_thousand")
    private Integer hasThousand;

    /**
     * 是否派出单位落实责任、项目、资金捆绑的
     */
    @TableField("has_bundled")
    private Integer hasBundled;

    /**
     * 提拔使用或晋级的第一书记数
     */
    @TableField("promoted_num")
    private Integer promotedNum;

    /**
     * 因工作不胜任召回调整的第一书记数
     */
    @TableField("adjusted_num")
    private Integer adjustedNum;

    /**
     * 运转经费
     */
    @TableField("operating_expenses")
    private BigDecimal operatingExpenses;

    /**
     * 每村办公经费
     */
    @TableField("village_per")
    private BigDecimal villagePer;

    /**
     * 党组织书记平均报酬
     */
    @TableField("secretary_salary")
    private BigDecimal secretarySalary;

    /**
     * 活动场所面积
     */
    @TableField("space_area")
    private Integer spaceArea;

    /**
     * 本年新建或改扩建活动场所数量
     */
    @TableField("new_expand_area")
    private Integer newExpandArea;

    /**
     * 专调2 村党组织书记中录用公务员数
     */
    @TableField("secretary_party_num")
    private Integer secretaryPartyNum;

    /**
     * 专调2 村党组织书记中录用事业编制工作人员数
     */
    @TableField("secretary_employ_sybz_num")
    private Integer secretaryEmploySybzNum;

    /**
     * 从村党组织书记中选拔乡镇领导干部人员数
     */
    @TableField("secretary_promoted_num")
    private Integer secretaryPromotedNum;

    /**
     * 社区纳入财政预算的工作经费总额
     */
    @TableField("community_money_num")
    private BigDecimal communityMoneyNum;

    /**
     * 社区全年服务群众专项经费总额
     */
    @TableField("community_serving_people")
    private BigDecimal communityServingPeople;

    /**
     * 是否开展在职党员到社区报到为群众服务
     */
    @TableField("community_masses")
    private Integer communityMasses;

    /**
     * 是否赋予区域综合管理权、对上级部门派驻机构负责人的人事考核权等权力 1是，0否
     */
    @TableField("has_examination_power")
    private Integer hasExaminationPower;

    /**
     * 是否取消招商引资等职能 1是，0否
     */
    @TableField("has_cancel_investment_promotion")
    private Integer hasCancelInvestmentPromotion;

    /**
     * 是否整合职能统筹设置党政内设工作机构 1是，0否
     */
    @TableField("has_work_mechanism")
    private Integer hasWorkMechanism;

    /**
     * 是否组织委员是否纳入上一级党委管理 1是，0否
     */
    @TableField("has_included_committee")
    private Integer hasIncludedCommittee;

    /**
     * 是否建立党群服务中心 1是，0否（城市街道用）
     */
    @TableField("has_group_service_center")
    private Integer hasGroupServiceCenter;

    /**
     * 是否实行与驻区单位党建联建共建 1是，0否
     */
    @TableField("has_party_build_endeavor")
    private Integer hasPartyBuildEndeavor;

    /**
     * 社区工作者中专职党务工作者数
     */
    @TableField("party_worker_num")
    private Integer partyWorkerNum;

    /**
     * 社区工作者中大专学历以上人员人数
     */
    @TableField("college_degree_num")
    private Integer collegeDegreeNum;

    /**
     * 社区工作者中从机关和街道选派的人数
     */
    @TableField("offices_and_streets")
    private Integer officesAndStreets;

    /**
     * 社区工作者中从退役军人中选聘的人数
     */
    @TableField("veterans")
    private Integer veterans;

    /**
     * 社区工作者中录用为公务员的人数
     */
    @TableField("civil_servants")
    private Integer civilServants;

    /**
     * 社区工作者中选拔进入事业编制的人数
     */
    @TableField("establishment")
    private Integer establishment;

    /**
     * 社区工作者中推荐为两代表一委人数
     */
    @TableField("two_and_one")
    private Integer twoAndOne;

    /**
     * 是否按不低于上年度当地全口径城镇单位就业人员平均工资水平确定报酬
     */
    @TableField("has_lower_social")
    private Integer hasLowerSocial;

    /**
     * 全部社区工作者年工资总额
     */
    @TableField("community_workers_salary")
    private BigDecimal communityWorkersSalary;

    /**
     * 社区党组织书记年工资总额
     */
    @TableField("community_secretary_salary")
    private BigDecimal communitySecretarySalary;

    /**
     * 党建工作指导员数
     */
    @TableField("community_building_number")
    private Integer communityBuildingNumber;

    /**
     * 是否建立社区工作者岗位等级序列
     */
    @TableField("has_community_positions")
    private Integer hasCommunityPositions;

    /**
     * 社区纳入财政预算的工作经费总额（万元）
     */
    @TableField("included_financial")
    private BigDecimal includedFinancial;

    /**
     * 社区全年服务群众专项经费总额（万元）
     */
    @TableField("special_funds_masses")
    private BigDecimal specialFundsMasses;

    /**
     * 是否开展在职党员到社区报到为群众服务 1是，0否
     */
    @TableField("has_community_report")
    private Integer hasCommunityReport;

    /**
     * 社区办公用房面积
     */
    @TableField("community_office_space")
    private Integer communityOfficeSpace;

    /**
     * 是否实行兼职委员制
     */
    @TableField("has_parttime_system")
    private Integer hasParttimeSystem;

    /**
     * 是否设立常委会 1是，0否
     */
    @TableField("has_standing_committee")
    private Integer hasStandingCommittee;

    /**
     * 办校类别_code
     */
    @TableField("d109_code")
    private String d109Code;

    /**
     * 办校类别代码_name
     */
    @TableField("d109_name")
    private String d109Name;

    /**
     * 是否向地方党委和主管部委专题报告党委领导下的校长负责制执行 1是 0否
     */
    @TableField("has_report_implementation")
    private Integer hasReportImplementation;

    /**
     * 是否修订党委全委会、常委会和校长办公会议事规则 1是 0否
     */
    @TableField("has_office_procedure")
    private Integer hasOfficeProcedure;

    /**
     * 学校党委书记是否向地方党委述职 1是，0否
     */
    @TableField("school_has_reports_local")
    private Integer schoolHasReportsLocal;

    /**
     * 是否组织开展二级院（系）党组织书记向学校党委述职  1是，0否
     */
    @TableField("has_secretary_university_committee")
    private Integer hasSecretaryUniversityCommittee;

    /**
     * 校长是否中共党员1是，0否  2 未配备
     */
    @TableField("has_president_party_member")
    private Integer hasPresidentPartyMember;

    /**
     * 校长是否担任党委副书记 1是 0否 2 未配备
     */
    @TableField("has_deputy_party_secretary")
    private Integer hasDeputyPartySecretary;

    /**
     * 纪委书记是否担任学校党委常委 1是，0否
     */
    @TableField("has_secretary_committee")
    private Integer hasSecretaryCommittee;

    /**
     * 组织部长是否担任学校党委常委 1是，0否
     */
    @TableField("has_tissue_committee")
    private Integer hasTissueCommittee;

    /**
     * 宣传部长是否担任学校党委常委 1是，0否
     */
    @TableField("has_propaganda_committee")
    private Integer hasPropagandaCommittee;

    /**
     * 统战部长是否担任学校党委常委 1是，0否
     */
    @TableField("has_front_committee")
    private Integer hasFrontCommittee;

    /**
     * 二级院（系）个数
     */
    @TableField("secondary_college")
    private Integer secondaryCollege;

    /**
     * 二级院（系）中建立党委的
     */
    @TableField("secondary_college_committee")
    private Integer secondaryCollegeCommittee;

    /**
     * 二级院（系）中建立总支部的
     */
    @TableField("secondary_college_always_branch")
    private Integer secondaryCollegeAlwaysBranch;

    /**
     * 二级院（系）中建立支部的
     */
    @TableField("secondary_college_branch")
    private Integer secondaryCollegeBranch;

    /**
     * 二级院（系）配备1名专职组织员的
     */
    @TableField("secondary_college_with_one")
    private Integer secondaryCollegeWithOne;

    /**
     * 二级院（系）配备2名及以上专职组织员的
     */
    @TableField("secondary_college_greater_two")
    private Integer secondaryCollegeGreaterTwo;

    /**
     * 2023-add-单位类别为【教育】时
     * 是否已实行党委领导下的院长负责制 1是，0否
     */
    @TableField("has_responsibility_system")
    private Integer hasResponsibilitySystem;

    /**
     * 2023-add-单位类别为【科研院所】时
     * 2023-add-单位类别为【教育】时
     * 领导体制已写入医院章程的 1是，0否
     */
    @TableField("ldtzyyzc")
    private Integer ldtzyyzc;

    /**
     * 是否党建工作要求写入医院章程 1是，0否
     */
    @TableField("is_party_work_write")
    private Integer isPartyWorkWrite;

    /**
     * 是否开展基层党建述职评议考核	 1是，0否
     */
    @TableField("is_open_org_assess")
    private Integer isOpenOrgAssess;

    /**
     * 2023-add-单位类别为【科研院所】时
     * 2023-add-单位类别为【教育】时
     * 是否党委书记、院长分设 1是，0否
     */
    @TableField("is_leader_separate")
    private Integer isLeaderSeparate;

    /**
     * 2023-add-单位类别为【科研院所】时
     * 2023-add-单位类别为【教育】时
     * 是否院长系中共党员 1是，0否
     */
    @TableField("leader_is_gcdy")
    private Integer leaderIsGcdy;

    /**
     * 2023-add-单位类别为【科研院所】时
     * 2023-add-单位类别为【教育】时
     * 是否院长担任党委副书记 1是，0否
     */
    @TableField("is_leader_deputy_secretary")
    private Integer isLeaderDeputySecretary;

    /**
     * 2023-add-单位类别为【科研院所】时
     * 医院内设机构党支部（个）[计算]
     */
    @TableField("is_set_org_party")
    private Integer isSetOrgParty;

    /**
     * 2023-add-单位类别为【科研院所】时
     * 党支部书记是内设机构负责人（个）[计算]
     */
    @TableField("secretary_is_inside_leader")
    private Integer secretaryIsInsideLeader;

    /**
     * 2023-add-单位类别为【科研院所】时
     * 党支部书记是“双带头人”的（个）[计算]
     */
    @TableField("sjsdtr")
    private Integer sjsdtr;

    /**
     * 医院本年度任届期满的内设机构党支部[计算]
     */
    @TableField("internal_institutions")
    private Integer internalInstitutions;

    /**
     * 医院本年度任届期满的内设机构党支部且本年换届[计算]
     */
    @TableField("internal_institutions_transition")
    private Integer internalInstitutionsTransition;

    /**
     * 医院本年度发展党员[计算]
     */
    @TableField("hospitals_develop")
    private Integer hospitalsDevelop;

    /**
     * 医院本年度发展卫生技术人员党员[计算]
     */
    @TableField("hospitals_develop_technology")
    private Integer hospitalsDevelopTechnology;

    /**
     * 医院本年度列为入党积极分子[计算]
     */
    @TableField("hospitals_active")
    private Integer hospitalsActive;

    /**
     * 国有经济企业是否建立董事会  1是，0否
     */
    @TableField("has_directors")
    private Integer hasDirectors;

    /**
     * 国有经济企业董事长是否担任书记 1是，0否
     */
    @TableField("has_chairman_secretary")
    private Integer hasChairmanSecretary;

    /**
     * 国有经济企业党建工作经费是否按上年度工资总额一定比例纳入企业管理费用 1是，0否
     */
    @TableField("has_proportionate_funding")
    private Integer hasProportionateFunding;

    /**
     * 国有经济企业人事管理和基层党建是否由一个部门抓 1是，0否
     */
    @TableField("has_branch_to_catch")
    private Integer hasBranchToCatch;

    /**
     * 国有经济企业人事管理和基层党建是否由一个领导管 1是，0否
     */
    @TableField("has_by_leader")
    private Integer hasByLeader;

    /**
     * 国有经济企业党务工作人员和经营管理人员是否同职级同待遇 1是，0否
     */
    @TableField("has_same_treatment")
    private Integer hasSameTreatment;

    /**
     * 国有经济企业是否上市公司 1是，0否
     */
    @TableField("has_public_company")
    private Integer hasPublicCompany;

    /**
     * 国有经济企业党建工作是否写入公司章程
     */
    @TableField("has_articles_incorporation")
    private Integer hasArticlesIncorporation;

    /**
     * 国有经济企业是否党组织研究讨论作为董事会、经理层决策重大问题前置程序
     */
    @TableField("has_prepositional_procedure")
    private Integer hasPrepositionalProcedure;

    /**
     * 国有经济企业董事长是否由上级企业有关负责人兼任
     */
    @TableField("has_responsible_person")
    private Integer hasResponsiblePerson;

    /**
     * 国有经济企业分支机构数
     */
    @TableField("branches")
    private Integer branches;

    /**
     * 国有经济企业 境外分支机构境外党员数
     */
    @TableField("party_members")
    private Integer partyMembers;

    /**
     * 是否依托组织部门成立的非公党工委 1是，0否
     */
    @TableField("has_non_public_party")
    private Integer hasNonPublicParty;

    /**
     * 是否设立专门办事机构的非公党工委 1是，0否
     */
    @TableField("has_special_agencies")
    private Integer hasSpecialAgencies;

    /**
     * 行业分类代码
     */
    @TableField("d114_code")
    private String d114Code;

    /**
     * 行业分类名称
     */
    @TableField("d114_name")
    private String d114Name;

    /**
     * 2023年统修改增加国名经济行业
     */
    @TableField("d194_code")
    private String d194Code;

    @TableField("d194_name")
    private String d194Name;


    /**
     * 2023年统修改增加生产服务行行业
     */
    @TableField("d195_code")
    private String d195Code;



    @TableField("d195_name")
    private String d195Name;



    /**
     * 未建立党组织情况--代码
     */
    @TableField("d05_code")
    private String d05Code;

    /**
     * 未建立党组织情况--名称
     */
    @TableField("d05_name")
    private String d05Name;

    /**
     * 法定代表人兼任党组织书记
     */
    @TableField("legal_is_secretary")
    private Integer legalIsSecretary;

    /**
     * 联合党支部数（已弃用，表25第7行不用该字段）
     */
    @TableField("party_branches_number")
    private Integer partyBranchesNumber;

    /**
     * 在岗职工中的党员数
     */
    @TableField("in_party_members")
    private Integer inPartyMembers;

    /**
     * 技术人员中的党员数
     */
    @TableField("technology_party_members")
    private Integer technologyPartyMembers;

    /**
     * 本科以上学历人数（数字）
     */
    @TableField("above_bk_education")
    private Integer aboveBkEducation;

    /**
     * 研究生以上学历人数（数字）
     */
    @TableField("above_yjs_education")
    private Integer aboveYjsEducation;

    /**
     * 是否由企业中高层管理人员担任党组织书记的 1是，0否（前端不再显示）
     */
    @TableField("has_secretary_high_level")
    private Integer hasSecretaryHighLevel;

    /**
     * 本年度发展党员（人）
     */
    @TableField("year_develop_mem")
    private Integer yearDevelopMem;

    /**
     * 主要负责人是否党员 1是，0否
     */
    @TableField("has_head_party")
    private Integer hasHeadParty;

    /**
     * 是否企业本级  1是，0否
     */
    @TableField("has_firm_level")
    private Integer hasFirmLevel;

    /**
     * 是否企业本级党组织书记（是1 否0） 前端不显示
     */
    @TableField("has_level_secretary")
    private Integer hasLevelSecretary;

    /**
     * 是否法人单位标识：1是，0否
     */
    @TableField("is_legal")
    private Integer isLegal;

    /**
     * 企业级别（国有经济控制）
     */
    @TableField("d115_code")
    private String d115Code;

    /**
     * 国有经济企业 境外分支机构基层党组织数量
     */
    @TableField("party_organization_num")
    private Integer partyOrganizationNum;

    /**
     * 国有经济企业 境外分支机构已建立党组织
     */
    @TableField("have_been_established")
    private Integer haveBeenEstablished;

    /**
     * 高校党支部（党组织关联高校时更新）
     */
    @TableField("college_party_branch")
    private Integer collegePartyBranch;

    /**
     * 教师党支部
     */
    @TableField("teacher_party_branch")
    private Integer teacherPartyBranch;

    /**
     * 教师党支部书记是否是“双带头人” 的
     */
    @TableField("has_teachers_double_leaders")
    private Integer hasTeachersDoubleLeaders;

    /**
     * 学生党支部
     */
    @TableField("student_party_branch")
    private Integer studentPartyBranch;

    /**
     * 机关党支部
     */
    @TableField("organ_party_branch")
    private Integer organPartyBranch;

    /**
     * 本年度院系党委书记参加培训人次(废弃)
     * 专调表八 本年度院系本级党组织书记参加培训人次
     */
    @TableField("year_training")
    private Integer yearTraining;

    /**
     * 本年度毕业生党员(废弃)
     * 专调表八 本年度毕业生党员
     */
    @TableField("graduate_party_member")
    private Integer graduatePartyMember;

    /**
     * 本年换届（个）
     */
    @TableField("is_year_org_change")
    private Integer isYearOrgChange;

    /**
     * 是否落实社区事务准入制度 1是，0否
     */
    @TableField("has_community_access")
    private Integer hasCommunityAccess;

    /**
     * 是否实行“四议两公开”工作法
     */
    @TableField("has_four_two_open_work")
    private Integer hasFourTwoOpenWork;

    /**
     * 是否成立村务监督委员会或其他村务监督机构
     */
    @TableField("has_community_supervisory")
    private Integer hasCommunitySupervisory;

    /**
     * 企业隶属关系
     */
    @TableField("d79_code")
    private String d79Code;

    /**
     * 所属层级
     */
    @TableField("d78_code")
    private String d78Code;

    /**
     * 医疗机构设立级别
     */
    @TableField("d77_code")
    private String d77Code;

    /**
     * 党建指导组织_层级码
     */
    @TableField("manage_unit_org_code")
    private String manageUnitOrgCode;

    /**
     * 是否建立组织
     */
    @TableField("is_create_org")
    private Integer isCreateOrg;

    /**
     * 主单位标示的组织层级码
     */
    @TableField("main_unit_org_code")
    private String mainUnitOrgCode;

    /**
     * 配备专职党务工作人员
     */
    @TableField("has_major_worker")
    private Integer hasMajorWorker;

    /**
     * 党建指导组织_code
     */
    @TableField("manage_org_code")
    private String manageOrgCode;

    /**
     * 党建指导组织_name
     */
    @TableField("manage_org_name")
    private String manageOrgName;

    /**
     * 法定代表人是否为党员
     */
    @TableField("legal_is_member")
    private Integer legalIsMember;

    /**
     * 是否是分支单位
     */
    @TableField("is_branch")
    private Integer isBranch;

    /**
     * 单位地址
     */
    @TableField("address")
    private String address;

    /**
     * 单位电话号码
     */
    @TableField("telephone")
    private String telephone;

    @TableField("ratio")
    private Double ratio;

    /**
     * 搜索关键字
     */
    @TableField("keywords")
    private String keywords;

    @TableField("unit_code")
    private String unitCode;

    @TableField("orgs")
    private String orgs;

    @TableField("timestamp")
    private Date timestamp;

    /**
     * 主单位标示的组织code
     */
    @TableField("main_org_code")
    private String mainOrgCode;

    /**
     * 主单位标示的组织name
     */
    @TableField("main_org_name")
    private String mainOrgName;

    /**
     * 主单位标示的组织类别
     */
    @TableField("main_org_type")
    private String mainOrgType;

    /**
     * 主单位标示的组织大类
     */
    @TableField("main_org_type_code")
    private String mainOrgTypeCode;

    /**
     * 是否是历史数据
     */
    @TableField("is_history")
    private String isHistory;

    /**
     * 最近更新账号
     */
    @TableField("update_account")
    private String updateAccount;

    /**
     * 医院等级代码
     */
    @TableField("d95_code")
    private String d95Code;

    /**
     * 医院等级名称
     */
    @TableField("d95_name")
    private String d95Name;

    /**
     * 是否党组
     */
    @TableField("has_party")
    private Integer hasParty;

    /**
     * 现任第一书记code
     */
    @TableField("first_secretary_code")
    private String firstSecretaryCode;

    /**
     * 现任第一书记名称
     */
    @TableField("first_secretary_name")
    private String firstSecretaryName;

    /**
     * 街道干部人数
     */
    @TableField("street_cadres")
    private Integer streetCadres;

    /**
     * 街道干部35岁及以下人数
     */
    @TableField("age35_below")
    private Integer age35Below;

    /**
     * 街道干部36至55岁人数
     */
    @TableField("age36_to_age55")
    private Integer age36ToAge55;

    /**
     * 街道干部56岁及以上人数
     */
    @TableField("age_56_above")
    private Integer age56Above;

    /**
     * 街道干部大专及以上学历人数
     */
    @TableField("college_degree_above")
    private Integer collegeDegreeAbove;

    /**
     * 街道干部高中中专及以下人数
     */
    @TableField("secondary_school_below")
    private Integer secondarySchoolBelow;

    /**
     * 街道干部公务员人数
     */
    @TableField("street_cadres_civil")
    private Integer streetCadresCivil;

    /**
     * 街道干部事业单位人数
     */
    @TableField("street_cadres_institutions")
    private Integer streetCadresInstitutions;

    /**
     * 街道干部其他身份人数
     */
    @TableField("cadre_other")
    private Integer cadreOther;

    /**
     * 年经营性收入5万元以下薄弱村空壳村（集体经济情况）（1是，0否）
     */
    @TableField("income_less_5w")
    private String incomeLess5w;

    /**
     * 年经营性收入50-100万元的村（1是，0否）
     */
    @TableField("income_50w_100w")
    private String income50w100w;

    /**
     * 年经营性收入100万元以上的村（1是，0否）
     */
    @TableField("income_above_100w")
    private String incomeAbove100w;

    /**
     * 有集体经济组织的村（1是，0否）
     */
    @TableField("has_collective_economy")
    private String hasCollectiveEconomy;

    /**
     * 村党组织书记担任村级集体经济组织负责人的村（1是，0否）
     */
    @TableField("has_economic_village")
    private String hasEconomicVillage;

    /**
     * 到社区报到的在职党员
     */
    @TableField("report_community_member")
    private Integer reportCommunityMember;


    /**
     * 全体在校学生中研究生人数
     */
    @TableField("graduate_student")
    private Integer graduateStudent;

    /**
     * 全体在校学生中大学本科生人数
     */
    @TableField("undergraduate_student")
    private Integer undergraduateStudent;

    /**
     * 全体在校学生中大学专科生人数
     */
    @TableField("junior_college_student")
    private Integer juniorCollegeStudent;

    /**
     * 全体在校学生中高中、中技人数
     */
    @TableField("middle_technical_students")
    private Integer middleTechnicalStudents;

    /**
     * 高等学校教师人数
     */
    @TableField("teachers_institutions_higher")
    private Integer teachersInstitutionsHigher;

    /**
     * 高等学校教师中女性人数
     */
    @TableField("teachers_higher_women")
    private Integer teachersHigherWomen;

    /**
     * 高等学校教师中35岁及以下人数
     */
    @TableField("teachers_age_thirty_five_below")
    private Integer teachersAgeThirtyFiveBelow;

    /**
     * 全体在校学生中中专生人数
     */
    @TableField("technical_secondary_student")
    private Integer technicalSecondaryStudent;
    /**
     * 年经营性收入
     */
    @TableField("year_amount")
    private BigDecimal yearAmount;
    /**
     * 2023-add-单位类别为【科研院所】时
     * 2023-add-单位类别为【教育】时
     * 党委书记是否兼任行政职务
     */
    @TableField("has_clerk_position")
    private Integer hasClerkPosition;
    /**
     * 二级院系配备专职组织员的总数
     */
    @TableField("secondary_college_with_one_two")
    private Integer secondaryCollegeWithOneTwo;
    /**
     * 有村党组织书记后备人选的行政村
     */
    @TableField("has_country_secretary_hbgb")
    private Integer hasCountrySecretaryHbgb;

    /**
     * 是否兼任企业党组书记 1是，0否
     * 单位类别是4 开头的， 经济类型控制是（3，4，5）开头的时候，是否兼任企业党组书记修改为法定代表人是否兼任党组织书记
     */
    @TableField("has_proper_secretary")
    private Integer hasProperSecretary;

    /**
     * 村（社区）工作者数量
     */
    @TableField("community_worker_count")
    private Integer communityWorkerCount;

    /**
     * 是否机关党组
     */
    @TableField("has_authority")
    private Integer hasAuthority;
    /**
     * 党组织类型code
     */
    @TableField("d01_code")
    private String d01Code;
    /**
     * 是否配备院长（0否，1是）
     */
    @TableField("is_allocate_dean")
    private Integer isAllocateDean;
    /**
     * 是否配备书记（0否，1是）
     */
    @TableField("is_allocate_secretary")
    private Integer isAllocateSecretary;

    /**
     * 锁定字段
     */
    @TableField(value = "lock_fields", typeHandler = JsonbTypeHandler.class)
    private Object lockFields;

    /**
     * 是否建立工会  1是，0否
     */
    @TableField("has_labour_union")
    private Integer hasLabourUnion;

    /**
     * 是否建立共青团组织  1是，0否
     */
    @TableField("has_youth_league")
    private Integer hasYouthLeague;

    /**
     * 是否建立妇联组织  1是，0否
     */
    @TableField("has_womens_federation")
    private Integer hasWomensFederation;

    /**
     * 村（社区）类别
     */
    @TableField("d155_code")
    private String d155Code;

    @TableField("d155_name")
    private String d155Name;
    /**
     * 是否已统一整合设置网格 0否，1是
     */
    @TableField("has_set_grid")
    private Integer hasSetGrid;
    /**
     * 是否有专职网格员纳入社区工作者管理 0否，1是
     */
    @TableField("has_included_grid_worker")
    private Integer hasIncludedGridWorker;

    /**
     * 是否党组织软弱涣散村（1是）
     */
    @TableField("has_org_slack_village")
    private String hasOrgSlackVillage;
    /**
     * 实行与驻区单位党建联建共建（1是）
     */
    @TableField("has_joint_units")
    private Integer hasJointUnits;

    /**
     * 农村专业技术协会数量
     */
    @TableField("rural_professional_technical_association_num")
    private Integer ruralProfessionalTechnicalAssociationNum;

    /**
     * 农民专业合作社数量
     */
    @TableField("farmer_specialized_cooperatives_num")
    private Integer farmerSpecializedCooperativesNum;

    /**
     * 家庭农场数量
     */
    @TableField("family_farm_num")
    private Integer familyFarmNum;

    /**
     * 专调表八 本年度高校党支部书记参加培训人次
     */
    @TableField("year_branch_training")
    private Integer yearBranchTraining;

    /**
     * 专调表八 尚未转出组织关系的
     */
    @TableField("org_relationship_not_transferred")
    private Integer orgRelationshipNotTransferred;
    /**
     * 党组织所在单位情况代码
     */
    @TableField("d02_code")
    private String d02Code;

    /**
     * 是否将党建工作经费纳入管理费列支、税前扣除(当单位类型为社会组织)
     */
    @TableField("has_working_expenses")
    private Integer hasWorkingExpenses;

    /**
     * 与业务主管单位关系(当单位类型为社会组织)
     */
    @TableField("d159_code")
    private String d159Code;

    /**
     * 与业务主管单位关系(当单位类型为社会组织)
     */
    @TableField("d159_name")
    private String d159Name;
    /**
     * 集体经济收入5万元以下薄弱村空壳村（1是）
     */
    @TableField("all_income_less_5w")
    private String allIncomeLess5w;
    /**
     * 集体经济情况收入情况
     */
    @TableField("year_amount_all")
    private BigDecimal yearAmountAll;
    /**
     * 本级企业是否省内企业
     */
    @TableField("has_industry_province")
    private Integer hasIndustryProvince;

    /**
     * 年经营收益5万元以下村（1是，0否）
     */
    @TableField("earnings_less_5w")
    private String earningsLess5w;

    /**
     * 年经营性收益50-100万元的村（1是，0否）
     */
    @TableField("earnings_50w_100w")
    private String earnings50w100w;

    /**
     * 年经营收益100万元以上的村（1是，0否）
     */
    @TableField("earnings_above_100w")
    private String earningsAbove100w;

    /**
     * 当年经营收益金额
     */
    @TableField("earnings_amount")
    private BigDecimal earningsAmount;


    // -----------专题十一 科研院所 相关统计项--------
    // -----------专题十二 中小学校 相关统计项--------

    /**
     * 2023-add-单位类别为【科研院所】时
     * 2023-add-单位类别为【教育】时
     *  已修订党组织会议、院（所）长办公会议议事规则的（1是0否）
     */
    @TableField("ysgz_is")
    private Integer ysgzIs;
    /**
     * 2023-add-单位类别为【科研院所】时
     * 2023-add-单位类别为【教育】时
     * 已建立学校党组织书记和校长定期沟通制度的 （1是0否）
     */
    @TableField("yjldqgt_is")
    private Integer yjldqgtIs;
    /**
     * 2023-add-单位类别为【科研院所】时
     * 2023-add-单位类别为【教育】时
     * 已设立党务工作机构的 （1是0否）
     */
    @TableField("ysldwgzjg_is")
    private Integer ysldwgzjgIs;
    /**
     * 2023-add-单位类别为【科研院所】时
     * 2023-add-单位类别为【教育】时
     *已配备专职党务工作人员的	（1是0否）
     */
    @TableField("ypbzzdwgzry_is")
    private Integer ypbzzdwgzryIs;

    /**
     * 是否建立履职事项清单 1-是 0-否
     */
    @TableField("has_performed_detail")
    private Integer hasPerformedDetail;

    /**
     * 国有经济控制的企业党建工作情况：境外分支机构有党员的机构
     */
    @TableField("exist_member_branches")
    private Integer existMemberBranches;

    /**
     * 国有经济控制的企业党建工作情况：境外分支机构有党员3人以上、未建立党组织的
     */
    @TableField("three_member_no_org_branches")
    private Integer threeMemberNoOrgBranches;

    /**
     * 划分网格数
     */
    @TableField("grids")
    private Integer grids;

    /**
     * 在居民住宅小区、楼院等划分的网格数
     */
    @TableField("jmzzlyWgs")
    private Integer jmzzlyWgs;

    /**
     * 在居民住宅小区、楼院等划分已建立党组织的网格数
     */
    @TableField("jmzzlyDzWgs")
    private Integer jmzzlyDzWgs;

    /**
     * 在商务楼宇、商圈市场等单独划定的网格数
     */
    @TableField("swsqWgs")
    private Integer swsqWgs;

    /**
     * 在商务楼宇、商圈市场等单独划定已建立党组织的网格数
     */
    @TableField("swsqDzWgs")
    private Integer swsqDzWgs;

    /**
     * 街道（乡镇）领导班子成员直接联系的网格数
     */
    @TableField("jdldWgs")
    private Integer jdldWgs;

    /**
     * 配备网格员数
     */
    @TableField("grid_members")
    private Integer gridMembers;

    /**
     * 由社区工作者担任的专职网格员数
     */
    @TableField("zzWgys")
    private Integer zzWgys;

    /**
     * 全部专职网格员年工资总额（万元）
     */
    @TableField("zzNgzze")
    private Integer zzNgzze;

    /**
     * 配备兼职网格员数
     */
    @TableField("jzWgys")
    private Integer jzWgys;

    /**
     * 居民小区数
     */
    @TableField("residential_areas")
    private Integer residentialAreas;

    /**
     * 物业管理的小区数
     */
    @TableField("tube_plots")
    private Integer tubePlots;

    /**
     * 成立党组织的物业企业数
     */
    @TableField("organization_companies")
    private Integer organizationCompanies;

    /**
     * 成立业委会的小区数
     */
    @TableField("industry_authority_community")
    private Integer industryAuthorityCommunity;

    /**
     * 成立业委会党组织数
     */
    @TableField("industry_authority_organization")
    private Integer industryAuthorityOrganization;

    /**
     * 建立社区党组织领导下，居委会、物业企业、业委会“三方共议”“双向进入”的小区数:
     */
    @TableField("three_parties_communities")
    private Integer threePartiesCommunities;

    /**
     * 有经济控制的企业境外分支共有员工数
     */
    @TableField("branch_employee")
    private Integer branchEmployee;
    /**
     * 有经济控制的企业境外分支其中国内派出员工
     */
    @TableField("branch_employee_home")
    private Integer branchEmployeeHome;
    /**
     * 境外分支机构已建立的党委数
     */
    @TableField("branch_committee")
    private Integer branchCommittee;
    /**
     * 境外分支机构已建立的总支部数
     */
    @TableField("branch_general")
    private Integer branchGeneral;
    /**
     * 境外分支机构已建立的支部数
     */
    @TableField("branch_node")
    private Integer branchNode;

    public Integer getBranchEmployee() {
        return branchEmployee;
    }

    public void setBranchEmployee(Integer branchEmployee) {
        this.branchEmployee = branchEmployee;
    }

    public Integer getBranchEmployeeHome() {
        return branchEmployeeHome;
    }

    public void setBranchEmployeeHome(Integer branchEmployeeHome) {
        this.branchEmployeeHome = branchEmployeeHome;
    }

    public Integer getBranchCommittee() {
        return branchCommittee;
    }

    public void setBranchCommittee(Integer branchCommittee) {
        this.branchCommittee = branchCommittee;
    }

    public Integer getBranchGeneral() {
        return branchGeneral;
    }

    public void setBranchGeneral(Integer branchGeneral) {
        this.branchGeneral = branchGeneral;
    }

    public Integer getBranchNode() {
        return branchNode;
    }

    public void setBranchNode(Integer branchNode) {
        this.branchNode = branchNode;
    }

    public Integer getYsgzIs() {
        return ysgzIs;
    }

    public void setYsgzIs(Integer ysgzIs) {
        this.ysgzIs = ysgzIs;
    }

    public Integer getYjldqgtIs() {
        return yjldqgtIs;
    }

    public void setYjldqgtIs(Integer yjldqgtIs) {
        this.yjldqgtIs = yjldqgtIs;
    }

    public Integer getYsldwgzjgIs() {
        return ysldwgzjgIs;
    }

    public void setYsldwgzjgIs(Integer ysldwgzjgIs) {
        this.ysldwgzjgIs = ysldwgzjgIs;
    }

    public Integer getYpbzzdwgzryIs() {
        return ypbzzdwgzryIs;
    }

    public void setYpbzzdwgzryIs(Integer ypbzzdwgzryIs) {
        this.ypbzzdwgzryIs = ypbzzdwgzryIs;
    }

    public BigDecimal getEarningsAmount() {
        return earningsAmount;
    }

    public void setEarningsAmount(BigDecimal earningsAmount) {
        this.earningsAmount = earningsAmount;
    }

    public String getEarningsLess5w() {
        return earningsLess5w;
    }

    public void setEarningsLess5w(String earningsLess5w) {
        this.earningsLess5w = earningsLess5w;
    }

    public String getEarnings50w100w() {
        return earnings50w100w;
    }

    public void setEarnings50w100w(String earnings50w100w) {
        this.earnings50w100w = earnings50w100w;
    }

    public String getEarningsAbove100w() {
        return earningsAbove100w;
    }

    public void setEarningsAbove100w(String earningsAbove100w) {
        this.earningsAbove100w = earningsAbove100w;
    }

    public String getD194Code() {return d194Code;}

    public void setD194Code(String d194Code) {this.d194Code = d194Code;}

    public String getD195Code() {return d195Code;}

    public void setD195Code(String d195Code) {this.d195Code = d195Code;}

    public String getD194Name() {return d194Name;}

    public void setD194Name(String d194Name) {this.d194Name = d194Name;}

    public String getD195Name() {return d195Name;}

    public void setD195Name(String d195Name) {this.d195Name = d195Name;
    }
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUnitPartyCentral() {
        return unitPartyCentral;
    }

    public void setUnitPartyCentral(String unitPartyCentral) {
        this.unitPartyCentral = unitPartyCentral;
    }

    public String getUnitPartyProvince() {
        return unitPartyProvince;
    }

    public void setUnitPartyProvince(String unitPartyProvince) {
        this.unitPartyProvince = unitPartyProvince;
    }

    public String getUnitPartyCity() {
        return unitPartyCity;
    }

    public void setUnitPartyCity(String unitPartyCity) {
        this.unitPartyCity = unitPartyCity;
    }

    public String getUnitPartyCounty() {
        return unitPartyCounty;
    }

    public void setUnitPartyCounty(String unitPartyCounty) {
        this.unitPartyCounty = unitPartyCounty;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEsId() {
        return esId;
    }

    public void setEsId(String esId) {
        this.esId = esId;
    }

    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getCreateUnitOrgCode() {
        return createUnitOrgCode;
    }

    public void setCreateUnitOrgCode(String createUnitOrgCode) {
        this.createUnitOrgCode = createUnitOrgCode;
    }

    public String getCreateOrgZbCode() {
        return createOrgZbCode;
    }

    public void setCreateOrgZbCode(String createOrgZbCode) {
        this.createOrgZbCode = createOrgZbCode;
    }

    public String getCreateOrgCode() {
        return createOrgCode;
    }

    public void setCreateOrgCode(String createOrgCode) {
        this.createOrgCode = createOrgCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPinyin() {
        return pinyin;
    }

    public void setPinyin(String pinyin) {
        this.pinyin = pinyin;
    }

    public String getD48Code() {
        return d48Code;
    }

    public void setD48Code(String d48Code) {
        this.d48Code = d48Code;
    }

    public String getD48Name() {
        return d48Name;
    }

    public void setD48Name(String d48Name) {
        this.d48Name = d48Name;
    }

    public String getD04Code() {
        return d04Code;
    }

    public void setD04Code(String d04Code) {
        this.d04Code = d04Code;
    }

    public String getD04Name() {
        return d04Name;
    }

    public void setD04Name(String d04Name) {
        this.d04Name = d04Name;
    }

    public Integer getIsOrgService() {
        return isOrgService;
    }

    public void setIsOrgService(Integer isOrgService) {
        this.isOrgService = isOrgService;
    }

    public Integer getIsVolTeam() {
        return isVolTeam;
    }

    public void setIsVolTeam(Integer isVolTeam) {
        this.isVolTeam = isVolTeam;
    }

    public Integer getHasVolunteerOrganization() {
        return hasVolunteerOrganization;
    }

    public void setHasVolunteerOrganization(Integer hasVolunteerOrganization) {
        this.hasVolunteerOrganization = hasVolunteerOrganization;
    }

    public String getD16Code() {
        return d16Code;
    }

    public void setD16Code(String d16Code) {
        this.d16Code = d16Code;
    }

    public String getD16Name() {
        return d16Name;
    }

    public void setD16Name(String d16Name) {
        this.d16Name = d16Name;
    }

    public String getD17Code() {
        return d17Code;
    }

    public void setD17Code(String d17Code) {
        this.d17Code = d17Code;
    }

    public String getD17Name() {
        return d17Name;
    }

    public void setD17Name(String d17Name) {
        this.d17Name = d17Name;
    }

    public Integer getOnPostNum() {
        return onPostNum;
    }

    public void setOnPostNum(Integer onPostNum) {
        this.onPostNum = onPostNum;
    }

    public Integer getHasPartyWork() {
        return hasPartyWork;
    }

    public void setHasPartyWork(Integer hasPartyWork) {
        this.hasPartyWork = hasPartyWork;
    }

    public Integer getHasMajorDeputySecretary() {
        return hasMajorDeputySecretary;
    }

    public void setHasMajorDeputySecretary(Integer hasMajorDeputySecretary) {
        this.hasMajorDeputySecretary = hasMajorDeputySecretary;
    }

    public Integer getHasRepresentative() {
        return hasRepresentative;
    }

    public void setHasRepresentative(Integer hasRepresentative) {
        this.hasRepresentative = hasRepresentative;
    }

    public Integer getHasUnionOrganization() {
        return hasUnionOrganization;
    }

    public void setHasUnionOrganization(Integer hasUnionOrganization) {
        this.hasUnionOrganization = hasUnionOrganization;
    }

    public Integer getAbsorbedTissueNumber() {
        return absorbedTissueNumber;
    }

    public void setAbsorbedTissueNumber(Integer absorbedTissueNumber) {
        this.absorbedTissueNumber = absorbedTissueNumber;
    }

    public Integer getNotTurnedParty() {
        return notTurnedParty;
    }

    public void setNotTurnedParty(Integer notTurnedParty) {
        this.notTurnedParty = notTurnedParty;
    }

    public Integer getBzt610() {
        return bzt610;
    }

    public void setBzt610(Integer bzt610) {
        this.bzt610 = bzt610;
    }

    public Integer getTecNum() {
        return tecNum;
    }

    public void setTecNum(Integer tecNum) {
        this.tecNum = tecNum;
    }

    public Integer getZaigangGaoji() {
        return zaigangGaoji;
    }

    public void setZaigangGaoji(Integer zaigangGaoji) {
        this.zaigangGaoji = zaigangGaoji;
    }

    public Integer getTechnicalPersonnel() {
        return technicalPersonnel;
    }

    public void setTechnicalPersonnel(Integer technicalPersonnel) {
        this.technicalPersonnel = technicalPersonnel;
    }

    public Integer getPartySeniorTitle() {
        return partySeniorTitle;
    }

    public void setPartySeniorTitle(Integer partySeniorTitle) {
        this.partySeniorTitle = partySeniorTitle;
    }

    public String getD35Code() {
        return d35Code;
    }

    public void setD35Code(String d35Code) {
        this.d35Code = d35Code;
    }

    public String getD35Name() {
        return d35Name;
    }

    public void setD35Name(String d35Name) {
        this.d35Name = d35Name;
    }

    public String getD112Code() {
        return d112Code;
    }

    public void setD112Code(String d112Code) {
        this.d112Code = d112Code;
    }

    public String getD112Name() {
        return d112Name;
    }

    public void setD112Name(String d112Name) {
        this.d112Name = d112Name;
    }

    public String getD81Code() {
        return d81Code;
    }

    public void setD81Code(String d81Code) {
        this.d81Code = d81Code;
    }

    public String getD81Name() {
        return d81Name;
    }

    public void setD81Name(String d81Name) {
        this.d81Name = d81Name;
    }

    public String getD111Code() {
        return d111Code;
    }

    public void setD111Code(String d111Code) {
        this.d111Code = d111Code;
    }

    public String getD111Name() {
        return d111Name;
    }

    public void setD111Name(String d111Name) {
        this.d111Name = d111Name;
    }

    public Integer getB30A12() {
        return b30A12;
    }

    public void setB30A12(Integer b30A12) {
        this.b30A12 = b30A12;
    }

    public Integer getHasInstructorContact() {
        return hasInstructorContact;
    }

    public void setHasInstructorContact(Integer hasInstructorContact) {
        this.hasInstructorContact = hasInstructorContact;
    }

    public Integer getHasOrganizationSecretary() {
        return hasOrganizationSecretary;
    }

    public void setHasOrganizationSecretary(Integer hasOrganizationSecretary) {
        this.hasOrganizationSecretary = hasOrganizationSecretary;
    }

    public Integer getEmployeesNumber() {
        return employeesNumber;
    }

    public void setEmployeesNumber(Integer employeesNumber) {
        this.employeesNumber = employeesNumber;
    }

    public Integer getIsDecouplIndustry() {
        return isDecouplIndustry;
    }

    public void setIsDecouplIndustry(Integer isDecouplIndustry) {
        this.isDecouplIndustry = isDecouplIndustry;
    }

    public Integer getCreatePartyGroup() {
        return createPartyGroup;
    }

    public void setCreatePartyGroup(Integer createPartyGroup) {
        this.createPartyGroup = createPartyGroup;
    }

    public Integer getCreatePartyTeam() {
        return createPartyTeam;
    }

    public void setCreatePartyTeam(Integer createPartyTeam) {
        this.createPartyTeam = createPartyTeam;
    }

    public Integer getCreatePartyCommittee() {
        return createPartyCommittee;
    }

    public void setCreatePartyCommittee(Integer createPartyCommittee) {
        this.createPartyCommittee = createPartyCommittee;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Date getYear() {
        return year;
    }

    public void setYear(Date year) {
        this.year = year;
    }

    public String getFirstSecretarySelect() {
        return firstSecretarySelect;
    }

    public void setFirstSecretarySelect(String firstSecretarySelect) {
        this.firstSecretarySelect = firstSecretarySelect;
    }

    public Integer getSecretaryTrainingNum() {
        return secretaryTrainingNum;
    }

    public void setSecretaryTrainingNum(Integer secretaryTrainingNum) {
        this.secretaryTrainingNum = secretaryTrainingNum;
    }

    public Integer getHasThousand() {
        return hasThousand;
    }

    public void setHasThousand(Integer hasThousand) {
        this.hasThousand = hasThousand;
    }

    public Integer getHasBundled() {
        return hasBundled;
    }

    public void setHasBundled(Integer hasBundled) {
        this.hasBundled = hasBundled;
    }

    public Integer getPromotedNum() {
        return promotedNum;
    }

    public void setPromotedNum(Integer promotedNum) {
        this.promotedNum = promotedNum;
    }

    public Integer getAdjustedNum() {
        return adjustedNum;
    }

    public void setAdjustedNum(Integer adjustedNum) {
        this.adjustedNum = adjustedNum;
    }

    public BigDecimal getOperatingExpenses() {
        return operatingExpenses;
    }

    public void setOperatingExpenses(BigDecimal operatingExpenses) {
        this.operatingExpenses = operatingExpenses;
    }

    public BigDecimal getVillagePer() {
        return villagePer;
    }

    public void setVillagePer(BigDecimal villagePer) {
        this.villagePer = villagePer;
    }

    public BigDecimal getSecretarySalary() {
        return secretarySalary;
    }

    public void setSecretarySalary(BigDecimal secretarySalary) {
        this.secretarySalary = secretarySalary;
    }

    public Integer getSpaceArea() {
        return spaceArea;
    }

    public void setSpaceArea(Integer spaceArea) {
        this.spaceArea = spaceArea;
    }

    public Integer getNewExpandArea() {
        return newExpandArea;
    }

    public void setNewExpandArea(Integer newExpandArea) {
        this.newExpandArea = newExpandArea;
    }

    public Integer getSecretaryPartyNum() {
        return secretaryPartyNum;
    }

    public void setSecretaryPartyNum(Integer secretaryPartyNum) {
        this.secretaryPartyNum = secretaryPartyNum;
    }

    public Integer getSecretaryEmploySybzNum() {
        return secretaryEmploySybzNum;
    }

    public void setSecretaryEmploySybzNum(Integer secretaryEmploySybzNum) {
        this.secretaryEmploySybzNum = secretaryEmploySybzNum;
    }

    public Integer getSecretaryPromotedNum() {
        return secretaryPromotedNum;
    }

    public void setSecretaryPromotedNum(Integer secretaryPromotedNum) {
        this.secretaryPromotedNum = secretaryPromotedNum;
    }

    public BigDecimal getCommunityMoneyNum() {
        return communityMoneyNum;
    }

    public void setCommunityMoneyNum(BigDecimal communityMoneyNum) {
        this.communityMoneyNum = communityMoneyNum;
    }

    public BigDecimal getCommunityServingPeople() {
        return communityServingPeople;
    }

    public void setCommunityServingPeople(BigDecimal communityServingPeople) {
        this.communityServingPeople = communityServingPeople;
    }

    public Integer getCommunityMasses() {
        return communityMasses;
    }

    public void setCommunityMasses(Integer communityMasses) {
        this.communityMasses = communityMasses;
    }

    public Integer getHasExaminationPower() {
        return hasExaminationPower;
    }

    public void setHasExaminationPower(Integer hasExaminationPower) {
        this.hasExaminationPower = hasExaminationPower;
    }

    public Integer getHasCancelInvestmentPromotion() {
        return hasCancelInvestmentPromotion;
    }

    public void setHasCancelInvestmentPromotion(Integer hasCancelInvestmentPromotion) {
        this.hasCancelInvestmentPromotion = hasCancelInvestmentPromotion;
    }

    public Integer getHasWorkMechanism() {
        return hasWorkMechanism;
    }

    public void setHasWorkMechanism(Integer hasWorkMechanism) {
        this.hasWorkMechanism = hasWorkMechanism;
    }

    public Integer getHasIncludedCommittee() {
        return hasIncludedCommittee;
    }

    public void setHasIncludedCommittee(Integer hasIncludedCommittee) {
        this.hasIncludedCommittee = hasIncludedCommittee;
    }

    public Integer getHasGroupServiceCenter() {
        return hasGroupServiceCenter;
    }

    public void setHasGroupServiceCenter(Integer hasGroupServiceCenter) {
        this.hasGroupServiceCenter = hasGroupServiceCenter;
    }

    public Integer getHasPartyBuildEndeavor() {
        return hasPartyBuildEndeavor;
    }

    public void setHasPartyBuildEndeavor(Integer hasPartyBuildEndeavor) {
        this.hasPartyBuildEndeavor = hasPartyBuildEndeavor;
    }

    public Integer getPartyWorkerNum() {
        return partyWorkerNum;
    }

    public void setPartyWorkerNum(Integer partyWorkerNum) {
        this.partyWorkerNum = partyWorkerNum;
    }

    public Integer getCollegeDegreeNum() {
        return collegeDegreeNum;
    }

    public void setCollegeDegreeNum(Integer collegeDegreeNum) {
        this.collegeDegreeNum = collegeDegreeNum;
    }

    public Integer getOfficesAndStreets() {
        return officesAndStreets;
    }

    public void setOfficesAndStreets(Integer officesAndStreets) {
        this.officesAndStreets = officesAndStreets;
    }

    public Integer getVeterans() {
        return veterans;
    }

    public void setVeterans(Integer veterans) {
        this.veterans = veterans;
    }

    public Integer getCivilServants() {
        return civilServants;
    }

    public void setCivilServants(Integer civilServants) {
        this.civilServants = civilServants;
    }

    public Integer getEstablishment() {
        return establishment;
    }

    public void setEstablishment(Integer establishment) {
        this.establishment = establishment;
    }

    public Integer getTwoAndOne() {
        return twoAndOne;
    }

    public void setTwoAndOne(Integer twoAndOne) {
        this.twoAndOne = twoAndOne;
    }

    public Integer getHasLowerSocial() {
        return hasLowerSocial;
    }

    public void setHasLowerSocial(Integer hasLowerSocial) {
        this.hasLowerSocial = hasLowerSocial;
    }

    public BigDecimal getCommunityWorkersSalary() {
        return communityWorkersSalary;
    }

    public void setCommunityWorkersSalary(BigDecimal communityWorkersSalary) {
        this.communityWorkersSalary = communityWorkersSalary;
    }

    public BigDecimal getCommunitySecretarySalary() {
        return communitySecretarySalary;
    }

    public void setCommunitySecretarySalary(BigDecimal communitySecretarySalary) {
        this.communitySecretarySalary = communitySecretarySalary;
    }

    public Integer getCommunityBuildingNumber() {
        return communityBuildingNumber;
    }

    public void setCommunityBuildingNumber(Integer communityBuildingNumber) {
        this.communityBuildingNumber = communityBuildingNumber;
    }

    public Integer getHasCommunityPositions() {
        return hasCommunityPositions;
    }

    public void setHasCommunityPositions(Integer hasCommunityPositions) {
        this.hasCommunityPositions = hasCommunityPositions;
    }

    public BigDecimal getIncludedFinancial() {
        return includedFinancial;
    }

    public void setIncludedFinancial(BigDecimal includedFinancial) {
        this.includedFinancial = includedFinancial;
    }

    public BigDecimal getSpecialFundsMasses() {
        return specialFundsMasses;
    }

    public void setSpecialFundsMasses(BigDecimal specialFundsMasses) {
        this.specialFundsMasses = specialFundsMasses;
    }

    public Integer getHasCommunityReport() {
        return hasCommunityReport;
    }

    public void setHasCommunityReport(Integer hasCommunityReport) {
        this.hasCommunityReport = hasCommunityReport;
    }

    public Integer getCommunityOfficeSpace() {
        return communityOfficeSpace;
    }

    public void setCommunityOfficeSpace(Integer communityOfficeSpace) {
        this.communityOfficeSpace = communityOfficeSpace;
    }

    public Integer getHasParttimeSystem() {
        return hasParttimeSystem;
    }

    public void setHasParttimeSystem(Integer hasParttimeSystem) {
        this.hasParttimeSystem = hasParttimeSystem;
    }

    public Integer getHasStandingCommittee() {
        return hasStandingCommittee;
    }

    public void setHasStandingCommittee(Integer hasStandingCommittee) {
        this.hasStandingCommittee = hasStandingCommittee;
    }

    public String getD109Code() {
        return d109Code;
    }

    public void setD109Code(String d109Code) {
        this.d109Code = d109Code;
    }

    public String getD109Name() {
        return d109Name;
    }

    public void setD109Name(String d109Name) {
        this.d109Name = d109Name;
    }

    public Integer getHasReportImplementation() {
        return hasReportImplementation;
    }

    public void setHasReportImplementation(Integer hasReportImplementation) {
        this.hasReportImplementation = hasReportImplementation;
    }

    public Integer getHasOfficeProcedure() {
        return hasOfficeProcedure;
    }

    public void setHasOfficeProcedure(Integer hasOfficeProcedure) {
        this.hasOfficeProcedure = hasOfficeProcedure;
    }

    public Integer getSchoolHasReportsLocal() {
        return schoolHasReportsLocal;
    }

    public void setSchoolHasReportsLocal(Integer schoolHasReportsLocal) {
        this.schoolHasReportsLocal = schoolHasReportsLocal;
    }

    public Integer getHasSecretaryUniversityCommittee() {
        return hasSecretaryUniversityCommittee;
    }

    public void setHasSecretaryUniversityCommittee(Integer hasSecretaryUniversityCommittee) {
        this.hasSecretaryUniversityCommittee = hasSecretaryUniversityCommittee;
    }

    public Integer getHasPresidentPartyMember() {
        return hasPresidentPartyMember;
    }

    public void setHasPresidentPartyMember(Integer hasPresidentPartyMember) {
        this.hasPresidentPartyMember = hasPresidentPartyMember;
    }

    public Integer getHasDeputyPartySecretary() {
        return hasDeputyPartySecretary;
    }

    public void setHasDeputyPartySecretary(Integer hasDeputyPartySecretary) {
        this.hasDeputyPartySecretary = hasDeputyPartySecretary;
    }

    public Integer getHasSecretaryCommittee() {
        return hasSecretaryCommittee;
    }

    public void setHasSecretaryCommittee(Integer hasSecretaryCommittee) {
        this.hasSecretaryCommittee = hasSecretaryCommittee;
    }

    public Integer getHasTissueCommittee() {
        return hasTissueCommittee;
    }

    public void setHasTissueCommittee(Integer hasTissueCommittee) {
        this.hasTissueCommittee = hasTissueCommittee;
    }

    public Integer getHasPropagandaCommittee() {
        return hasPropagandaCommittee;
    }

    public void setHasPropagandaCommittee(Integer hasPropagandaCommittee) {
        this.hasPropagandaCommittee = hasPropagandaCommittee;
    }

    public Integer getHasFrontCommittee() {
        return hasFrontCommittee;
    }

    public void setHasFrontCommittee(Integer hasFrontCommittee) {
        this.hasFrontCommittee = hasFrontCommittee;
    }

    public Integer getSecondaryCollege() {
        return secondaryCollege;
    }

    public void setSecondaryCollege(Integer secondaryCollege) {
        this.secondaryCollege = secondaryCollege;
    }

    public Integer getSecondaryCollegeCommittee() {
        return secondaryCollegeCommittee;
    }

    public void setSecondaryCollegeCommittee(Integer secondaryCollegeCommittee) {
        this.secondaryCollegeCommittee = secondaryCollegeCommittee;
    }

    public Integer getSecondaryCollegeAlwaysBranch() {
        return secondaryCollegeAlwaysBranch;
    }

    public void setSecondaryCollegeAlwaysBranch(Integer secondaryCollegeAlwaysBranch) {
        this.secondaryCollegeAlwaysBranch = secondaryCollegeAlwaysBranch;
    }

    public Integer getSecondaryCollegeBranch() {
        return secondaryCollegeBranch;
    }

    public void setSecondaryCollegeBranch(Integer secondaryCollegeBranch) {
        this.secondaryCollegeBranch = secondaryCollegeBranch;
    }

    public Integer getSecondaryCollegeWithOne() {
        return secondaryCollegeWithOne;
    }

    public void setSecondaryCollegeWithOne(Integer secondaryCollegeWithOne) {
        this.secondaryCollegeWithOne = secondaryCollegeWithOne;
    }

    public Integer getSecondaryCollegeGreaterTwo() {
        return secondaryCollegeGreaterTwo;
    }

    public void setSecondaryCollegeGreaterTwo(Integer secondaryCollegeGreaterTwo) {
        this.secondaryCollegeGreaterTwo = secondaryCollegeGreaterTwo;
    }

    public Integer getHasResponsibilitySystem() {
        return hasResponsibilitySystem;
    }

    public void setHasResponsibilitySystem(Integer hasResponsibilitySystem) {
        this.hasResponsibilitySystem = hasResponsibilitySystem;
    }

    public Integer getLdtzyyzc() {
        return ldtzyyzc;
    }

    public void setLdtzyyzc(Integer ldtzyyzc) {
        this.ldtzyyzc = ldtzyyzc;
    }

    public Integer getIsPartyWorkWrite() {
        return isPartyWorkWrite;
    }

    public void setIsPartyWorkWrite(Integer isPartyWorkWrite) {
        this.isPartyWorkWrite = isPartyWorkWrite;
    }

    public Integer getIsOpenOrgAssess() {
        return isOpenOrgAssess;
    }

    public void setIsOpenOrgAssess(Integer isOpenOrgAssess) {
        this.isOpenOrgAssess = isOpenOrgAssess;
    }

    public Integer getIsLeaderSeparate() {
        return isLeaderSeparate;
    }

    public void setIsLeaderSeparate(Integer isLeaderSeparate) {
        this.isLeaderSeparate = isLeaderSeparate;
    }

    public Integer getLeaderIsGcdy() {
        return leaderIsGcdy;
    }

    public void setLeaderIsGcdy(Integer leaderIsGcdy) {
        this.leaderIsGcdy = leaderIsGcdy;
    }

    public Integer getIsLeaderDeputySecretary() {
        return isLeaderDeputySecretary;
    }

    public void setIsLeaderDeputySecretary(Integer isLeaderDeputySecretary) {
        this.isLeaderDeputySecretary = isLeaderDeputySecretary;
    }

    public Integer getIsSetOrgParty() {
        return isSetOrgParty;
    }

    public void setIsSetOrgParty(Integer isSetOrgParty) {
        this.isSetOrgParty = isSetOrgParty;
    }

    public Integer getSecretaryIsInsideLeader() {
        return secretaryIsInsideLeader;
    }

    public void setSecretaryIsInsideLeader(Integer secretaryIsInsideLeader) {
        this.secretaryIsInsideLeader = secretaryIsInsideLeader;
    }

    public Integer getSjsdtr() {
        return sjsdtr;
    }

    public void setSjsdtr(Integer sjsdtr) {
        this.sjsdtr = sjsdtr;
    }

    public Integer getInternalInstitutions() {
        return internalInstitutions;
    }

    public void setInternalInstitutions(Integer internalInstitutions) {
        this.internalInstitutions = internalInstitutions;
    }

    public Integer getInternalInstitutionsTransition() {
        return internalInstitutionsTransition;
    }

    public void setInternalInstitutionsTransition(Integer internalInstitutionsTransition) {
        this.internalInstitutionsTransition = internalInstitutionsTransition;
    }

    public Integer getHospitalsDevelop() {
        return hospitalsDevelop;
    }

    public void setHospitalsDevelop(Integer hospitalsDevelop) {
        this.hospitalsDevelop = hospitalsDevelop;
    }

    public Integer getHospitalsDevelopTechnology() {
        return hospitalsDevelopTechnology;
    }

    public void setHospitalsDevelopTechnology(Integer hospitalsDevelopTechnology) {
        this.hospitalsDevelopTechnology = hospitalsDevelopTechnology;
    }

    public Integer getHospitalsActive() {
        return hospitalsActive;
    }

    public void setHospitalsActive(Integer hospitalsActive) {
        this.hospitalsActive = hospitalsActive;
    }

    public Integer getHasDirectors() {
        return hasDirectors;
    }

    public void setHasDirectors(Integer hasDirectors) {
        this.hasDirectors = hasDirectors;
    }

    public Integer getHasChairmanSecretary() {
        return hasChairmanSecretary;
    }

    public void setHasChairmanSecretary(Integer hasChairmanSecretary) {
        this.hasChairmanSecretary = hasChairmanSecretary;
    }

    public Integer getHasProportionateFunding() {
        return hasProportionateFunding;
    }

    public void setHasProportionateFunding(Integer hasProportionateFunding) {
        this.hasProportionateFunding = hasProportionateFunding;
    }

    public Integer getHasBranchToCatch() {
        return hasBranchToCatch;
    }

    public void setHasBranchToCatch(Integer hasBranchToCatch) {
        this.hasBranchToCatch = hasBranchToCatch;
    }

    public Integer getHasByLeader() {
        return hasByLeader;
    }

    public void setHasByLeader(Integer hasByLeader) {
        this.hasByLeader = hasByLeader;
    }

    public Integer getHasSameTreatment() {
        return hasSameTreatment;
    }

    public void setHasSameTreatment(Integer hasSameTreatment) {
        this.hasSameTreatment = hasSameTreatment;
    }

    public Integer getHasPublicCompany() {
        return hasPublicCompany;
    }

    public void setHasPublicCompany(Integer hasPublicCompany) {
        this.hasPublicCompany = hasPublicCompany;
    }

    public Integer getHasArticlesIncorporation() {
        return hasArticlesIncorporation;
    }

    public void setHasArticlesIncorporation(Integer hasArticlesIncorporation) {
        this.hasArticlesIncorporation = hasArticlesIncorporation;
    }

    public Integer getHasPrepositionalProcedure() {
        return hasPrepositionalProcedure;
    }

    public void setHasPrepositionalProcedure(Integer hasPrepositionalProcedure) {
        this.hasPrepositionalProcedure = hasPrepositionalProcedure;
    }

    public Integer getHasResponsiblePerson() {
        return hasResponsiblePerson;
    }

    public void setHasResponsiblePerson(Integer hasResponsiblePerson) {
        this.hasResponsiblePerson = hasResponsiblePerson;
    }

    public Integer getBranches() {
        return branches;
    }

    public void setBranches(Integer branches) {
        this.branches = branches;
    }

    public Integer getPartyMembers() {
        return partyMembers;
    }

    public void setPartyMembers(Integer partyMembers) {
        this.partyMembers = partyMembers;
    }

    public Integer getHasNonPublicParty() {
        return hasNonPublicParty;
    }

    public void setHasNonPublicParty(Integer hasNonPublicParty) {
        this.hasNonPublicParty = hasNonPublicParty;
    }

    public Integer getHasSpecialAgencies() {
        return hasSpecialAgencies;
    }

    public void setHasSpecialAgencies(Integer hasSpecialAgencies) {
        this.hasSpecialAgencies = hasSpecialAgencies;
    }

    public String getD114Code() {
        return d114Code;
    }

    public void setD114Code(String d114Code) {
        this.d114Code = d114Code;
    }

    public String getD114Name() {
        return d114Name;
    }

    public void setD114Name(String d114Name) {
        this.d114Name = d114Name;
    }

    public String getD05Code() {
        return d05Code;
    }

    public void setD05Code(String d05Code) {
        this.d05Code = d05Code;
    }

    public String getD05Name() {
        return d05Name;
    }

    public void setD05Name(String d05Name) {
        this.d05Name = d05Name;
    }

    public Integer getLegalIsSecretary() {
        return legalIsSecretary;
    }

    public void setLegalIsSecretary(Integer legalIsSecretary) {
        this.legalIsSecretary = legalIsSecretary;
    }

    public Integer getPartyBranchesNumber() {
        return partyBranchesNumber;
    }

    public void setPartyBranchesNumber(Integer partyBranchesNumber) {
        this.partyBranchesNumber = partyBranchesNumber;
    }

    public Integer getInPartyMembers() {
        return inPartyMembers;
    }

    public void setInPartyMembers(Integer inPartyMembers) {
        this.inPartyMembers = inPartyMembers;
    }

    public Integer getTechnologyPartyMembers() {
        return technologyPartyMembers;
    }

    public void setTechnologyPartyMembers(Integer technologyPartyMembers) {
        this.technologyPartyMembers = technologyPartyMembers;
    }

    public Integer getAboveBkEducation() {
        return aboveBkEducation;
    }

    public void setAboveBkEducation(Integer aboveBkEducation) {
        this.aboveBkEducation = aboveBkEducation;
    }

    public Integer getAboveYjsEducation() {
        return aboveYjsEducation;
    }

    public void setAboveYjsEducation(Integer aboveYjsEducation) {
        this.aboveYjsEducation = aboveYjsEducation;
    }

    public Integer getHasSecretaryHighLevel() {
        return hasSecretaryHighLevel;
    }

    public void setHasSecretaryHighLevel(Integer hasSecretaryHighLevel) {
        this.hasSecretaryHighLevel = hasSecretaryHighLevel;
    }

    public Integer getYearDevelopMem() {
        return yearDevelopMem;
    }

    public void setYearDevelopMem(Integer yearDevelopMem) {
        this.yearDevelopMem = yearDevelopMem;
    }

    public Integer getHasHeadParty() {
        return hasHeadParty;
    }

    public void setHasHeadParty(Integer hasHeadParty) {
        this.hasHeadParty = hasHeadParty;
    }

    public Integer getHasFirmLevel() {
        return hasFirmLevel;
    }

    public void setHasFirmLevel(Integer hasFirmLevel) {
        this.hasFirmLevel = hasFirmLevel;
    }

    public Integer getHasLevelSecretary() {
        return hasLevelSecretary;
    }

    public void setHasLevelSecretary(Integer hasLevelSecretary) {
        this.hasLevelSecretary = hasLevelSecretary;
    }

    public Integer getIsLegal() {
        return isLegal;
    }

    public void setIsLegal(Integer isLegal) {
        this.isLegal = isLegal;
    }

    public String getD115Code() {
        return d115Code;
    }

    public void setD115Code(String d115Code) {
        this.d115Code = d115Code;
    }

    public Integer getPartyOrganizationNum() {
        return partyOrganizationNum;
    }

    public void setPartyOrganizationNum(Integer partyOrganizationNum) {
        this.partyOrganizationNum = partyOrganizationNum;
    }

    public Integer getHaveBeenEstablished() {
        return haveBeenEstablished;
    }

    public void setHaveBeenEstablished(Integer haveBeenEstablished) {
        this.haveBeenEstablished = haveBeenEstablished;
    }

    public Integer getCollegePartyBranch() {
        return collegePartyBranch;
    }

    public void setCollegePartyBranch(Integer collegePartyBranch) {
        this.collegePartyBranch = collegePartyBranch;
    }

    public Integer getTeacherPartyBranch() {
        return teacherPartyBranch;
    }

    public void setTeacherPartyBranch(Integer teacherPartyBranch) {
        this.teacherPartyBranch = teacherPartyBranch;
    }

    public Integer getHasTeachersDoubleLeaders() {
        return hasTeachersDoubleLeaders;
    }

    public void setHasTeachersDoubleLeaders(Integer hasTeachersDoubleLeaders) {
        this.hasTeachersDoubleLeaders = hasTeachersDoubleLeaders;
    }

    public Integer getStudentPartyBranch() {
        return studentPartyBranch;
    }

    public void setStudentPartyBranch(Integer studentPartyBranch) {
        this.studentPartyBranch = studentPartyBranch;
    }

    public Integer getOrganPartyBranch() {
        return organPartyBranch;
    }

    public void setOrganPartyBranch(Integer organPartyBranch) {
        this.organPartyBranch = organPartyBranch;
    }

    public Integer getYearTraining() {
        return yearTraining;
    }

    public void setYearTraining(Integer yearTraining) {
        this.yearTraining = yearTraining;
    }

    public Integer getGraduatePartyMember() {
        return graduatePartyMember;
    }

    public void setGraduatePartyMember(Integer graduatePartyMember) {
        this.graduatePartyMember = graduatePartyMember;
    }

    public Integer getIsYearOrgChange() {
        return isYearOrgChange;
    }

    public void setIsYearOrgChange(Integer isYearOrgChange) {
        this.isYearOrgChange = isYearOrgChange;
    }

    public Integer getHasCommunityAccess() {
        return hasCommunityAccess;
    }

    public void setHasCommunityAccess(Integer hasCommunityAccess) {
        this.hasCommunityAccess = hasCommunityAccess;
    }

    public Integer getHasFourTwoOpenWork() {
        return hasFourTwoOpenWork;
    }

    public void setHasFourTwoOpenWork(Integer hasFourTwoOpenWork) {
        this.hasFourTwoOpenWork = hasFourTwoOpenWork;
    }

    public Integer getHasCommunitySupervisory() {
        return hasCommunitySupervisory;
    }

    public void setHasCommunitySupervisory(Integer hasCommunitySupervisory) {
        this.hasCommunitySupervisory = hasCommunitySupervisory;
    }

    public String getD79Code() {
        return d79Code;
    }

    public void setD79Code(String d79Code) {
        this.d79Code = d79Code;
    }

    public String getD78Code() {
        return d78Code;
    }

    public void setD78Code(String d78Code) {
        this.d78Code = d78Code;
    }

    public String getD77Code() {
        return d77Code;
    }

    public void setD77Code(String d77Code) {
        this.d77Code = d77Code;
    }

    public String getManageUnitOrgCode() {
        return manageUnitOrgCode;
    }

    public void setManageUnitOrgCode(String manageUnitOrgCode) {
        this.manageUnitOrgCode = manageUnitOrgCode;
    }

    public Integer getIsCreateOrg() {
        return isCreateOrg;
    }

    public void setIsCreateOrg(Integer isCreateOrg) {
        this.isCreateOrg = isCreateOrg;
    }

    public String getMainUnitOrgCode() {
        return mainUnitOrgCode;
    }

    public void setMainUnitOrgCode(String mainUnitOrgCode) {
        this.mainUnitOrgCode = mainUnitOrgCode;
    }

    public Integer getHasMajorWorker() {
        return hasMajorWorker;
    }

    public void setHasMajorWorker(Integer hasMajorWorker) {
        this.hasMajorWorker = hasMajorWorker;
    }

    public String getManageOrgCode() {
        return manageOrgCode;
    }

    public void setManageOrgCode(String manageOrgCode) {
        this.manageOrgCode = manageOrgCode;
    }

    public String getManageOrgName() {
        return manageOrgName;
    }

    public void setManageOrgName(String manageOrgName) {
        this.manageOrgName = manageOrgName;
    }

    public Integer getLegalIsMember() {
        return legalIsMember;
    }

    public void setLegalIsMember(Integer legalIsMember) {
        this.legalIsMember = legalIsMember;
    }

    public Integer getIsBranch() {
        return isBranch;
    }

    public void setIsBranch(Integer isBranch) {
        this.isBranch = isBranch;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public Double getRatio() {
        return ratio;
    }

    public void setRatio(Double ratio) {
        this.ratio = ratio;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getOrgs() {
        return orgs;
    }

    public void setOrgs(String orgs) {
        this.orgs = orgs;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public String getMainOrgCode() {
        return mainOrgCode;
    }

    public void setMainOrgCode(String mainOrgCode) {
        this.mainOrgCode = mainOrgCode;
    }

    public String getMainOrgName() {
        return mainOrgName;
    }

    public void setMainOrgName(String mainOrgName) {
        this.mainOrgName = mainOrgName;
    }

    public String getMainOrgType() {
        return mainOrgType;
    }

    public void setMainOrgType(String mainOrgType) {
        this.mainOrgType = mainOrgType;
    }

    public String getMainOrgTypeCode() {
        return mainOrgTypeCode;
    }

    public void setMainOrgTypeCode(String mainOrgTypeCode) {
        this.mainOrgTypeCode = mainOrgTypeCode;
    }

    public String getIsHistory() {
        return isHistory;
    }

    public void setIsHistory(String isHistory) {
        this.isHistory = isHistory;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    public String getD95Code() {
        return d95Code;
    }

    public void setD95Code(String d95Code) {
        this.d95Code = d95Code;
    }

    public String getD95Name() {
        return d95Name;
    }

    public void setD95Name(String d95Name) {
        this.d95Name = d95Name;
    }

    public Integer getHasParty() {
        return hasParty;
    }

    public void setHasParty(Integer hasParty) {
        this.hasParty = hasParty;
    }

    public String getFirstSecretaryCode() {
        return firstSecretaryCode;
    }

    public void setFirstSecretaryCode(String firstSecretaryCode) {
        this.firstSecretaryCode = firstSecretaryCode;
    }

    public String getFirstSecretaryName() {
        return firstSecretaryName;
    }

    public void setFirstSecretaryName(String firstSecretaryName) {
        this.firstSecretaryName = firstSecretaryName;
    }

    public Integer getStreetCadres() {
        return streetCadres;
    }

    public void setStreetCadres(Integer streetCadres) {
        this.streetCadres = streetCadres;
    }

    public Integer getAge35Below() {
        return age35Below;
    }

    public void setAge35Below(Integer age35Below) {
        this.age35Below = age35Below;
    }

    public Integer getAge36ToAge55() {
        return age36ToAge55;
    }

    public void setAge36ToAge55(Integer age36ToAge55) {
        this.age36ToAge55 = age36ToAge55;
    }

    public Integer getAge56Above() {
        return age56Above;
    }

    public void setAge56Above(Integer age56Above) {
        this.age56Above = age56Above;
    }

    public Integer getCollegeDegreeAbove() {
        return collegeDegreeAbove;
    }

    public void setCollegeDegreeAbove(Integer collegeDegreeAbove) {
        this.collegeDegreeAbove = collegeDegreeAbove;
    }

    public Integer getSecondarySchoolBelow() {
        return secondarySchoolBelow;
    }

    public void setSecondarySchoolBelow(Integer secondarySchoolBelow) {
        this.secondarySchoolBelow = secondarySchoolBelow;
    }

    public Integer getStreetCadresCivil() {
        return streetCadresCivil;
    }

    public void setStreetCadresCivil(Integer streetCadresCivil) {
        this.streetCadresCivil = streetCadresCivil;
    }

    public Integer getStreetCadresInstitutions() {
        return streetCadresInstitutions;
    }

    public void setStreetCadresInstitutions(Integer streetCadresInstitutions) {
        this.streetCadresInstitutions = streetCadresInstitutions;
    }

    public Integer getCadreOther() {
        return cadreOther;
    }

    public void setCadreOther(Integer cadreOther) {
        this.cadreOther = cadreOther;
    }

    public String getIncomeLess5w() {
        return incomeLess5w;
    }

    public void setIncomeLess5w(String incomeLess5w) {
        this.incomeLess5w = incomeLess5w;
    }

    public String getIncome50w100w() {
        return income50w100w;
    }

    public void setIncome50w100w(String income50w100w) {
        this.income50w100w = income50w100w;
    }

    public String getIncomeAbove100w() {
        return incomeAbove100w;
    }

    public void setIncomeAbove100w(String incomeAbove100w) {
        this.incomeAbove100w = incomeAbove100w;
    }

    public String getHasCollectiveEconomy() {
        return hasCollectiveEconomy;
    }

    public void setHasCollectiveEconomy(String hasCollectiveEconomy) {
        this.hasCollectiveEconomy = hasCollectiveEconomy;
    }

    public String getHasEconomicVillage() {
        return hasEconomicVillage;
    }

    public void setHasEconomicVillage(String hasEconomicVillage) {
        this.hasEconomicVillage = hasEconomicVillage;
    }

    public Integer getGraduateStudent() {
        return graduateStudent;
    }

    public void setGraduateStudent(Integer graduateStudent) {
        this.graduateStudent = graduateStudent;
    }

    public Integer getUndergraduateStudent() {
        return undergraduateStudent;
    }

    public void setUndergraduateStudent(Integer undergraduateStudent) {
        this.undergraduateStudent = undergraduateStudent;
    }

    public Integer getJuniorCollegeStudent() {
        return juniorCollegeStudent;
    }

    public void setJuniorCollegeStudent(Integer juniorCollegeStudent) {
        this.juniorCollegeStudent = juniorCollegeStudent;
    }

    public Integer getMiddleTechnicalStudents() {
        return middleTechnicalStudents;
    }

    public void setMiddleTechnicalStudents(Integer middleTechnicalStudents) {
        this.middleTechnicalStudents = middleTechnicalStudents;
    }

    public Integer getTeachersInstitutionsHigher() {
        return teachersInstitutionsHigher;
    }

    public void setTeachersInstitutionsHigher(Integer teachersInstitutionsHigher) {
        this.teachersInstitutionsHigher = teachersInstitutionsHigher;
    }

    public Integer getTeachersHigherWomen() {
        return teachersHigherWomen;
    }

    public void setTeachersHigherWomen(Integer teachersHigherWomen) {
        this.teachersHigherWomen = teachersHigherWomen;
    }

    public Integer getTeachersAgeThirtyFiveBelow() {
        return teachersAgeThirtyFiveBelow;
    }

    public void setTeachersAgeThirtyFiveBelow(Integer teachersAgeThirtyFiveBelow) {
        this.teachersAgeThirtyFiveBelow = teachersAgeThirtyFiveBelow;
    }

    public Integer getTechnicalSecondaryStudent() {
        return technicalSecondaryStudent;
    }

    public void setTechnicalSecondaryStudent(Integer technicalSecondaryStudent) {
        this.technicalSecondaryStudent = technicalSecondaryStudent;
    }

    public Integer getReportCommunityMember() {
        return reportCommunityMember;
    }

    public void setReportCommunityMember(Integer reportCommunityMember) {
        this.reportCommunityMember = reportCommunityMember;
    }

    public BigDecimal getYearAmount() {
        return yearAmount;
    }

    public void setYearAmount(BigDecimal yearAmount) {
        this.yearAmount = yearAmount;
    }

    public Integer getHasClerkPosition() {
        return hasClerkPosition;
    }

    public void setHasClerkPosition(Integer hasClerkPosition) {
        this.hasClerkPosition = hasClerkPosition;
    }

    public Integer getSecondaryCollegeWithOneTwo() {
        return secondaryCollegeWithOneTwo;
    }

    public void setSecondaryCollegeWithOneTwo(Integer secondaryCollegeWithOneTwo) {
        this.secondaryCollegeWithOneTwo = secondaryCollegeWithOneTwo;
    }

    public Integer getHasCountrySecretaryHbgb() {
        return hasCountrySecretaryHbgb;
    }

    public void setHasCountrySecretaryHbgb(Integer hasCountrySecretaryHbgb) {
        this.hasCountrySecretaryHbgb = hasCountrySecretaryHbgb;
    }

    public Integer getHasProperSecretary() {
        return hasProperSecretary;
    }

    public void setHasProperSecretary(Integer hasProperSecretary) {
        this.hasProperSecretary = hasProperSecretary;
    }

    public Integer getCommunityWorkerCount() {
        return communityWorkerCount;
    }

    public void setCommunityWorkerCount(Integer communityWorkerCount) {
        this.communityWorkerCount = communityWorkerCount;
    }

    public Integer getHasAuthority() {
        return hasAuthority;
    }

    public void setHasAuthority(Integer hasAuthority) {
        this.hasAuthority = hasAuthority;
    }

    public String getD01Code() {
        return d01Code;
    }

    public void setD01Code(String d01Code) {
        this.d01Code = d01Code;
    }

    public Integer getIsAllocateDean() {
        return isAllocateDean;
    }

    public void setIsAllocateDean(Integer isAllocateDean) {
        this.isAllocateDean = isAllocateDean;
    }

    public Integer getIsAllocateSecretary() {
        return isAllocateSecretary;
    }

    public void setIsAllocateSecretary(Integer isAllocateSecretary) {
        this.isAllocateSecretary = isAllocateSecretary;
    }

    public Object getLockFields() {
        return lockFields;
    }

    public void setLockFields(Object lockFields) {
        this.lockFields = lockFields;
    }

    public Integer getHasLabourUnion() {
        return hasLabourUnion;
    }

    public void setHasLabourUnion(Integer hasLabourUnion) {
        this.hasLabourUnion = hasLabourUnion;
    }

    public Integer getHasYouthLeague() {
        return hasYouthLeague;
    }

    public void setHasYouthLeague(Integer hasYouthLeague) {
        this.hasYouthLeague = hasYouthLeague;
    }

    public Integer getHasWomensFederation() {
        return hasWomensFederation;
    }

    public void setHasWomensFederation(Integer hasWomensFederation) {
        this.hasWomensFederation = hasWomensFederation;
    }

    public String getD155Code() {
        return d155Code;
    }

    public void setD155Code(String d155Code) {
        this.d155Code = d155Code;
    }

    public String getD155Name() {
        return d155Name;
    }

    public void setD155Name(String d155Name) {
        this.d155Name = d155Name;
    }

    public Integer getHasSetGrid() {
        return hasSetGrid;
    }

    public void setHasSetGrid(Integer hasSetGrid) {
        this.hasSetGrid = hasSetGrid;
    }

    public Integer getHasIncludedGridWorker() {
        return hasIncludedGridWorker;
    }

    public void setHasIncludedGridWorker(Integer hasIncludedGridWorker) {
        this.hasIncludedGridWorker = hasIncludedGridWorker;
    }

    public String getHasOrgSlackVillage() {
        return hasOrgSlackVillage;
    }

    public void setHasOrgSlackVillage(String hasOrgSlackVillage) {
        this.hasOrgSlackVillage = hasOrgSlackVillage;
    }

    public Integer getHasJointUnits() {
        return hasJointUnits;
    }

    public void setHasJointUnits(Integer hasJointUnits) {
        this.hasJointUnits = hasJointUnits;
    }

    public Integer getRuralProfessionalTechnicalAssociationNum() {
        return ruralProfessionalTechnicalAssociationNum;
    }

    public void setRuralProfessionalTechnicalAssociationNum(Integer ruralProfessionalTechnicalAssociationNum) {
        this.ruralProfessionalTechnicalAssociationNum = ruralProfessionalTechnicalAssociationNum;
    }

    public Integer getFarmerSpecializedCooperativesNum() {
        return farmerSpecializedCooperativesNum;
    }

    public void setFarmerSpecializedCooperativesNum(Integer farmerSpecializedCooperativesNum) {
        this.farmerSpecializedCooperativesNum = farmerSpecializedCooperativesNum;
    }

    public Integer getFamilyFarmNum() {
        return familyFarmNum;
    }

    public void setFamilyFarmNum(Integer familyFarmNum) {
        this.familyFarmNum = familyFarmNum;
    }

    public Integer getYearBranchTraining() {
        return yearBranchTraining;
    }

    public void setYearBranchTraining(Integer yearBranchTraining) {
        this.yearBranchTraining = yearBranchTraining;
    }

    public Integer getOrgRelationshipNotTransferred() {
        return orgRelationshipNotTransferred;
    }

    public void setOrgRelationshipNotTransferred(Integer orgRelationshipNotTransferred) {
        this.orgRelationshipNotTransferred = orgRelationshipNotTransferred;
    }

    public Integer getHasWorkingExpenses() {
        return hasWorkingExpenses;
    }

    public void setHasWorkingExpenses(Integer hasWorkingExpenses) {
        this.hasWorkingExpenses = hasWorkingExpenses;
    }

    public String getD159Code() {
        return d159Code;
    }

    public void setD159Code(String d159Code) {
        this.d159Code = d159Code;
    }

    public String getD159Name() {
        return d159Name;
    }

    public void setD159Name(String d159Name) {
        this.d159Name = d159Name;
    }

    public String getD02Code() {
        return d02Code;
    }

    public void setD02Code(String d02Code) {
        this.d02Code = d02Code;
    }

    public String getAllIncomeLess5w() {
        return allIncomeLess5w;
    }

    public void setAllIncomeLess5w(String allIncomeLess5w) {
        this.allIncomeLess5w = allIncomeLess5w;
    }

    public BigDecimal getYearAmountAll() {
        return yearAmountAll;
    }

    public void setYearAmountAll(BigDecimal yearAmountAll) {
        this.yearAmountAll = yearAmountAll;
    }

    public Integer getHasIndustryProvince() {
        return hasIndustryProvince;
    }

    public void setHasIndustryProvince(Integer hasIndustryProvince) {
        this.hasIndustryProvince = hasIndustryProvince;
    }

    public Integer getHasPerformedDetail() {
        return hasPerformedDetail;
    }

    public void setHasPerformedDetail(Integer hasPerformedDetail) {
        this.hasPerformedDetail = hasPerformedDetail;
    }

    public Integer getExistMemberBranches() {
        return existMemberBranches;
    }

    public void setExistMemberBranches(Integer existMemberBranches) {
        this.existMemberBranches = existMemberBranches;
    }

    public Integer getThreeMemberNoOrgBranches() {
        return threeMemberNoOrgBranches;
    }

    public void setThreeMemberNoOrgBranches(Integer threeMemberNoOrgBranches) {
        this.threeMemberNoOrgBranches = threeMemberNoOrgBranches;
    }

    public Integer getGrids() {
        return grids;
    }

    public void setGrids(Integer grids) {
        this.grids = grids;
    }

    public Integer getJmzzlyWgs() {
        return jmzzlyWgs;
    }

    public void setJmzzlyWgs(Integer jmzzlyWgs) {
        this.jmzzlyWgs = jmzzlyWgs;
    }

    public Integer getJmzzlyDzWgs() {
        return jmzzlyDzWgs;
    }

    public void setJmzzlyDzWgs(Integer jmzzlyDzWgs) {
        this.jmzzlyDzWgs = jmzzlyDzWgs;
    }

    public Integer getSwsqWgs() {
        return swsqWgs;
    }

    public void setSwsqWgs(Integer swsqWgs) {
        this.swsqWgs = swsqWgs;
    }

    public Integer getSwsqDzWgs() {
        return swsqDzWgs;
    }

    public void setSwsqDzWgs(Integer swsqDzWgs) {
        this.swsqDzWgs = swsqDzWgs;
    }

    public Integer getJdldWgs() {
        return jdldWgs;
    }

    public void setJdldWgs(Integer jdldWgs) {
        this.jdldWgs = jdldWgs;
    }

    public Integer getGridMembers() {
        return gridMembers;
    }

    public void setGridMembers(Integer gridMembers) {
        this.gridMembers = gridMembers;
    }

    public Integer getZzWgys() {
        return zzWgys;
    }

    public void setZzWgys(Integer zzWgys) {
        this.zzWgys = zzWgys;
    }

    public Integer getZzNgzze() {
        return zzNgzze;
    }

    public void setZzNgzze(Integer zzNgzze) {
        this.zzNgzze = zzNgzze;
    }

    public Integer getJzWgys() {
        return jzWgys;
    }

    public void setJzWgys(Integer jzWgys) {
        this.jzWgys = jzWgys;
    }

    public Integer getResidentialAreas() {
        return residentialAreas;
    }

    public void setResidentialAreas(Integer residentialAreas) {
        this.residentialAreas = residentialAreas;
    }

    public Integer getTubePlots() {
        return tubePlots;
    }

    public void setTubePlots(Integer tubePlots) {
        this.tubePlots = tubePlots;
    }

    public Integer getOrganizationCompanies() {
        return organizationCompanies;
    }

    public void setOrganizationCompanies(Integer organizationCompanies) {
        this.organizationCompanies = organizationCompanies;
    }

    public Integer getIndustryAuthorityCommunity() {
        return industryAuthorityCommunity;
    }

    public void setIndustryAuthorityCommunity(Integer industryAuthorityCommunity) {
        this.industryAuthorityCommunity = industryAuthorityCommunity;
    }

    public Integer getIndustryAuthorityOrganization() {
        return industryAuthorityOrganization;
    }

    public void setIndustryAuthorityOrganization(Integer industryAuthorityOrganization) {
        this.industryAuthorityOrganization = industryAuthorityOrganization;
    }

    public Integer getThreePartiesCommunities() {
        return threePartiesCommunities;
    }

    public void setThreePartiesCommunities(Integer threePartiesCommunities) {
        this.threePartiesCommunities = threePartiesCommunities;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "UnitAll{" +
                "id='" + id + '\'' +
                ", unitPartyCentral='" + unitPartyCentral + '\'' +
                ", unitPartyProvince='" + unitPartyProvince + '\'' +
                ", unitPartyCity='" + unitPartyCity + '\'' +
                ", unitPartyCounty='" + unitPartyCounty + '\'' +
                ", code='" + code + '\'' +
                ", esId='" + esId + '\'' +
                ", creditCode='" + creditCode + '\'' +
                ", createUnitOrgCode='" + createUnitOrgCode + '\'' +
                ", createOrgZbCode='" + createOrgZbCode + '\'' +
                ", createOrgCode='" + createOrgCode + '\'' +
                ", name='" + name + '\'' +
                ", pinyin='" + pinyin + '\'' +
                ", d48Code='" + d48Code + '\'' +
                ", d48Name='" + d48Name + '\'' +
                ", d04Code='" + d04Code + '\'' +
                ", d04Name='" + d04Name + '\'' +
                ", isOrgService=" + isOrgService +
                ", isVolTeam=" + isVolTeam +
                ", d16Code='" + d16Code + '\'' +
                ", d16Name='" + d16Name + '\'' +
                ", d17Code='" + d17Code + '\'' +
                ", d17Name='" + d17Name + '\'' +
                ", onPostNum=" + onPostNum +
                ", hasPartyWork=" + hasPartyWork +
                ", hasMajorDeputySecretary=" + hasMajorDeputySecretary +
                ", hasRepresentative=" + hasRepresentative +
                ", hasUnionOrganization=" + hasUnionOrganization +
                ", absorbedTissueNumber=" + absorbedTissueNumber +
                ", notTurnedParty=" + notTurnedParty +
                ", bzt610=" + bzt610 +
                ", tecNum=" + tecNum +
                ", zaigangGaoji=" + zaigangGaoji +
                ", d35Code='" + d35Code + '\'' +
                ", d35Name='" + d35Name + '\'' +
                ", d112Code='" + d112Code + '\'' +
                ", d112Name='" + d112Name + '\'' +
                ", d81Code='" + d81Code + '\'' +
                ", d81Name='" + d81Name + '\'' +
                ", d111Code='" + d111Code + '\'' +
                ", d111Name='" + d111Name + '\'' +
                ", b30A12=" + b30A12 +
                ", hasInstructorContact=" + hasInstructorContact +
                ", hasOrganizationSecretary=" + hasOrganizationSecretary +
                ", employeesNumber=" + employeesNumber +
                ", isDecouplIndustry=" + isDecouplIndustry +
                ", createPartyGroup=" + createPartyGroup +
                ", createPartyTeam=" + createPartyTeam +
                ", createPartyCommittee=" + createPartyCommittee +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deleteTime=" + deleteTime +
                ", year=" + year +
                ", firstSecretarySelect='" + firstSecretarySelect + '\'' +
                ", secretaryTrainingNum=" + secretaryTrainingNum +
                ", hasThousand=" + hasThousand +
                ", hasBundled=" + hasBundled +
                ", promotedNum=" + promotedNum +
                ", adjustedNum=" + adjustedNum +
                ", operatingExpenses=" + operatingExpenses +
                ", villagePer=" + villagePer +
                ", secretarySalary=" + secretarySalary +
                ", spaceArea=" + spaceArea +
                ", newExpandArea=" + newExpandArea +
                ", secretaryPartyNum=" + secretaryPartyNum +
                ", secretaryPromotedNum=" + secretaryPromotedNum +
                ", communityMoneyNum=" + communityMoneyNum +
                ", communityServingPeople=" + communityServingPeople +
                ", communityMasses=" + communityMasses +
                ", hasExaminationPower=" + hasExaminationPower +
                ", hasCancelInvestmentPromotion=" + hasCancelInvestmentPromotion +
                ", hasWorkMechanism=" + hasWorkMechanism +
                ", hasIncludedCommittee=" + hasIncludedCommittee +
                ", hasGroupServiceCenter=" + hasGroupServiceCenter +
                ", hasPartyBuildEndeavor=" + hasPartyBuildEndeavor +
                ", partyWorkerNum=" + partyWorkerNum +
                ", collegeDegreeNum=" + collegeDegreeNum +
                ", officesAndStreets=" + officesAndStreets +
                ", veterans=" + veterans +
                ", civilServants=" + civilServants +
                ", establishment=" + establishment +
                ", twoAndOne=" + twoAndOne +
                ", hasLowerSocial=" + hasLowerSocial +
                ", communityWorkersSalary=" + communityWorkersSalary +
                ", communitySecretarySalary=" + communitySecretarySalary +
                ", communityBuildingNumber=" + communityBuildingNumber +
                ", hasCommunityPositions=" + hasCommunityPositions +
                ", includedFinancial=" + includedFinancial +
                ", specialFundsMasses=" + specialFundsMasses +
                ", hasCommunityReport=" + hasCommunityReport +
                ", communityOfficeSpace=" + communityOfficeSpace +
                ", hasParttimeSystem=" + hasParttimeSystem +
                ", hasStandingCommittee=" + hasStandingCommittee +
                ", d109Code='" + d109Code + '\'' +
                ", d109Name='" + d109Name + '\'' +
                ", hasReportImplementation=" + hasReportImplementation +
                ", hasOfficeProcedure=" + hasOfficeProcedure +
                ", schoolHasReportsLocal=" + schoolHasReportsLocal +
                ", hasSecretaryUniversityCommittee=" + hasSecretaryUniversityCommittee +
                ", hasPresidentPartyMember=" + hasPresidentPartyMember +
                ", hasDeputyPartySecretary=" + hasDeputyPartySecretary +
                ", hasSecretaryCommittee=" + hasSecretaryCommittee +
                ", hasTissueCommittee=" + hasTissueCommittee +
                ", hasPropagandaCommittee=" + hasPropagandaCommittee +
                ", hasFrontCommittee=" + hasFrontCommittee +
                ", secondaryCollege=" + secondaryCollege +
                ", secondaryCollegeCommittee=" + secondaryCollegeCommittee +
                ", secondaryCollegeAlwaysBranch=" + secondaryCollegeAlwaysBranch +
                ", secondaryCollegeBranch=" + secondaryCollegeBranch +
                ", secondaryCollegeWithOne=" + secondaryCollegeWithOne +
                ", secondaryCollegeGreaterTwo=" + secondaryCollegeGreaterTwo +
                ", hasResponsibilitySystem=" + hasResponsibilitySystem +
                ", ldtzyyzc=" + ldtzyyzc +
                ", isPartyWorkWrite=" + isPartyWorkWrite +
                ", isOpenOrgAssess=" + isOpenOrgAssess +
                ", isLeaderSeparate=" + isLeaderSeparate +
                ", leaderIsGcdy=" + leaderIsGcdy +
                ", isLeaderDeputySecretary=" + isLeaderDeputySecretary +
                ", isSetOrgParty=" + isSetOrgParty +
                ", secretaryIsInsideLeader=" + secretaryIsInsideLeader +
                ", sjsdtr=" + sjsdtr +
                ", internalInstitutions=" + internalInstitutions +
                ", internalInstitutionsTransition=" + internalInstitutionsTransition +
                ", hospitalsDevelop=" + hospitalsDevelop +
                ", hospitalsDevelopTechnology=" + hospitalsDevelopTechnology +
                ", hospitalsActive=" + hospitalsActive +
                ", hasDirectors=" + hasDirectors +
                ", hasChairmanSecretary=" + hasChairmanSecretary +
                ", hasProportionateFunding=" + hasProportionateFunding +
                ", hasBranchToCatch=" + hasBranchToCatch +
                ", hasByLeader=" + hasByLeader +
                ", hasSameTreatment=" + hasSameTreatment +
                ", hasPublicCompany=" + hasPublicCompany +
                ", hasArticlesIncorporation=" + hasArticlesIncorporation +
                ", hasPrepositionalProcedure=" + hasPrepositionalProcedure +
                ", hasResponsiblePerson=" + hasResponsiblePerson +
                ", branches=" + branches +
                ", partyMembers=" + partyMembers +
                ", hasNonPublicParty=" + hasNonPublicParty +
                ", hasSpecialAgencies=" + hasSpecialAgencies +
                ", d114Code='" + d114Code + '\'' +
                ", d114Name='" + d114Name + '\'' +
                ", d05Code='" + d05Code + '\'' +
                ", d05Name='" + d05Name + '\'' +
                ", legalIsSecretary=" + legalIsSecretary +
                ", partyBranchesNumber=" + partyBranchesNumber +
                ", inPartyMembers=" + inPartyMembers +
                ", technologyPartyMembers=" + technologyPartyMembers +
                ", aboveBkEducation=" + aboveBkEducation +
                ", aboveYjsEducation=" + aboveYjsEducation +
                ", hasSecretaryHighLevel=" + hasSecretaryHighLevel +
                ", yearDevelopMem=" + yearDevelopMem +
                ", hasHeadParty=" + hasHeadParty +
                ", hasFirmLevel=" + hasFirmLevel +
                ", hasLevelSecretary=" + hasLevelSecretary +
                ", isLegal=" + isLegal +
                ", d115Code='" + d115Code + '\'' +
                ", partyOrganizationNum=" + partyOrganizationNum +
                ", haveBeenEstablished=" + haveBeenEstablished +
                ", collegePartyBranch=" + collegePartyBranch +
                ", teacherPartyBranch=" + teacherPartyBranch +
                ", hasTeachersDoubleLeaders=" + hasTeachersDoubleLeaders +
                ", studentPartyBranch=" + studentPartyBranch +
                ", organPartyBranch=" + organPartyBranch +
                ", yearTraining=" + yearTraining +
                ", graduatePartyMember=" + graduatePartyMember +
                ", isYearOrgChange=" + isYearOrgChange +
                ", hasCommunityAccess=" + hasCommunityAccess +
                ", hasFourTwoOpenWork=" + hasFourTwoOpenWork +
                ", hasCommunitySupervisory=" + hasCommunitySupervisory +
                ", d79Code='" + d79Code + '\'' +
                ", d78Code='" + d78Code + '\'' +
                ", d77Code='" + d77Code + '\'' +
                ", manageUnitOrgCode='" + manageUnitOrgCode + '\'' +
                ", isCreateOrg=" + isCreateOrg +
                ", mainUnitOrgCode='" + mainUnitOrgCode + '\'' +
                ", hasMajorWorker=" + hasMajorWorker +
                ", manageOrgCode='" + manageOrgCode + '\'' +
                ", manageOrgName='" + manageOrgName + '\'' +
                ", legalIsMember=" + legalIsMember +
                ", isBranch=" + isBranch +
                ", address='" + address + '\'' +
                ", telephone='" + telephone + '\'' +
                ", ratio=" + ratio +
                ", keywords='" + keywords + '\'' +
                ", unitCode='" + unitCode + '\'' +
                ", orgs='" + orgs + '\'' +
                ", timestamp=" + timestamp +
                ", mainOrgCode='" + mainOrgCode + '\'' +
                ", mainOrgName='" + mainOrgName + '\'' +
                ", mainOrgType='" + mainOrgType + '\'' +
                ", mainOrgTypeCode='" + mainOrgTypeCode + '\'' +
                ", isHistory='" + isHistory + '\'' +
                ", updateAccount='" + updateAccount + '\'' +
                ", d95Code='" + d95Code + '\'' +
                ", d95Name='" + d95Name + '\'' +
                ", hasParty=" + hasParty +
                ", firstSecretaryCode='" + firstSecretaryCode + '\'' +
                ", firstSecretaryName='" + firstSecretaryName + '\'' +
                ", streetCadres=" + streetCadres +
                ", age35Below=" + age35Below +
                ", age36ToAge55=" + age36ToAge55 +
                ", age56Above=" + age56Above +
                ", collegeDegreeAbove=" + collegeDegreeAbove +
                ", secondarySchoolBelow=" + secondarySchoolBelow +
                ", streetCadresCivil=" + streetCadresCivil +
                ", streetCadresInstitutions=" + streetCadresInstitutions +
                ", cadreOther=" + cadreOther +
                ", incomeLess5w='" + incomeLess5w + '\'' +
                ", income50w100w='" + income50w100w + '\'' +
                ", incomeAbove100w='" + incomeAbove100w + '\'' +
                ", hasCollectiveEconomy='" + hasCollectiveEconomy + '\'' +
                ", hasEconomicVillage='" + hasEconomicVillage + '\'' +
                ", reportCommunityMember=" + reportCommunityMember +
                ", graduateStudent=" + graduateStudent +
                ", undergraduateStudent=" + undergraduateStudent +
                ", juniorCollegeStudent=" + juniorCollegeStudent +
                ", middleTechnicalStudents=" + middleTechnicalStudents +
                ", teachersInstitutionsHigher=" + teachersInstitutionsHigher +
                ", teachersHigherWomen=" + teachersHigherWomen +
                ", teachersAgeThirtyFiveBelow=" + teachersAgeThirtyFiveBelow +
                ", technicalSecondaryStudent=" + technicalSecondaryStudent +
                ", yearAmount=" + yearAmount +
                ", hasClerkPosition=" + hasClerkPosition +
                ", secondaryCollegeWithOneTwo=" + secondaryCollegeWithOneTwo +
                ", hasCountrySecretaryHbgb=" + hasCountrySecretaryHbgb +
                ", hasProperSecretary=" + hasProperSecretary +
                ", communityWorkerCount=" + communityWorkerCount +
                ", hasAuthority=" + hasAuthority +
                ", d01Code='" + d01Code + '\'' +
                ", isAllocateDean=" + isAllocateDean +
                ", isAllocateSecretary=" + isAllocateSecretary +
                ", lockFields=" + lockFields +
                ", hasLabourUnion=" + hasLabourUnion +
                ", hasYouthLeague=" + hasYouthLeague +
                ", hasWomensFederation=" + hasWomensFederation +
                ", d155Code='" + d155Code + '\'' +
                ", d155Name='" + d155Name + '\'' +
                ", hasSetGrid=" + hasSetGrid +
                ", hasIncludedGridWorker=" + hasIncludedGridWorker +
                ", hasOrgSlackVillage='" + hasOrgSlackVillage + '\'' +
                ", hasJointUnits=" + hasJointUnits +
                ", ruralProfessionalTechnicalAssociationNum=" + ruralProfessionalTechnicalAssociationNum +
                ", farmerSpecializedCooperativesNum=" + farmerSpecializedCooperativesNum +
                ", familyFarmNum=" + familyFarmNum +
                ", yearBranchTraining=" + yearBranchTraining +
                ", orgRelationshipNotTransferred=" + orgRelationshipNotTransferred +
                '}';
    }
}
