package com.zenith.front.entity.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.zenith.front.annotation.DynamicTable;
import com.zenith.front.config.JsonbTypeHandler;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-10
 */
@DynamicTable
@TableName("ccp_unit")
public class Unit extends Model<Unit> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("code")
    private String code;

    @TableField("es_id")
    private String esId;

    /**
     * 单位代码
     */
    @TableField("credit_code")
    private String creditCode;

    /**
     * 创建单位的组织层级码
     */
    @TableField("create_unit_org_code")
    private String createUnitOrgCode;

    /**
     * 创建单位的组织code
     */
    @TableField("create_org_zb_code")
    private String createOrgZbCode;

    /**
     * 创建单位的组织code
     */
    @TableField("create_org_code")
    private String createOrgCode;

    /**
     * 是否建立组织
     */
    @TableField("is_create_org")
    private Integer isCreateOrg;

    @TableField("name")
    private String name;

    @TableField("pinyin")
    private String pinyin;

    /**
     * 所在地区_code
     */
    @TableField("d48_code")
    private String d48Code;

    /**
     * 所在地区_name
     */
    @TableField("d48_name")
    private String d48Name;

    /**
     * 单位类别_code
     */
    @TableField("d04_code")
    private String d04Code;

    /**
     * 单位类别_name
     */
    @TableField("d04_name")
    private String d04Name;

    /**
     * 配备专职党务工作人员
     */
    @TableField("has_major_worker")
    private Integer hasMajorWorker;

    /**
     * 配备专职副书记
     */
    @TableField("has_major_deputy_secretary")
    private Integer hasMajorDeputySecretary;

    /**
     * 党建指导组织_code
     */
    @TableField("manage_org_code")
    private String manageOrgCode;

    /**
     * 党建指导组织_name
     */
    @TableField("manage_org_name")
    private String manageOrgName;

    /**
     * 单位隶属关系_code
     */
    @TableField("d35_code")
    private String d35Code;

    /**
     * 单位隶属关系_name
     */
    @TableField("d35_name")
    private String d35Name;

    /**
     * 法定代表人是否为党员
     */
    @TableField("legal_is_member")
    private Integer legalIsMember;

    /**
     * 法定代表人兼任党组织书记
     */
    @TableField("legal_is_secretary")
    private Integer legalIsSecretary;

    /**
     * 未建立党组织情况--代码
     */
    @TableField("d05_code")
    private String d05Code;

    /**
     * 未建立党组织情况-
     */
    @TableField("d05_name")
    private String d05Name;

    /**
     * 是否是分支单位
     */
    @TableField("is_branch")
    private Integer isBranch;

    /**
     * 单位地址
     */
    @TableField("address")
    private String address;

    /**
     * 单位电话号码
     */
    @TableField("telephone")
    private String telephone;

    /**
     * 是否法人单位标识：1是，0否
     */
    @TableField("is_legal")
    private Integer isLegal;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    @TableField("delete_time")
    private Date deleteTime;

    @TableField("ratio")
    private Double ratio;

    /**
     * 搜索关键字
     */
    @TableField("keywords")
    private String keywords;

    @TableField("unit_code")
    private String unitCode;

    @TableField("orgs")
    private String orgs;

    @TableField("timestamp")
    private Date timestamp;

    /**
     * 主单位标示的组织code
     */
    @TableField("main_org_code")
    private String mainOrgCode;

    /**
     * 主单位标示的组织name
     */
    @TableField("main_org_name")
    private String mainOrgName;

    /**
     * 主单位标示的组织类别
     */
    @TableField("main_org_type")
    private String mainOrgType;

    /**
     * 主单位标示的组织大类
     */
    @TableField("main_org_type_code")
    private String mainOrgTypeCode;

    /**
     * 主单位标示的组织层级码
     */
    @TableField("main_unit_org_code")
    private String mainUnitOrgCode;

    /**
     * 是否是历史数据
     */
    @TableField("is_history")
    private String isHistory;

    /**
     * 最近更新账号
     */
    @TableField("update_account")
    private String updateAccount;

    /**
     * 党建指导组织_层级码
     */
    @TableField("manage_unit_org_code")
    private String manageUnitOrgCode;

    /**
     * 联合党支部数
     */
    @TableField("party_branches_number")
    private Integer partyBranchesNumber;
    /**
     * 锁定字段
     */
    @TableField(value = "lock_fields", typeHandler = JsonbTypeHandler.class)
    private Object lockFields;

    /**
     * 应到村任职选调生人数（数字、必填）
     */
    @TableField("number_of_students_to_be_transferred_to_the_village")
    private Integer numberOfStudentsToBeTransferredToTheVillage;

    /**
     * 是否有大学毕业生在村工作（选择框、必填）1 是 0 否
     */
    @TableField("whether_there_are_college_graduates_working_in_the_village")
    private Integer whetherThereAreCollegeGraduatesWorkingInTheVillage;
    /**
     * 参加县级及以上集中培训人数（数字、必填）
     */
    @TableField("join_above_county_train_num")
    private Integer joinAboveCountyTrainNum;
    /**
     * 村干部参加城镇职工养老保险人数（数字、必填）
     */
    @TableField("village_join_urban_worker_num")
    private Integer villageJoinUrbanWorkerNum;

    /**
     * 不是党委委员的政府领导班子成员人数（必填、数字）
     */
    @TableField("number_of_non_governmental_members")
    private Integer numberOfNonGovernmentalMembers;

    /**
     * 登记级别
     */
    @TableField("d118_code")
    private String d118Code;

    /**
     * 登记级别
     */
    @TableField("d118_name")
    private String d118Name;

    /**
     * 是否民族院校 供发展党员工作情况调度表 民族院校（人） 使用
     */
    @TableField("has_national_colleges")
    private Integer hasNationalColleges;

    /**
     * 村（社区）类别
     */
    @TableField("d155_code")
    private String d155Code;

    @TableField("d155_name")
    private String d155Name;

    /**
     * 农村专业技术协会数量
     */
    @TableField("rural_professional_technical_association_num")
    private Integer ruralProfessionalTechnicalAssociationNum;

    /**
     * 农民专业合作社数量
     */
    @TableField("farmer_specialized_cooperatives_num")
    private Integer farmerSpecializedCooperativesNum;

    /**
     * 家庭农场数量
     */
    @TableField("family_farm_num")
    private Integer familyFarmNum;

    /**
     * 专调表八 本年度高校党支部书记参加培训人次
     */
    @TableField("year_branch_training")
    private Integer yearBranchTraining;

    /**
     * 专调表八 本年度院系本级党组织书记参加培训人次
     */
    @TableField("year_training")
    private Integer yearTraining;

    /**
     * 专调表八 本年度毕业生党员
     */
    @TableField("graduate_party_member")
    private Integer graduatePartyMember;

    /**
     * 专调表八 尚未转出组织关系的
     */
    @TableField("org_relationship_not_transferred")
    private Integer orgRelationshipNotTransferred;

    /**
     * 是否将党建工作经费纳入管理费列支、税前扣除(当单位类型为社会组织)
     */
    @TableField("has_working_expenses")
    private Integer hasWorkingExpenses;

    /**
     * 与业务主管单位关系(当单位类型为社会组织)
     */
    @TableField("d159_code")
    private String d159Code;

    /**
     * 与业务主管单位关系(当单位类型为社会组织)
     */
    @TableField("d159_name")
    private String d159Name;


    /**
     * 国民经济行业CODE
     */
    @TableField("d194_code")
    private String d194Code;
    /**
     * 国民经济行业name
     */
    @TableField("d194_name")
    private String d194Name;

    /**
     * 生产性服务行业CODE
     */
    @TableField("d195_code")
    private String d195Code;
    /**
     * 生产性服务行业name
     */
    @TableField("d195_name")
    private String d195Name;

    /**
     * 是否垂直管理部门 1是，0否 dict_d147
     */
    @TableField("is_czglbm")
    private String isCzglbm;

    public String getIsCzglbm() {
        return isCzglbm;
    }

    public void setIsCzglbm(String isCzglbm) {
        this.isCzglbm = isCzglbm;
    }

    public String getD194Code() {
        return d194Code;
    }

    public void setD194Code(String d194Code) {
        this.d194Code = d194Code;
    }

    public String getD194Name() {
        return d194Name;
    }

    public void setD194Name(String d194Name) {
        this.d194Name = d194Name;
    }

    public String getD195Code() {
        return d195Code;
    }

    public void setD195Code(String d195Code) {
        this.d195Code = d195Code;
    }

    public String getD195Name() {
        return d195Name;
    }

    public void setD195Name(String d195Name) {
        this.d195Name = d195Name;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEsId() {
        return esId;
    }

    public void setEsId(String esId) {
        this.esId = esId;
    }

    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getCreateUnitOrgCode() {
        return createUnitOrgCode;
    }

    public void setCreateUnitOrgCode(String createUnitOrgCode) {
        this.createUnitOrgCode = createUnitOrgCode;
    }

    public String getCreateOrgZbCode() {
        return createOrgZbCode;
    }

    public void setCreateOrgZbCode(String createOrgZbCode) {
        this.createOrgZbCode = createOrgZbCode;
    }

    public String getCreateOrgCode() {
        return createOrgCode;
    }

    public void setCreateOrgCode(String createOrgCode) {
        this.createOrgCode = createOrgCode;
    }

    public Integer getIsCreateOrg() {
        return isCreateOrg;
    }

    public void setIsCreateOrg(Integer isCreateOrg) {
        this.isCreateOrg = isCreateOrg;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPinyin() {
        return pinyin;
    }

    public void setPinyin(String pinyin) {
        this.pinyin = pinyin;
    }

    public String getD48Code() {
        return d48Code;
    }

    public void setD48Code(String d48Code) {
        this.d48Code = d48Code;
    }

    public String getD48Name() {
        return d48Name;
    }

    public void setD48Name(String d48Name) {
        this.d48Name = d48Name;
    }

    public String getD04Code() {
        return d04Code;
    }

    public void setD04Code(String d04Code) {
        this.d04Code = d04Code;
    }

    public String getD04Name() {
        return d04Name;
    }

    public void setD04Name(String d04Name) {
        this.d04Name = d04Name;
    }

    public Integer getHasMajorWorker() {
        return hasMajorWorker;
    }

    public void setHasMajorWorker(Integer hasMajorWorker) {
        this.hasMajorWorker = hasMajorWorker;
    }

    public Integer getHasMajorDeputySecretary() {
        return hasMajorDeputySecretary;
    }

    public void setHasMajorDeputySecretary(Integer hasMajorDeputySecretary) {
        this.hasMajorDeputySecretary = hasMajorDeputySecretary;
    }

    public String getManageOrgCode() {
        return manageOrgCode;
    }

    public void setManageOrgCode(String manageOrgCode) {
        this.manageOrgCode = manageOrgCode;
    }

    public String getManageOrgName() {
        return manageOrgName;
    }

    public void setManageOrgName(String manageOrgName) {
        this.manageOrgName = manageOrgName;
    }

    public String getD35Code() {
        return d35Code;
    }

    public void setD35Code(String d35Code) {
        this.d35Code = d35Code;
    }

    public String getD35Name() {
        return d35Name;
    }

    public void setD35Name(String d35Name) {
        this.d35Name = d35Name;
    }

    public Integer getLegalIsMember() {
        return legalIsMember;
    }

    public void setLegalIsMember(Integer legalIsMember) {
        this.legalIsMember = legalIsMember;
    }

    public Integer getLegalIsSecretary() {
        return legalIsSecretary;
    }

    public void setLegalIsSecretary(Integer legalIsSecretary) {
        this.legalIsSecretary = legalIsSecretary;
    }

    public String getD05Code() {
        return d05Code;
    }

    public void setD05Code(String d05Code) {
        this.d05Code = d05Code;
    }

    public String getD05Name() {
        return d05Name;
    }

    public void setD05Name(String d05Name) {
        this.d05Name = d05Name;
    }

    public Integer getIsBranch() {
        return isBranch;
    }

    public void setIsBranch(Integer isBranch) {
        this.isBranch = isBranch;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public Integer getIsLegal() {
        return isLegal;
    }

    public void setIsLegal(Integer isLegal) {
        this.isLegal = isLegal;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Double getRatio() {
        return ratio;
    }

    public void setRatio(Double ratio) {
        this.ratio = ratio;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getOrgs() {
        return orgs;
    }

    public void setOrgs(String orgs) {
        this.orgs = orgs;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public String getMainOrgCode() {
        return mainOrgCode;
    }

    public void setMainOrgCode(String mainOrgCode) {
        this.mainOrgCode = mainOrgCode;
    }

    public String getMainOrgName() {
        return mainOrgName;
    }

    public void setMainOrgName(String mainOrgName) {
        this.mainOrgName = mainOrgName;
    }

    public String getMainOrgType() {
        return mainOrgType;
    }

    public void setMainOrgType(String mainOrgType) {
        this.mainOrgType = mainOrgType;
    }

    public String getMainOrgTypeCode() {
        return mainOrgTypeCode;
    }

    public void setMainOrgTypeCode(String mainOrgTypeCode) {
        this.mainOrgTypeCode = mainOrgTypeCode;
    }

    public String getMainUnitOrgCode() {
        return mainUnitOrgCode;
    }

    public void setMainUnitOrgCode(String mainUnitOrgCode) {
        this.mainUnitOrgCode = mainUnitOrgCode;
    }

    public String getIsHistory() {
        return isHistory;
    }

    public void setIsHistory(String isHistory) {
        this.isHistory = isHistory;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    public String getManageUnitOrgCode() {
        return manageUnitOrgCode;
    }

    public void setManageUnitOrgCode(String manageUnitOrgCode) {
        this.manageUnitOrgCode = manageUnitOrgCode;
    }

    public Integer getPartyBranchesNumber() {
        return partyBranchesNumber;
    }

    public void setPartyBranchesNumber(Integer partyBranchesNumber) {
        this.partyBranchesNumber = partyBranchesNumber;
    }

    public Object getLockFields() {
        return lockFields;
    }

    public void setLockFields(Object lockFields) {
        this.lockFields = lockFields;
    }

    public Integer getNumberOfStudentsToBeTransferredToTheVillage() {
        return numberOfStudentsToBeTransferredToTheVillage;
    }

    public void setNumberOfStudentsToBeTransferredToTheVillage(Integer numberOfStudentsToBeTransferredToTheVillage) {
        this.numberOfStudentsToBeTransferredToTheVillage = numberOfStudentsToBeTransferredToTheVillage;
    }

    public Integer getWhetherThereAreCollegeGraduatesWorkingInTheVillage() {
        return whetherThereAreCollegeGraduatesWorkingInTheVillage;
    }

    public void setWhetherThereAreCollegeGraduatesWorkingInTheVillage(Integer whetherThereAreCollegeGraduatesWorkingInTheVillage) {
        this.whetherThereAreCollegeGraduatesWorkingInTheVillage = whetherThereAreCollegeGraduatesWorkingInTheVillage;
    }

    public Integer getJoinAboveCountyTrainNum() {
        return joinAboveCountyTrainNum;
    }

    public void setJoinAboveCountyTrainNum(Integer joinAboveCountyTrainNum) {
        this.joinAboveCountyTrainNum = joinAboveCountyTrainNum;
    }

    public Integer getVillageJoinUrbanWorkerNum() {
        return villageJoinUrbanWorkerNum;
    }

    public void setVillageJoinUrbanWorkerNum(Integer villageJoinUrbanWorkerNum) {
        this.villageJoinUrbanWorkerNum = villageJoinUrbanWorkerNum;
    }

    public Integer getNumberOfNonGovernmentalMembers() {
        return numberOfNonGovernmentalMembers;
    }

    public void setNumberOfNonGovernmentalMembers(Integer numberOfNonGovernmentalMembers) {
        this.numberOfNonGovernmentalMembers = numberOfNonGovernmentalMembers;
    }

    public String getD118Code() {
        return d118Code;
    }

    public void setD118Code(String d118Code) {
        this.d118Code = d118Code;
    }

    public String getD118Name() {
        return d118Name;
    }

    public void setD118Name(String d118Name) {
        this.d118Name = d118Name;
    }

    public Integer getHasNationalColleges() {
        return hasNationalColleges;
    }

    public void setHasNationalColleges(Integer hasNationalColleges) {
        this.hasNationalColleges = hasNationalColleges;
    }

    public String getD155Code() {
        return d155Code;
    }

    public void setD155Code(String d155Code) {
        this.d155Code = d155Code;
    }

    public String getD155Name() {
        return d155Name;
    }

    public void setD155Name(String d155Name) {
        this.d155Name = d155Name;
    }

    public Integer getRuralProfessionalTechnicalAssociationNum() {
        return ruralProfessionalTechnicalAssociationNum;
    }

    public void setRuralProfessionalTechnicalAssociationNum(Integer ruralProfessionalTechnicalAssociationNum) {
        this.ruralProfessionalTechnicalAssociationNum = ruralProfessionalTechnicalAssociationNum;
    }

    public Integer getFarmerSpecializedCooperativesNum() {
        return farmerSpecializedCooperativesNum;
    }

    public void setFarmerSpecializedCooperativesNum(Integer farmerSpecializedCooperativesNum) {
        this.farmerSpecializedCooperativesNum = farmerSpecializedCooperativesNum;
    }

    public Integer getFamilyFarmNum() {
        return familyFarmNum;
    }

    public void setFamilyFarmNum(Integer familyFarmNum) {
        this.familyFarmNum = familyFarmNum;
    }

    public Integer getYearBranchTraining() {
        return yearBranchTraining;
    }

    public void setYearBranchTraining(Integer yearBranchTraining) {
        this.yearBranchTraining = yearBranchTraining;
    }

    public Integer getYearTraining() {
        return yearTraining;
    }

    public void setYearTraining(Integer yearTraining) {
        this.yearTraining = yearTraining;
    }

    public Integer getGraduatePartyMember() {
        return graduatePartyMember;
    }

    public void setGraduatePartyMember(Integer graduatePartyMember) {
        this.graduatePartyMember = graduatePartyMember;
    }

    public Integer getOrgRelationshipNotTransferred() {
        return orgRelationshipNotTransferred;
    }

    public void setOrgRelationshipNotTransferred(Integer orgRelationshipNotTransferred) {
        this.orgRelationshipNotTransferred = orgRelationshipNotTransferred;
    }

    public Integer getHasWorkingExpenses() {
        return hasWorkingExpenses;
    }

    public void setHasWorkingExpenses(Integer hasWorkingExpenses) {
        this.hasWorkingExpenses = hasWorkingExpenses;
    }

    public String getD159Code() {
        return d159Code;
    }

    public void setD159Code(String d159Code) {
        this.d159Code = d159Code;
    }

    public String getD159Name() {
        return d159Name;
    }

    public void setD159Name(String d159Name) {
        this.d159Name = d159Name;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "Unit{" +
                "id=" + id +
                ", code=" + code +
                ", esId=" + esId +
                ", creditCode=" + creditCode +
                ", createUnitOrgCode=" + createUnitOrgCode +
                ", createOrgZbCode=" + createOrgZbCode +
                ", createOrgCode=" + createOrgCode +
                ", isCreateOrg=" + isCreateOrg +
                ", name=" + name +
                ", pinyin=" + pinyin +
                ", d48Code=" + d48Code +
                ", d48Name=" + d48Name +
                ", d04Code=" + d04Code +
                ", d04Name=" + d04Name +
                ", hasMajorWorker=" + hasMajorWorker +
                ", hasMajorDeputySecretary=" + hasMajorDeputySecretary +
                ", manageOrgCode=" + manageOrgCode +
                ", manageOrgName=" + manageOrgName +
                ", d35Code=" + d35Code +
                ", d35Name=" + d35Name +
                ", legalIsMember=" + legalIsMember +
                ", legalIsSecretary=" + legalIsSecretary +
                ", d05Code=" + d05Code +
                ", d05Name=" + d05Name +
                ", isBranch=" + isBranch +
                ", address=" + address +
                ", telephone=" + telephone +
                ", isLegal=" + isLegal +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deleteTime=" + deleteTime +
                ", ratio=" + ratio +
                ", keywords=" + keywords +
                ", unitCode=" + unitCode +
                ", orgs=" + orgs +
                ", timestamp=" + timestamp +
                ", mainOrgCode=" + mainOrgCode +
                ", mainOrgName=" + mainOrgName +
                ", mainOrgType=" + mainOrgType +
                ", mainOrgTypeCode=" + mainOrgTypeCode +
                ", mainUnitOrgCode=" + mainUnitOrgCode +
                ", isHistory=" + isHistory +
                ", updateAccount=" + updateAccount +
                ", manageUnitOrgCode=" + manageUnitOrgCode +
                ", partyBranchesNumber=" + partyBranchesNumber +
                "}";
    }
}
