package com.zenith.front.entity.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.zenith.front.config.JsonbTypeHandler;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 积极分子转接记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-09
 */
@TableName("ccp_activist_transfer_record")
public class ActivistTransferRecord extends Model<ActivistTransferRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 用户id
     */
    @TableField("user_id")
    private String userId;

    /**
     * 名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 人员id
     */
    @TableField("mem_id")
    private String memId;

    /**
     * 源组织id
     */
    @TableField("src_org_id")
    private String srcOrgId;

    /**
     * 源组织名称
     */
    @TableField("src_org_name")
    private String srcOrgName;

    /**
     * 目标组织id
     */
    @TableField("target_org_id")
    private String targetOrgId;

    /**
     * 目标组织名称
     */
    @TableField("target_org_name")
    private String targetOrgName;

    /**
     * 公共节点id
     */
    @TableField("common_org_id")
    private String commonOrgId;

    /**
     * 公共节点名称
     */
    @TableField("common_org_name")
    private String commonOrgName;

    /**
     * 源组织关系数组
     */
    @TableField(value = "src_org_relation", typeHandler = JsonbTypeHandler.class)
    private Object srcOrgRelation;
    /**
     * 目标组织关系数组
     */
    @TableField(value = "target_org_relation", typeHandler = JsonbTypeHandler.class)
    private Object targetOrgRelation;
    /**
     * 转出类型
     */
    @TableField("out_type")
    private String outType;

    /**
     * 转入类型
     */
    @TableField("in_type")
    private String inType;

    /**
     * 类型
     */
    @TableField("type")
    private String type;

    /**
     * 审批记录id
     */
    @TableField("current_approval_id")
    private String currentApprovalId;

    /**
     * 转接状态,0转接中 1 转接完成  2已撤销
     */
    @TableField("status")
    private Integer status;

    /**
     * 审批理由
     */
    @TableField("reason")
    private String reason;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 附加信息
     */
    @TableField(value = "extra_data", typeHandler = JsonbTypeHandler.class)
    private Object extraData;
    /**
     * log表附加信息
     */
    @TableField(value = "extra_data_log", typeHandler = JsonbTypeHandler.class)
    private Object extraDataLog;

    /**
     * 介绍信url
     */
    @TableField("letter_url")
    private String letterUrl;

    /**
     * 影响人数
     */
    @TableField("effect_mems")
    private Integer effectMems;

    /**
     * 档案类型code
     */
    @TableField("d92_code")
    private String d92Code;

    /**
     * 档案类型名称
     */
    @TableField("d92_name")
    private String d92Name;

    /**
     * 快递订单号等信息
     */
    @TableField("remark")
    private String remark;

    /**
     * 源组织关系数组 用于机构树查询
     */
    @TableField(value = "src_org_relation_rel", typeHandler = JsonbTypeHandler.class)
    private Object srcOrgRelationRel;

    /**
     * 目标组织关系数组
     */
    @TableField(value = "target_org_relation_rel", typeHandler = JsonbTypeHandler.class)
    private Object targetOrgRelationRel;

    /**
     * 是否延长预备期
     */
    @TableField("whether_extend_prep_period")
    private String whetherExtendPrepPeriod;

    @TableField("data_text")
    private String dataText;
    /**
     * 省外转出时间
     */
    @TableField("transfer_out_time")
    private Date transferOutTime;
    /**
     * 转接原因代码
     */
    @TableField("d146_code")
    private String d146Code;

    @TableField("d146_name")
    private String d146Name;
    /**
     * 党员报道日期
     */
    @TableField("report_time")
    private Date reportTime;
    /**
     * 转出党支部得单位性质
     */
    @TableField("out_d04_code")
    private String outD04Code;
    /**
     * 组织层级码
     */
    @TableField(exist = false)
    private String orgCode;
    /**
     * 流程表信息
     */
    @TableField(value = "extra_data_process", typeHandler = JsonbTypeHandler.class)
    private Object extraDataProcess;
    /**
     * 档案表信息
     */
    @TableField(value = "extra_data_digital", typeHandler = JsonbTypeHandler.class)
    private Object extraDataDigital;
    /**
     * 档案日志表信息
     */
    @TableField(value = "extra_data_digital_log", typeHandler = JsonbTypeHandler.class)
    private Object extraDataDigitalLog;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMemId() {
        return memId;
    }

    public void setMemId(String memId) {
        this.memId = memId;
    }

    public String getSrcOrgId() {
        return srcOrgId;
    }

    public void setSrcOrgId(String srcOrgId) {
        this.srcOrgId = srcOrgId;
    }

    public String getSrcOrgName() {
        return srcOrgName;
    }

    public void setSrcOrgName(String srcOrgName) {
        this.srcOrgName = srcOrgName;
    }

    public String getTargetOrgId() {
        return targetOrgId;
    }

    public void setTargetOrgId(String targetOrgId) {
        this.targetOrgId = targetOrgId;
    }

    public String getTargetOrgName() {
        return targetOrgName;
    }

    public void setTargetOrgName(String targetOrgName) {
        this.targetOrgName = targetOrgName;
    }

    public String getCommonOrgId() {
        return commonOrgId;
    }

    public void setCommonOrgId(String commonOrgId) {
        this.commonOrgId = commonOrgId;
    }

    public String getCommonOrgName() {
        return commonOrgName;
    }

    public void setCommonOrgName(String commonOrgName) {
        this.commonOrgName = commonOrgName;
    }

    public Object getSrcOrgRelation() {
        return srcOrgRelation;
    }

    public void setSrcOrgRelation(Object srcOrgRelation) {
        this.srcOrgRelation = srcOrgRelation;
    }

    public Object getTargetOrgRelation() {
        return targetOrgRelation;
    }

    public void setTargetOrgRelation(Object targetOrgRelation) {
        this.targetOrgRelation = targetOrgRelation;
    }

    public String getOutType() {
        return outType;
    }

    public void setOutType(String outType) {
        this.outType = outType;
    }

    public String getInType() {
        return inType;
    }

    public void setInType(String inType) {
        this.inType = inType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCurrentApprovalId() {
        return currentApprovalId;
    }

    public void setCurrentApprovalId(String currentApprovalId) {
        this.currentApprovalId = currentApprovalId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Object getExtraData() {
        return extraData;
    }

    public void setExtraData(Object extraData) {
        this.extraData = extraData;
    }

    public Object getExtraDataLog() {
        return extraDataLog;
    }

    public void setExtraDataLog(Object extraDataLog) {
        this.extraDataLog = extraDataLog;
    }

    public String getLetterUrl() {
        return letterUrl;
    }

    public void setLetterUrl(String letterUrl) {
        this.letterUrl = letterUrl;
    }

    public Integer getEffectMems() {
        return effectMems;
    }

    public void setEffectMems(Integer effectMems) {
        this.effectMems = effectMems;
    }

    public String getD92Code() {
        return d92Code;
    }

    public void setD92Code(String d92Code) {
        this.d92Code = d92Code;
    }

    public String getD92Name() {
        return d92Name;
    }

    public void setD92Name(String d92Name) {
        this.d92Name = d92Name;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Object getSrcOrgRelationRel() {
        return srcOrgRelationRel;
    }

    public void setSrcOrgRelationRel(Object srcOrgRelationRel) {
        this.srcOrgRelationRel = srcOrgRelationRel;
    }

    public Object getTargetOrgRelationRel() {
        return targetOrgRelationRel;
    }

    public void setTargetOrgRelationRel(Object targetOrgRelationRel) {
        this.targetOrgRelationRel = targetOrgRelationRel;
    }

    public String getWhetherExtendPrepPeriod() {
        return whetherExtendPrepPeriod;
    }

    public void setWhetherExtendPrepPeriod(String whetherExtendPrepPeriod) {
        this.whetherExtendPrepPeriod = whetherExtendPrepPeriod;
    }

    public String getDataText() {
        return dataText;
    }

    public void setDataText(String dataText) {
        this.dataText = dataText;
    }

    public Date getTransferOutTime() {
        return transferOutTime;
    }

    public void setTransferOutTime(Date transferOutTime) {
        this.transferOutTime = transferOutTime;
    }

    public String getD146Code() {
        return d146Code;
    }

    public void setD146Code(String d146Code) {
        this.d146Code = d146Code;
    }

    public String getD146Name() {
        return d146Name;
    }

    public void setD146Name(String d146Name) {
        this.d146Name = d146Name;
    }

    public Date getReportTime() {
        return reportTime;
    }

    public void setReportTime(Date reportTime) {
        this.reportTime = reportTime;
    }

    public String getOutD04Code() {
        return outD04Code;
    }

    public void setOutD04Code(String outD04Code) {
        this.outD04Code = outD04Code;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Object getExtraDataProcess() {
        return extraDataProcess;
    }

    public void setExtraDataProcess(Object extraDataProcess) {
        this.extraDataProcess = extraDataProcess;
    }

    public Object getExtraDataDigital() {
        return extraDataDigital;
    }

    public void setExtraDataDigital(Object extraDataDigital) {
        this.extraDataDigital = extraDataDigital;
    }

    public Object getExtraDataDigitalLog() {
        return extraDataDigitalLog;
    }

    public void setExtraDataDigitalLog(Object extraDataDigitalLog) {
        this.extraDataDigitalLog = extraDataDigitalLog;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "ActivistTransferRecord{" +
                "id=" + id +
                ", userId=" + userId +
                ", name=" + name +
                ", memId=" + memId +
                ", srcOrgId=" + srcOrgId +
                ", srcOrgName=" + srcOrgName +
                ", targetOrgId=" + targetOrgId +
                ", targetOrgName=" + targetOrgName +
                ", commonOrgId=" + commonOrgId +
                ", commonOrgName=" + commonOrgName +
                ", srcOrgRelation=" + srcOrgRelation +
                ", targetOrgRelation=" + targetOrgRelation +
                ", outType=" + outType +
                ", inType=" + inType +
                ", type=" + type +
                ", currentApprovalId=" + currentApprovalId +
                ", status=" + status +
                ", reason=" + reason +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", extraData=" + extraData +
                ", letterUrl=" + letterUrl +
                ", effectMems=" + effectMems +
                ", d92Code=" + d92Code +
                ", d92Name=" + d92Name +
                ", remark=" + remark +
                ", srcOrgRelationRel=" + srcOrgRelationRel +
                ", targetOrgRelationRel=" + targetOrgRelationRel +
                ", whetherExtendPrepPeriod=" + whetherExtendPrepPeriod +
                ", dataText=" + dataText +
                ", transferOutTime=" + transferOutTime +
                ", d146Code=" + d146Code +
                ", d146Name=" + d146Name +
                ", reportTime=" + reportTime +
                ", outD04Code=" + outD04Code +
                "}";
    }
}
