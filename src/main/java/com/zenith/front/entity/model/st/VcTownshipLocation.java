package com.zenith.front.entity.model.st;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 乡（镇）情简介-地理位置
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-17
 */
@TableName("vc_township_location")
public class VcTownshipLocation extends Model<VcTownshipLocation> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "code", type = IdType.ASSIGN_UUID)
    private String code;

    /**
     * 关联组织code
     */
    @TableField("org_code")
    private String orgCode;

    @TableField("org_name")
    private String orgName;

    /**
     * 单位code
     */
    @TableField("unit_code")
    private String unitCode;

    /**
     * 填报时间
     */
    @TableField("report_time")
    private Date reportTime;

    /**
     * 距镇中心（公里）
     */
    @TableField("distance_town_center")
    private Integer distanceTownCenter;

    /**
     * 距县城（公里）
     */
    @TableField("distance_county")
    private Integer distanceCounty;

    /**
     * 距最近的城市（公里）
     */
    @TableField("distance_city")
    private Integer distanceCity;

    /**
     * 距最近的高速入口（公里）
     */
    @TableField("distance_expressway_entrance")
    private Integer distanceExpresswayEntrance;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private Date updateTime;

    @TableField("delete_time")
    private Date deleteTime;


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public Date getReportTime() {
        return reportTime;
    }

    public void setReportTime(Date reportTime) {
        this.reportTime = reportTime;
    }

    public Integer getDistanceTownCenter() {
        return distanceTownCenter;
    }

    public void setDistanceTownCenter(Integer distanceTownCenter) {
        this.distanceTownCenter = distanceTownCenter;
    }

    public Integer getDistanceCounty() {
        return distanceCounty;
    }

    public void setDistanceCounty(Integer distanceCounty) {
        this.distanceCounty = distanceCounty;
    }

    public Integer getDistanceCity() {
        return distanceCity;
    }

    public void setDistanceCity(Integer distanceCity) {
        this.distanceCity = distanceCity;
    }

    public Integer getDistanceExpresswayEntrance() {
        return distanceExpresswayEntrance;
    }

    public void setDistanceExpresswayEntrance(Integer distanceExpresswayEntrance) {
        this.distanceExpresswayEntrance = distanceExpresswayEntrance;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    @Override
    protected Serializable pkVal() {
        return this.code;
    }

    @Override
    public String toString() {
        return "VcTownshipLocation{" +
                "code=" + code +
                ", orgCode=" + orgCode +
                ", orgName=" + orgName +
                ", unitCode=" + unitCode +
                ", reportTime=" + reportTime +
                ", distanceTownCenter=" + distanceTownCenter +
                ", distanceCounty=" + distanceCounty +
                ", distanceCity=" + distanceCity +
                ", distanceExpresswayEntrance=" + distanceExpresswayEntrance +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deleteTime=" + deleteTime +
                "}";
    }
}
