package com.zenith.front.entity.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("sys_role")
public class Role extends Model<Role> {

    private static final long serialVersionUID=1L;

    /**
     * 角色id,uuid,不能为null
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 角色名称,不能为null
     */
    @TableField("name")
    private String name;

    /**
     * 权限码1001011,不能为null,默认权限
     */
    @TableField("permission")
    private String permission;

    /**
     * 有效时间,内置角色有效时间为永久值为null
     */
    @TableField("valid_time")
    private Date validTime;

    /**
     * 创建时间,默认不为null
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间,默认为createTime,不能为null
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 角色层级码
     */
    @TableField("parent_id")
    private String parentId;

    /**
     * 创建者,角色account
     */
    @TableField("create_account")
    private String createAccount;

    /**
     * 角色类型,参照role_type
     */
    @TableField("role_type_code")
    private Integer roleTypeCode;

    /**
     * 修改者账号
     */
    @TableField("update_account")
    private String updateAccount;

    /**
     * 是否被删除0未删除,1删除
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 自定义角色的创建者组织code
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 自定义角色的组织id
     */
    @TableField("org_id")
    private String orgId;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPermission() {
        return permission;
    }

    public void setPermission(String permission) {
        this.permission = permission;
    }

    public Date getValidTime() {
        return validTime;
    }

    public void setValidTime(Date validTime) {
        this.validTime = validTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getCreateAccount() {
        return createAccount;
    }

    public void setCreateAccount(String createAccount) {
        this.createAccount = createAccount;
    }

    public Integer getRoleTypeCode() {
        return roleTypeCode;
    }

    public void setRoleTypeCode(Integer roleTypeCode) {
        this.roleTypeCode = roleTypeCode;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "Role{" +
        "id=" + id +
        ", name=" + name +
        ", permission=" + permission +
        ", validTime=" + validTime +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        ", parentId=" + parentId +
        ", createAccount=" + createAccount +
        ", roleTypeCode=" + roleTypeCode +
        ", updateAccount=" + updateAccount +
        ", isDelete=" + isDelete +
        ", orgCode=" + orgCode +
        ", orgId=" + orgId +
        "}";
    }
}
