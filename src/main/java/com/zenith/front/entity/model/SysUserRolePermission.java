package com.zenith.front.entity.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("sys_user_role_permission")
public class SysUserRolePermission extends Model<SysUserRolePermission> {

    private static final long serialVersionUID=1L;

    /**
     * uuid不能为null
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 用户id不能为null
     */
    @TableField("user_id")
    private String userId;

    /**
     * 角色id不能为null
     */
    @TableField("role_id")
    private String roleId;

    /**
     * 权限码不能为null,第一次创建权限码为角色默认权限码
     */
    @TableField("permission")
    private String permission;

    /**
     * 管理的组织层级码id,不能为null
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 创建时间不能为null
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间不能为null,默认为createTime
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 修改者账号
     */
    @TableField("update_account")
    private String updateAccount;

    /**
     * 创建者账号
     */
    @TableField("create_account")
    private String createAccount;

    /**
     * 管理组织id
     */
    @TableField("org_id")
    private String orgId;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getPermission() {
        return permission;
    }

    public void setPermission(String permission) {
        this.permission = permission;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    public String getCreateAccount() {
        return createAccount;
    }

    public void setCreateAccount(String createAccount) {
        this.createAccount = createAccount;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "UserRolePermission{" +
                "id=" + id +
                ", userId=" + userId +
                ", roleId=" + roleId +
                ", permission=" + permission +
                ", orgCode=" + orgCode +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", updateAccount=" + updateAccount +
                ", createAccount=" + createAccount +
                ", orgId=" + orgId +
                "}";
    }
}
