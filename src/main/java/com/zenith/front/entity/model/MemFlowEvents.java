package com.zenith.front.entity.model;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025-02-11 09:49
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "mem_flow_events", autoResultMap = true)
public class MemFlowEvents extends Model<MemFlowEvents> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private String id;

    /**
     * 流动党员登记code
     */
    @TableField("mem_flow_code")
    private String memFlowCode;

    /**
     * 流动党员code
     */
    @TableField("mem_code")
    private String memCode;

    /**
     * 活动类型（0组织活动 1志愿服务）
     */
    @TableField("event_type")
    private String eventType;

    /**
     * 活动名称或者志愿服务名称
     */
    @TableField("event_name")
    private String eventName;

    /**
     * 活动情况说明
     */
    @TableField("event_explain")
    private String eventExplain;

    /**
     * 活动类型详情
     */
    @TableField("event_details")
    private String eventDetails;

    /**
     * 活动日期
     */
    @TableField("event_date")
    private Date eventDate;

    /**
     * 维护日期
     */
    @TableField("maintain_date")
    private Date maintainDate;

    /**
     * 填写人ID
     */
    @TableField("filler_user_id")
    private String fillerUserId;

    /**
     * 填写人名称
     */
    @TableField("filler_user_name")
    private String fillerUserName;

    /**
     * 填写党组织ID
     */
    @TableField("filler_org_id")
    private String fillerOrgId;

    /**
     * 填写党组织code
     */
    @TableField("filler_org_code")
    private String fillerOrgCode;

    /**
     * 填写党组织名称
     */
    @TableField("filler_org_name")
    private String fillerOrgName;

    /**
     * 填写日期
     */
    @TableField("filler_date")
    private Date fillerDate;

    /**
     * 流出走向 （消息走向：1流出方发送 2流入方发送）
     */
    @TableField("flow_direction")
    private String flowDirection;

    /**
     * 流出方根节点代码
     */
    @TableField("outflow_node_code")
    private String outflowNodeCode;

    /**
     * 流入方根节点代码
     */
    @TableField("inflow_node_code")
    private String inflowNodeCode;

    /**
     * 1-本地创建  2-交换区数据
     */
    @TableField(value = "source_type")
    private Integer sourceType;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private Date updateTime;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;
}
