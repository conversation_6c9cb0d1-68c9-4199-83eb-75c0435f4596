package com.zenith.front.entity.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * @author: D.watermelon
 * @date: 2023/5/17 9:35
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 */


@TableName("transfer_month_statistics")
@Data
public class TransferMonthStatistics extends Model<TransferMonthStatistics> {

    /**
     * 唯一标识符
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String ID;

    /**
     * 省部级建库单位根节点党组织代码。
     */
    @TableField(value = "zjtj0001")
    private String ZJTJ0001;

    /**
     * 上报数据的所属年月
     */
    @TableField(value = "zjtj0002")
    private Date ZJTJ0002;

    /**
     * 上报党组织的唯一标识，如果是根节点则标为“-1”
     */
    @TableField(value = "zjtj0003")
    private String ZJTJ0003;

    /**
     * 上报党组织的简称，上报统计数的组织为省级和省直属下一级的党组织。
     */
    @TableField(value = "zjtj0004")
    private String ZJTJ0004;

    /**
     * 该党组织的上一级党组织唯一标识（UUID）
     */
    @TableField(value = "zjtj0005")
    private String ZJTJ0005;

    /**
     * 支部报到时间在上报月份内，本组织管理范围内，办理线下转入业务已完成的人次数
     */
    @TableField(value = "zjtj0013")
    private Integer ZJTJ0013;

    /**
     * 转出发起时间在上报月份内，本组织管理范围内，办理省内转出业务的人次数
     */
    @TableField(value = "zjtj0014")
    private Integer ZJTJ0014;

    /**
     * 支部报到时间在上报月份内，本组织管理范围内，省内转出业务正常办理结束的人次数
     */
    @TableField(value = "zjtj0015")
    private Integer ZJTJ0015;

    /**
     * 本组织管理范围内，省内转出党员被退回，退回确认时间在上报月份内的人次数
     */
    @TableField(value = "zjtj0016")
    private Integer ZJTJ0016;

    /**
     * 转出撤销时间在上报月份内，本组织管理范围内，省内转出业务由转出方撤销的人次数
     */
    @TableField(value = "zjtj0030")
    private Integer ZJTJ0030;

    /**
     * 转出超时退回时间在上报月份内，本组织管理范围内，省内转出业务超时退回的人次数
     */
    @TableField(value = "zjtj0031")
    private Integer ZJTJ0031;

    /**
     * 转出发起时间在上报月份内，本组织管理范围内，在系统内登记办理转出类型为解放军、武装警察部队和其他等非系统建库单位的人次数
     */
    @TableField(value = "zjtj0021")
    private Integer ZJTJ0021;

    /**
     * 本组织管理范围内，在系统内登记办理转出类型为解放军、武装警察部队和其他等非系统建库单位的人次数，从转出发起日期起计算，上报月份内，超过90天仍未填写回执的人次数
     */
    @TableField(value = "zjtj0032")
    private Integer ZJTJ0032;

    /**
     * 上报党组织所在地的行政区划，具体到市级的行政区划代码
     */
    @TableField(value = "zjtj0026")
    private String ZJTJ0026;

    /**
     * 上报党组织所在地的行政区划，具体到市级的行政区划名称
     */
    @TableField(value = "zjtj0027")
    private String ZJTJ0027;

    /**
     * 该记录上传业务交换区的日期
     */
    @TableField(value = "zjtj0028")
    private Date ZJTJ0028;

    /**
     * 在本级党组织展示时使用的排序号
     */
    @TableField(value = "zjtj0029")
    private Integer ZJTJ0029;

    /**
     * 上报数据类别（1.下级节点上报，2.上报中组部节点）
     */
    @TableField(value = "zjtj0033")
    private Integer ZJTJ0033;

    /**
     * 上报状态（0，待上报，1，上报成功，2上报失败）
     */
    @TableField(value = "zjtj0034")
    private Integer ZJTJ0034;

    /**
     * 上报时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 删除时间
     */
    @TableField(value = "delete_time")
    private Date deleteTime;


    /**
     * 数据节点key
     */
    @TableField(value = "zjtj0035")
    private String ZJTJ0035;

    /**
     * 上报组织编码
     */
    @TableField(value = "zjtj0036")
    private String ZJTJ0036;

    /**
     * 上报组织名称
     */
    @TableField(value = "zjtj0037")
    private String ZJTJ0037;
}
