package com.zenith.front.entity.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.zenith.front.annotation.DynamicTable;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-10
 */
@DynamicTable
@TableName("ccp_mem_many")
public class MemMany extends Model<MemMany> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("code")
    private String code;

    @TableField("mem_code")
    private String memCode;

    @TableField("mem_name")
    private String memName;

    @TableField("idcard")
    private String idcard;

    /**
     * 党员民族code
     */
    @TableField("d06_code")
    private String d06Code;

    /**
     * 党员民族code
     */
    @TableField("d06_name")
    private String d06Name;

    /**
     * 性别
     */
    @TableField("sex_code")
    private String sexCode;

    @TableField("sex_name")
    private String sexName;

    /**
     * 人员类型
     */
    @TableField("d08_code")
    private String d08Code;

    @TableField("d08_name")
    private String d08Name;

    @TableField("d48_name")
    private String d48Name;

    /**
     * 党员自身所在组织code
     */
    @TableField("mem_curr_org_code")
    private String memCurrOrgCode;

    /**
     * 党员自身所在组织名称
     */
    @TableField("mem_curr_org_name")
    private String memCurrOrgName;

    /**
     * 党员自身所在组织名称
     */
    @TableField("mem_curr_many_org_code")
    private String memCurrManyOrgCode;

    /**
     * 党员进入的组织code
     */
    @TableField("join_org_code")
    private String joinOrgCode;

    /**
     * 党员进入的组织code
     */
    @TableField("join_org_name")
    private String joinOrgName;

    /**
     * 党员进入的组织层级码
     */
    @TableField("join_many_org_code")
    private String joinManyOrgCode;

    /**
     * 进入管理类型code
     */
    @TableField("join_org_type_code")
    private String joinOrgTypeCode;

    /**
     * 进入管理类型名称
     */
    @TableField("join_org_type_name")
    private String joinOrgTypeName;

    /**
     * 进入组织日期
     */
    @TableField("join_org_date")
    private Date joinOrgDate;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    @TableField("timestamp")
    private Date timestamp;

    @TableField("delete_time")
    private Date deleteTime;

    @TableField("zb_code")
    private String zbCode;

    /**
     * 籍贯
     */
    @TableField("d48_code")
    private String d48Code;

    @TableField("is_history")
    private Integer isHistory;

    @TableField("update_account")
    private String updateAccount;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMemCode() {
        return memCode;
    }

    public void setMemCode(String memCode) {
        this.memCode = memCode;
    }

    public String getMemName() {
        return memName;
    }

    public void setMemName(String memName) {
        this.memName = memName;
    }

    public String getIdcard() {
        return idcard;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }

    public String getd06Code() {
        return d06Code;
    }

    public void setd06Code(String d06Code) {
        this.d06Code = d06Code;
    }

    public String getd06Name() {
        return d06Name;
    }

    public void setd06Name(String d06Name) {
        this.d06Name = d06Name;
    }

    public String getSexCode() {
        return sexCode;
    }

    public void setSexCode(String sexCode) {
        this.sexCode = sexCode;
    }

    public String getSexName() {
        return sexName;
    }

    public void setSexName(String sexName) {
        this.sexName = sexName;
    }

    public String getd08Code() {
        return d08Code;
    }

    public void setd08Code(String d08Code) {
        this.d08Code = d08Code;
    }

    public String getd08Name() {
        return d08Name;
    }

    public void setd08Name(String d08Name) {
        this.d08Name = d08Name;
    }

    public String getd48Name() {
        return d48Name;
    }

    public void setd48Name(String d48Name) {
        this.d48Name = d48Name;
    }

    public String getMemCurrOrgCode() {
        return memCurrOrgCode;
    }

    public void setMemCurrOrgCode(String memCurrOrgCode) {
        this.memCurrOrgCode = memCurrOrgCode;
    }

    public String getMemCurrOrgName() {
        return memCurrOrgName;
    }

    public void setMemCurrOrgName(String memCurrOrgName) {
        this.memCurrOrgName = memCurrOrgName;
    }

    public String getMemCurrManyOrgCode() {
        return memCurrManyOrgCode;
    }

    public void setMemCurrManyOrgCode(String memCurrManyOrgCode) {
        this.memCurrManyOrgCode = memCurrManyOrgCode;
    }

    public String getJoinOrgCode() {
        return joinOrgCode;
    }

    public void setJoinOrgCode(String joinOrgCode) {
        this.joinOrgCode = joinOrgCode;
    }

    public String getJoinOrgName() {
        return joinOrgName;
    }

    public void setJoinOrgName(String joinOrgName) {
        this.joinOrgName = joinOrgName;
    }

    public String getJoinManyOrgCode() {
        return joinManyOrgCode;
    }

    public void setJoinManyOrgCode(String joinManyOrgCode) {
        this.joinManyOrgCode = joinManyOrgCode;
    }

    public String getJoinOrgTypeCode() {
        return joinOrgTypeCode;
    }

    public void setJoinOrgTypeCode(String joinOrgTypeCode) {
        this.joinOrgTypeCode = joinOrgTypeCode;
    }

    public String getJoinOrgTypeName() {
        return joinOrgTypeName;
    }

    public void setJoinOrgTypeName(String joinOrgTypeName) {
        this.joinOrgTypeName = joinOrgTypeName;
    }

    public Date getJoinOrgDate() {
        return joinOrgDate;
    }

    public void setJoinOrgDate(Date joinOrgDate) {
        this.joinOrgDate = joinOrgDate;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public String getZbCode() {
        return zbCode;
    }

    public void setZbCode(String zbCode) {
        this.zbCode = zbCode;
    }

    public String getd48Code() {
        return d48Code;
    }

    public void setd48Code(String d48Code) {
        this.d48Code = d48Code;
    }

    public Integer getIsHistory() {
        return isHistory;
    }

    public void setIsHistory(Integer isHistory) {
        this.isHistory = isHistory;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "MemMany{" +
                "id=" + id +
                ", code=" + code +
                ", memCode=" + memCode +
                ", memName=" + memName +
                ", idcard=" + idcard +
                ", d06Code=" + d06Code +
                ", d06Name=" + d06Name +
                ", sexCode=" + sexCode +
                ", sexName=" + sexName +
                ", d08Code=" + d08Code +
                ", d08Name=" + d08Name +
                ", d48Name=" + d48Name +
                ", memCurrOrgCode=" + memCurrOrgCode +
                ", memCurrOrgName=" + memCurrOrgName +
                ", memCurrManyOrgCode=" + memCurrManyOrgCode +
                ", joinOrgCode=" + joinOrgCode +
                ", joinOrgName=" + joinOrgName +
                ", joinManyOrgCode=" + joinManyOrgCode +
                ", joinOrgTypeCode=" + joinOrgTypeCode +
                ", joinOrgTypeName=" + joinOrgTypeName +
                ", joinOrgDate=" + joinOrgDate +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", timestamp=" + timestamp +
                ", deleteTime=" + deleteTime +
                ", zbCode=" + zbCode +
                ", d48Code=" + d48Code +
                ", isHistory=" + isHistory +
                ", updateAccount=" + updateAccount +
                "}";
    }
}
