package com.zenith.front.entity.dto.st;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
* <p>
* 第三方系统用户表 列表查询DTO
* </p>
*
* <AUTHOR>
* @date 2023-06-01 16:14:49
*/
@Data
@ApiModel("第三方系统用户表 列表查询-VcSysUserListDTO")
public class VcSysUserListDTO {
    private static final long serialVersionUID = 2654049116658581199L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;

    private String orgCode;
}
