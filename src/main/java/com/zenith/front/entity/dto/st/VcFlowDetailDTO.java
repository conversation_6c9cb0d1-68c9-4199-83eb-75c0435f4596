package com.zenith.front.entity.dto.st;

import lombok.Data;

import java.util.Date;

/**
* <p>
* 流程审核主表 DTO
* </p>
*
* <AUTHOR>
* @date 2023-05-29 14:14:51
*/
@Data
public class VcFlowDetailDTO {
    private static final long serialVersionUID = 4267182989350385788L;

    /**
    *主键
    */
    private String id;
    /**
    *流程主键
    */
    private String flowId;
    /**
    *审核时间
    */
    private Date auditTime;
    /**
    *吧柠檬味荣誉4新市场打个包他9 吗，
    */
    private String auditUser;
    /**
    *审核人ID
    */
    private String auditUserId;
    /**
    *审核状态，1-待审核，2-通过，3-驳回，4-撤销
    */
    private String auditStatus;
    /**
    *审核用户的类型，1-指定用户，2-指定角色，3--指定部门
    */
    private String auditUserType;
    /**
    *当前审核节点
    */
    private Integer nowPoint;
    /**
    *审核意见
    */
    private String auditOpinion;
    /**
    *审核单位
    */
    private String auditUserUnit;
    /**
    *备注
    */
    private String remark;
    /**
    *流程是否结束
    */
    private Integer processEnd;
    /**
    *
    */
    private Date createTime;
    /**
    *
    */
    private Date updateTime;
    /**
    *
    */
    private Date deleteTime;

}

