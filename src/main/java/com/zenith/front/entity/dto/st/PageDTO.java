package com.zenith.front.entity.dto.st;

import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2019/4/33:16 PM
 */
public class PageDTO {
    /***
     * 页码
     * */
    @Min(value = 1, message = "页码最小为1")
    // @NotNull(message = "页码不能为空")
    private Integer pageNum;
    /***
     * 页码
     * */
    @Min(value = 1, message = "页码最小为1")
    // @NotNull(message = "页码不能为空")
    private Integer pageNumber;
    /***
     * 页大小
     * */
    @Range(min = 0, max = 100, message = "页大小范围在1-100")
    // @NotNull(message = "页大小不能为空")
    private Integer pageSize;
    /***
     * 需要分页的编码 如组织code 等
     * */
    private String code;

    /**
     * 账号 根据账号搜索
     */
    private String account;

    private String keyWord;

    /**
     * 机构层级码
     */
    @NotBlank(message = "orgLevelCode 不能为空")
    private String orgLevelCode;

    /**
     * 农村党建 角色code
     */
    private String vcRoleCode;

    public String getOrgLevelCode() {
        return orgLevelCode;
    }

    public void setOrgLevelCode(String orgLevelCode) {
        this.orgLevelCode = orgLevelCode;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getKeyWord() {
        return keyWord;
    }

    public PageDTO setKeyWord(String keyWord) {
        this.keyWord = keyWord;
        return this;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getVcRoleCode() {
        return vcRoleCode;
    }

    public void setVcRoleCode(String vcRoleCode) {
        this.vcRoleCode = vcRoleCode;
    }
}
