package com.zenith.front.entity.dto.st;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* <p>
* 第三方系统用户表 DTO
* </p>
*
* <AUTHOR>
* @date 2023-06-01 16:14:49
*/
@Data
@ApiModel("第三方系统用户表 请求实体-VcSysUserDTO")
public class VcSysUserDTO {
    private static final long serialVersionUID = 3367685397337622808L;

    /**
    *主键
    */
    @ApiModelProperty(value = "主键")
    private String id;
    /**
    *系统用户主键
    */
    @ApiModelProperty(value = "系统用户主键")
    private String userId;
    /**
    *系统权限code
    */
    @ApiModelProperty(value = "系统权限code")
    private String roleCode;
    /**
    *系统code
    */
    @ApiModelProperty(value = "系统code")
    private String systemCode;
    /**
    *机构下是否有组团式帮扶
    */
    @ApiModelProperty(value = "机构下是否有组团式帮扶")
    private Integer isGroupHelp;
    /**
    *组团式帮扶机构code
    */
    @ApiModelProperty(value = "组团式帮扶机构code")
    private String groupHelpCode;

    /**
     * 农村党建所属单位code
     */
    @ApiModelProperty(value = "农村党建所属单位code")
    private String vcOrgCode;
    /**
     * 农村党建所属单位层级码
     */
    @ApiModelProperty(value = "农村党建所属单位层级码")
    private String vcOrgLevelCode;

    /**
     * 党建系统所属单位code
     */
    @ApiModelProperty(value = "党建系统所属单位code")
    private String originOrgCode;
    /**
     * 党建系统所属单位层级码
     */
    @ApiModelProperty(value = "党建系统所属单位层级码")
    private String originOrgLevelCode;

}

