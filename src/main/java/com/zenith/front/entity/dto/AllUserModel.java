package com.zenith.front.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * @author: D.watermelon
 * @date: 2021/11/11 1:11
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class AllUserModel {
    private String data_key;
    private String account;
    private String id;
    private String exchange_user_login;
    /**
     * 创建时间
     */
    private Date create_time;
    /**
     * 管理组织组织层级码
     */
    private String org_level_code;
    /**
     * 管理组织名称
     */
    private String org_name;
    /**
     * 权限码1001011
     */
    private String permission;
    /**
     * 0 未删除 1已删除 默认为0
     */
    private Integer is_delete;

}
