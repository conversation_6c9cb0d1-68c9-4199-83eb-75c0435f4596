package com.zenith.front.entity.dto.st;

import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 请销假申请 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-31 15:34:14
 */
@Data
public class VcPleasDestroyHolidayDTO {
    private static final long serialVersionUID = 2089429145296417229L;

    /**
     * 主键
     */
    private String id;
    /**
     * 请假人干部名册ID
     */
    private String vcMemStationId;
    /**
     * 请假类型 1-事假、2-病假、3-年休假、4-婚假、5-产假、6-调休假、7-其他
     */
    private String vcDictD200Code;
    /**
     * 请假类型中文
     */
    private String vcDictD200Name;
    /**
     * 请假开始时间
     */
    private Date holidayStartDate;
    /**
     * 请假结束时间
     */
    private Date holidayEndDate;
    /**
     * 请假天数
     */
    private Integer days;
    /**
     * 是否离开驻地 0-否、1-是
     */
    private Integer leaveStation;
    /**
     * 目的地
     */
    private String destination;
    /**
     * 附件上传
     */
    private String file;
    /**
     * 请假事由
     */
    private String holidayCause;
    /**
     * 审批人
     */
    private String approveUserName;
    /**
     * 审批人ID
     */
    private String approveUserId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 删除时间
     */
    private Date deleteTime;

    /**
     * 乡名称
     */
    private String townshipName;

    /**
     * 请假人
     */
    private String memName;

    /**
     * 请假人单位
     */
    private String orgName;
    /**
     * 请假人单位层级
     */
    private String orgLevelCode;
    /**
     * 请假人单位id
     */
    private String orgId;

    /**
     * 数据节点来源key
     */
    private String nodeKey;
}

