package com.zenith.front.entity.dto;

import com.zenith.front.entity.model.OrgFlow;
import io.swagger.models.auth.In;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
public class SyncOrgFlowDTO {
    private String nginxKey;
    private OrgFlowSyncAll orgFlowSyncAll;
    private Integer pageNum;
    private Integer pageSize;
    private List<String> idList;

}
