package com.zenith.front.entity.dto.st;

import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 流程的详情信息 DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-29 14:14:51
 */
@Data
public class VcFlowInfoDTO {
    private static final long serialVersionUID = 2441608767598539300L;

    /**
     * 主键
     */
    private String id;
    /**
     * 流程类型
     */
    private String flowTypeCode;
    /**
     * 业务主键
     */
    private String bizNo;
    /**
     * 审核时间
     */
    private Date auditTime;
    /**
     * 审核人
     */
    private String auditUser;
    /**
     * 审核人ID
     */
    private String auditUserId;
    /**
     * 审核状态，1-待审核，2-通过，3-驳回，4-撤销
     */
    private String auditStatus;
    /**
     * 审核用户的类型，1-指定用户，2-指定角色，3--指定部门
     */
    private String auditUserType;
    /**
     * 流程总的审批节点数
     */
    private Integer totalPoint;
    /**
     * 当前审批节点
     */
    private Integer nowPoint;
    /**
     * 备注
     */
    private String remark;
    /**
     * 审核意见
     */
    private String auditOpinion;
    /**
     * 审核单位
     */
    private String auditUserUnit;
    /**
     * 流程是否结束
     */
    private Integer processEnd;
    /**
     *
     */
    private Date createTime;
    /**
     *
     */
    private Date updateTime;
    /**
     *
     */
    private Date deleteTime;
    /**
     * 流程发起时间
     */
    private Date startTime;

    /**
     * 流程发起人
     */
    private String initiateUser;
    /**
     * 流程发起人id
     */
    private String initiateUserId;

    /**
     * 下一步流程发起人姓名
     */
    private String nextAuditUser;
    /**
     * 下一步流程发起人姓名
     */
    private String nextAuditUserId;
    /**
     * 下一步流程发起人姓名
     */
    private String nextAuditUserType;
    /**
     * 下一步流程发起人姓名
     */
    private String NextAuditUserUnit;

    /**
     * 开始流程前函数接口
     */
    private String StartFLowBeforeFunc;
    /**
     * 开始流程后函数接口
     */
    private String StartFLowAfterFunc;
    /**
     * 流程流转前函数接口
     */
    private String NextFLowBeforeFunc;
    /**
     * 流程流转后函数接口
     */
    private String NextFLowAfterFunc;
    /**
     * 流程结束前函数接口
     */
    private String EndFlowBeforeFunc;
    /**
     * 流程结束后函数接口
     */
    private String EndFlowAfterFunc;


    /**
     * 关联业务所属人
     */
    private String createUser;
    /**
     * 关联业务所属人ID
     */
    private String createUserId;

    /**
     *  节点来源
     */
    private String nodeKey;

}

