package com.zenith.front.entity.dto;

public class ToAuthorizationDTO {

    private String account;

    /**
     * 补录
     */
    private String recording;

    /**
     * 错误录入
     */
    private String wrongEntry;

    /**
     * 确定预备党员
     */
    private String probationaryPartyMember;

    /**
     * 线下组织关系转接
     */
    private String offlineTransfer;

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getRecording() {
        return recording;
    }

    public void setRecording(String recording) {
        this.recording = recording;
    }

    public String getWrongEntry() {
        return wrongEntry;
    }

    public void setWrongEntry(String wrongEntry) {
        this.wrongEntry = wrongEntry;
    }

    public String getProbationaryPartyMember() {
        return probationaryPartyMember;
    }

    public void setProbationaryPartyMember(String probationaryPartyMember) {
        this.probationaryPartyMember = probationaryPartyMember;
    }

    public String getOfflineTransfer() {
        return offlineTransfer;
    }

    public void setOfflineTransfer(String offlineTransfer) {
        this.offlineTransfer = offlineTransfer;
    }

    @Override
    public String toString() {
        return "ToAuthorizationDTO{" +
                "account='" + account + '\'' +
                ", recording='" + recording + '\'' +
                ", wrongEntry='" + wrongEntry + '\'' +
                ", probationaryPartyMember='" + probationaryPartyMember + '\'' +
                ", offlineTransfer='" + offlineTransfer + '\'' +
                '}';
    }
}
