package com.zenith.front.entity.dto.st;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * 请销假申请 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-31 15:34:14
 */
@Data
public class VcPleasDestroyHolidayListDTO {
    private static final long serialVersionUID = 121747186631162069L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;

    @NotNull(message = "orgLevelCode 不能为空")
    private String orgLevelCode;
    /**
     * 审核状态，1-待审核，2-通过，3-驳回，4-撤销
     */
    private String auditStatus;
    /**
     * 姓名
     */
    private String name;
    /**
     * 请假开始时间
     */
    private Date holidayStartDate;
    /**
     * 请假结束时间
     */
    private Date holidayEndDate;
    /**
     * 请假类型 1-事假、2-病假、3-年休假、4-婚假、5-产假、6-调休假、7-其他
     */
    private String vcDictD200Code;
    /**
     * 请假天数起
     */
    private Integer startDays;
    /**
     * 请假天数止
     */
    private Integer endDays;

    /**
     * 流程方面参数
     */
    @JsonIgnore
    private VcFlowInfoQueryDTO flowDto;




    //

    /**
     * 是否请销假统计 true是 false否
     */
    private Boolean statistics;

    /**
     * 是否反查
     */
    private Boolean backward;

    /**
     * 是否内网
     */
    @JsonIgnore
    private Boolean isInternal;

    public Date getHolidayStartDate() {
        if(Objects.nonNull(holidayStartDate)){
            holidayStartDate = DateUtil.beginOfDay(holidayStartDate);
        }
        return holidayStartDate;
    }

    public Date getHolidayEndDate() {
        if(Objects.nonNull(holidayEndDate)){
            holidayEndDate = DateUtil.endOfDay(holidayEndDate);
        }
        return holidayEndDate;
    }
}
