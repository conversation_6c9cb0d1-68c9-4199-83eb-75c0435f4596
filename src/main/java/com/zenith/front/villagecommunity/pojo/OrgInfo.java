package com.zenith.front.villagecommunity.pojo;

/**
 * 机构信息
 *
 * <AUTHOR>
 * @since 2022/8/22 9:33
 */
public class OrgInfo {

    /**
     * 组织主键
     */
    private String code;

    /**
     * 组织名称
     */
    private String name;

    /**
     * 组织层级
     */
    private String orgCode;

    /**
     * 组织类别
     */
    private String d01Code;

    /**
     * 组织类别名称
     */
    private String d01Name;

    /**
     * 父级组织主键
     */
    private String parentCode;

    /**
     * 联系人
     */
    private String contacter;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 党组织书记名称
     */
    private String secretary;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getD01Code() {
        return d01Code;
    }

    public void setD01Code(String d01Code) {
        this.d01Code = d01Code;
    }

    public String getD01Name() {
        return d01Name;
    }

    public void setD01Name(String d01Name) {
        this.d01Name = d01Name;
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public String getContacter() {
        return contacter;
    }

    public void setContacter(String contacter) {
        this.contacter = contacter;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getSecretary() {
        return secretary;
    }

    public void setSecretary(String secretary) {
        this.secretary = secretary;
    }

    @Override
    public String toString() {
        return "OrgInfo{" +
                "code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", orgCode='" + orgCode + '\'' +
                ", d01Code='" + d01Code + '\'' +
                ", d01Name='" + d01Name + '\'' +
                ", parentCode='" + parentCode + '\'' +
                ", contacter='" + contacter + '\'' +
                ", contactPhone='" + contactPhone + '\'' +
                ", secretary='" + secretary + '\'' +
                '}';
    }
}
