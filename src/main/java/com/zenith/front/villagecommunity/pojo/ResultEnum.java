package com.zenith.front.villagecommunity.pojo;

/**
 * <AUTHOR>
 * @since 2022/2/24 17:13
 */
public enum ResultEnum implements ResultConstant {

    FAILED(-1, "操作失败"),
    SUCCESS(200, "操作成功"),

    ;

    ResultEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private int code;

    private String msg;

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public String getMsg() {
        return msg;
    }

    @Override
    public void setMsg(String msg) {
        this.msg = msg;
    }
}
