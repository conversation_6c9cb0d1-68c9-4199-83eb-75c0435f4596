package com.zenith.front.villagecommunity.pojo;

/**
 * 机构信息
 *
 * <AUTHOR>
 * @since 2022/8/22 9:33
 */
public class UnitInfo {

    /**
     * 单位主键
     */
    private String code;

    /**
     * 单位名称
     */
    private String name;

    /**
     * 单位类别
     */
    private String d04Code;

    /**
     * 单位类别名称
     */
    private String d04Name;

    /**
     * 单位隶属关系
     */
    private String d35Code;

    /**
     * 单位隶属关系名称
     */
    private String d35Name;

    /**
     * 是否法人单位标识：1是，0否
     */
    private Integer isLegal;

    /**
     * 单位代码
     */
    private String creditCode;

    /**
     * 组织主键
     */
    private String orgCode;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getD04Code() {
        return d04Code;
    }

    public void setD04Code(String d04Code) {
        this.d04Code = d04Code;
    }

    public String getD04Name() {
        return d04Name;
    }

    public void setD04Name(String d04Name) {
        this.d04Name = d04Name;
    }

    public String getD35Code() {
        return d35Code;
    }

    public void setD35Code(String d35Code) {
        this.d35Code = d35Code;
    }

    public String getD35Name() {
        return d35Name;
    }

    public void setD35Name(String d35Name) {
        this.d35Name = d35Name;
    }

    public Integer getIsLegal() {
        return isLegal;
    }

    public void setIsLegal(Integer isLegal) {
        this.isLegal = isLegal;
    }

    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    @Override
    public String toString() {
        return "UnitInfo{" +
                "code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", d04Code='" + d04Code + '\'' +
                ", d04Name='" + d04Name + '\'' +
                ", d35Code='" + d35Code + '\'' +
                ", d35Name='" + d35Name + '\'' +
                ", isLegal=" + isLegal +
                ", creditCode='" + creditCode + '\'' +
                ", orgCode='" + orgCode + '\'' +
                '}';
    }
}
