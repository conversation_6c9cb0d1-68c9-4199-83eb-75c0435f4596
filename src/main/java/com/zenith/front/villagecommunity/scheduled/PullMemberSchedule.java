package com.zenith.front.villagecommunity.scheduled;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.villagecommunity.model.VcMemInfo;
import com.zenith.front.villagecommunity.model.VillageCommunityMemberDTO;
import com.zenith.front.villagecommunity.client.VillageCommunityClient;
import com.zenith.front.villagecommunity.pojo.Result;
import com.zenith.front.villagecommunity.pojo.ResultEnum;
import com.zenith.front.villagecommunity.service.IVcMemInfoService;
import com.zenith.front.villagecommunity.util.CryptoData;
import com.zenith.front.villagecommunity.util.CryptoUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 拉取村社区人员任务调度 每天晚上定时拉取
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2022/8/18 15:12
 */
//@Configuration
//@EnableScheduling
@Slf4j
public class PullMemberSchedule {

    @Resource
    private VillageCommunityClient villageCommunityClient;
    @Resource
    private IVcMemInfoService vcMemInfoService;

    private static final Integer REQUEST_PAGE_SIZE = 1000;

    @Scheduled(cron = "0 10 1 * * ?")
    public void pull() {
        //清空表
        vcMemInfoService.clear();
        int pageNum = 1;
        int synchronizedNum = 0;
        for (; ; ) {
            VillageCommunityMemberDTO communityMemberDTO = new VillageCommunityMemberDTO();
            communityMemberDTO.setPageNum(pageNum);
            communityMemberDTO.setPageSize(REQUEST_PAGE_SIZE);
            Result<CryptoData> result = villageCommunityClient.pullMemInfo(communityMemberDTO);
            if (result.getCode() != ResultEnum.SUCCESS.getCode()) {
                log.error("请求村社区系统错误:{}", result.getMsg());
                return;
            }
            CryptoData cryptoData = result.getData();
            String keyDecrypt = CryptoUtil.asymmetricDecrypt(CryptoUtil.privateStr, cryptoData.getKey());
            // 数据解密
            final String decryptData = CryptoUtil.symmetricDecrypt(keyDecrypt, cryptoData.getData());
            //转化为page对象
            Page<Map<String, Object>> page = JSONUtil.toBean(decryptData, Page.class);
            List<Map<String, Object>> records = page.getRecords();
            if (CollUtil.isEmpty(records)) {
                break;
            }
            List<VcMemInfo> vcMemInfoList = new ArrayList<>();
            for (Map<String, Object> record : records) {
                VcMemInfo vcMemInfo = new VcMemInfo();
                vcMemInfo.setIdcard(record.get("idcard").toString());
                vcMemInfo.setCreateTime(new Date());
                vcMemInfoList.add(vcMemInfo);
            }
            synchronizedNum += vcMemInfoList.size();
            vcMemInfoService.saveBatch(vcMemInfoList);
            long pages = page.getPages();
            if (pageNum >= pages) {
                break;
            }
            pageNum++;
        }
        log.info("已拉取村社区人员数量:{}", synchronizedNum);
    }
}
