package com.zenith.front.villagecommunity.scheduled;

import com.zenith.front.villagecommunity.resulthandler.ISteamQueryResulService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;

/**
 * 推送集体经济
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2022/11/15 18:02
 */
//@Configuration
//@EnableScheduling
@Slf4j
public class PushUnitCollectiveEconomicSchedule {

    @Resource(name = "steamQueryUnitCollectiveEconomicServiceImpl")
    private ISteamQueryResulService streamingQuery;

    @Scheduled(cron = "0 30 2 * * ?")
    public void push() {
        streamingQuery.batchSteamQueryHandle();
    }
}
