package com.zenith.front.villagecommunity.scheduled;

import com.zenith.front.villagecommunity.resulthandler.ISteamQueryResulService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;

/**
 * 推送村社区用户任务调度
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2022/8/18 15:12
 */
//@Configuration
//@EnableScheduling
@Slf4j
public class PushUserSchedule {

    @Resource(name = "steamQueryUserServiceImpl")
    private ISteamQueryResulService streamingQuery;

    @Scheduled(cron = "0 40 3 * * ?")
    public void push() {
        streamingQuery.batchSteamQueryHandle();
    }
}
