package com.zenith.front.villagecommunity.scheduled;

import com.zenith.front.villagecommunity.resulthandler.ISteamQueryResulService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;

//@Configuration
//@EnableScheduling
@Slf4j
public class PushUnitCountryside {

    @Resource(name = "steamQueryUnitCountrysideServiceImpl")
    private ISteamQueryResulService streamingQuery;

    @Scheduled(cron = "0 30 2 * * ?")
    public void push() {
        streamingQuery.batchSteamQueryHandle();
    }
}
