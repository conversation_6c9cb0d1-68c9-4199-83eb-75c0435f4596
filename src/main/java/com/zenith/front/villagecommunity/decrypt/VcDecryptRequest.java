package com.zenith.front.villagecommunity.decrypt;

import cn.hutool.core.io.IoUtil;
import cn.hutool.json.JSONUtil;
import com.zenith.front.villagecommunity.annotation.VcDecrypt;
import com.zenith.front.villagecommunity.util.CryptoData;
import com.zenith.front.villagecommunity.util.CryptoUtil;
import com.zenith.front.villagecommunity.util.KeyConstant;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.lang.NonNull;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdviceAdapter;

import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;
import java.nio.charset.Charset;

/**
 * 统一解密带有注解VcDecrypt的传输请求参数
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2022/8/29 16:08
 */
@RestControllerAdvice
public class VcDecryptRequest extends RequestBodyAdviceAdapter {

    @Override
    public boolean supports(MethodParameter methodParameter, @NonNull Type type, @NonNull Class<? extends HttpMessageConverter<?>> aClass) {
        return methodParameter.hasMethodAnnotation(VcDecrypt.class) || methodParameter.hasParameterAnnotation(VcDecrypt.class);
    }

    @Override
    public HttpInputMessage beforeBodyRead(@NotNull final HttpInputMessage inputMessage, @NonNull MethodParameter parameter, @NonNull Type targetType, @NonNull Class<? extends HttpMessageConverter<?>> converterType) throws IOException {
        InputStream inputMessageBody = inputMessage.getBody();
        if (inputMessageBody.available() <= 0) {
            return inputMessage;
        }
        Charset charset = Charset.defaultCharset();
        //获取http请求中原始的body
        String body = IoUtil.read(inputMessageBody, charset);
        CryptoData cryptoData = JSONUtil.toBean(body, CryptoData.class);
        // key 解密
        String keyDecrypt = CryptoUtil.asymmetricDecrypt(KeyConstant.PRIVATE_STR, cryptoData.getKey());
        // 数据解密
        String decryptData = CryptoUtil.symmetricDecrypt(keyDecrypt, cryptoData.getData());

        //将解密之后的body数据重新封装为HttpInputMessage作为当前方法的返回值
        InputStream inputStream = IoUtil.toStream(decryptData, charset);
        return new HttpInputMessage() {
            @Override
            public InputStream getBody() {
                return inputStream;
            }

            @Override
            public HttpHeaders getHeaders() {
                return inputMessage.getHeaders();
            }
        };
    }
}
