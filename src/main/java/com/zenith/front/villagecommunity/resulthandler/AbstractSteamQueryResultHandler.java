package com.zenith.front.villagecommunity.resulthandler;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.ResultContext;
import org.apache.ibatis.session.ResultHandler;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2022/8/22 16:31
 */
@Slf4j
public abstract class AbstractSteamQueryResultHandler<T> implements ResultHandler<T> {

    /**
     * 这是每一个批处理查询的数量
     */
    public final int batchSize = 1000;
    /**
     * 初始值
     */
    public int size = 0;
    /**
     * 存储每批数据的临时容器
     */
    public List<T> rowDataList = new ArrayList<>();

    /**
     * 是否第一页
     */
    public boolean last = false;

    /**
     * 是否最后一页
     */
    public boolean first = false;

    @Override
    public void handleResult(ResultContext<? extends T> resultContext) {
        // 流式查询每次返回的单条结果
        T t = resultContext.getResultObject();
        rowDataList.add(t);
        size++;
        if (size == batchSize) {
            handle();
        }
    }

    /**
     * 这个方需要子类重写此接口，处理具体业务逻辑
     */
    public abstract void handle();

    /**
     * 处理最后一批不到 batchSize(查询设定的阀值)的数据
     */
    public void lastSteamHandle() {
        log.info("最后批次处理数据量 :{}", size);
        last = true;
        handle();
    }
}
