package com.zenith.front.villagecommunity.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * <p>
 * 村社区发展党员基础信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-29
 */
@TableName("vc_develop_mem_info")
public class VcDevelopMemInfo extends Model<VcDevelopMemInfo> {

    private static final long serialVersionUID = 1L;

    /**
     * 人员标识
     */
    @TableId(value = "code", type = IdType.ASSIGN_UUID)
    private String code;

    /**
     * 发展党员姓名
     */
    @TableField("name")
    private String name;

    /**
     * 公民身份证
     */
    @TableField("idcard")
    private String idcard;

    /**
     * 民族
     */
    @TableField("d06_code")
    private String d06Code;

    /**
     * 民族
     */
    @TableField("d06_name")
    private String d06Name;

    /**
     * 籍贯
     */
    @TableField("d48_code")
    private String d48Code;

    /**
     * 籍贯
     */
    @TableField("d48_name")
    private String d48Name;

    /**
     * 性别
     */
    @TableField("sex_code")
    private String sexCode;

    /**
     * 性别
     */
    @TableField("sex_name")
    private String sexName;

    /**
     * 电话
     */
    @TableField("phone")
    private String phone;

    /**
     * 学历
     */
    @TableField("d07_code")
    private String d07Code;

    /**
     * 学历
     */
    @TableField("d07_name")
    private String d07Name;

    /**
     * 工作岗位
     */
    @TableField("d09_code")
    private String d09Code;

    /**
     * 工作岗位
     */
    @TableField("d09_name")
    private String d09Name;

    /**
     * 人员类型
     */
    @TableField("d08_code")
    private String d08Code;

    /**
     * 人员类型
     */
    @TableField("d08_name")
    private String d08Name;

    /**
     * 组织code
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 组织名称
     */
    @TableField("org_name")
    private String orgName;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 数据类型
     */
    @TableField("data_type")
    private String dataType;

    /**
     * 节点key
     */
    @TableField("nginx_key")
    private String nginxKey;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdcard() {
        return idcard;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }

    public String getd06Code() {
        return d06Code;
    }

    public void setd06Code(String d06Code) {
        this.d06Code = d06Code;
    }

    public String getd06Name() {
        return d06Name;
    }

    public void setd06Name(String d06Name) {
        this.d06Name = d06Name;
    }

    public String getd48Code() {
        return d48Code;
    }

    public void setd48Code(String d48Code) {
        this.d48Code = d48Code;
    }

    public String getd48Name() {
        return d48Name;
    }

    public void setd48Name(String d48Name) {
        this.d48Name = d48Name;
    }

    public String getSexCode() {
        return sexCode;
    }

    public void setSexCode(String sexCode) {
        this.sexCode = sexCode;
    }

    public String getSexName() {
        return sexName;
    }

    public void setSexName(String sexName) {
        this.sexName = sexName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getd07Code() {
        return d07Code;
    }

    public void setd07Code(String d07Code) {
        this.d07Code = d07Code;
    }

    public String getd07Name() {
        return d07Name;
    }

    public void setd07Name(String d07Name) {
        this.d07Name = d07Name;
    }

    public String getd09Code() {
        return d09Code;
    }

    public void setd09Code(String d09Code) {
        this.d09Code = d09Code;
    }

    public String getd09Name() {
        return d09Name;
    }

    public void setd09Name(String d09Name) {
        this.d09Name = d09Name;
    }

    public String getd08Code() {
        return d08Code;
    }

    public void setd08Code(String d08Code) {
        this.d08Code = d08Code;
    }

    public String getd08Name() {
        return d08Name;
    }

    public void setd08Name(String d08Name) {
        this.d08Name = d08Name;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getNginxKey() {
        return nginxKey;
    }

    public void setNginxKey(String nginxKey) {
        this.nginxKey = nginxKey;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    @Override
    protected Serializable pkVal() {
        return this.code;
    }

    @Override
    public String toString() {
        return "VcDevelopMemInfo{" +
                "code=" + code +
                ", name=" + name +
                ", idcard=" + idcard +
                ", d06Code=" + d06Code +
                ", d06Name=" + d06Name +
                ", d48Code=" + d48Code +
                ", d48Name=" + d48Name +
                ", sexCode=" + sexCode +
                ", sexName=" + sexName +
                ", phone=" + phone +
                ", d07Code=" + d07Code +
                ", d07Name=" + d07Name +
                ", d09Code=" + d09Code +
                ", d09Name=" + d09Name +
                ", d08Code=" + d08Code +
                ", d08Name=" + d08Name +
                ", orgCode=" + orgCode +
                ", orgName=" + orgName +
                ", createTime=" + createTime +
                ", dataType=" + dataType +
                ", nginxKey=" + nginxKey +
                ", updateTime=" + updateTime +
                ", deleteTime=" + deleteTime +
                "}";
    }
}
