package com.zenith.front.villagecommunity.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/11/15
 */
@TableName("vc_unit_collective_economic")
public class VcUnitCollectiveEconomic extends Model<VcUnitCollectiveEconomic> {

    private static final long serialVersionUID = 1L;


    /**
     * 唯一标识符code
     */
    @TableId(value = "code", type = IdType.ASSIGN_UUID)
    private String code;

    /**
     * 党组织唯一组织code
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 党组织组织层级码
     */
    @TableField("economic_org_code")
    private String economicOrgCode;

    /**
     * 产业名称
     */
    @TableField("industry_name")
    private String industryName;

    /**
     * 收入渠道（不要了）
     */
    @TableField("income_channel")
    private String incomeChannel;

    /**
     * 收入产业（不要了）
     */
    @TableField("income_industry")
    private String incomeIndustry;

    /**
     * 单位code
     */
    @TableField("unit_code")
    private String unitCode;

    /**
     * 单位name
     */
    @TableField("unit_name")
    private String unitName;

    /**
     * 发展经济类型code
     */
    @TableField("d128_code")
    private String d128Code;

    /**
     * 发展经济类型name
     */
    @TableField("d128_name")
    private String d128Name;

    /**
     * 发展经济类型金额（万元）
     */
    @TableField("develop_money")
    private BigDecimal developMoney;

    /**
     * 采取组织形式code
     */
    @TableField("d129_code")
    private String d129Code;

    /**
     * 采取组织形式name
     */
    @TableField("d129_name")
    private String d129Name;

    /**
     * 组织形式金额（万元）
     */
    @TableField("organ_amount")
    private BigDecimal organAmount;

    /**
     * 受益农村人口数
     */
    @TableField("benefit_population")
    private Integer benefitPopulation;

    /**
     * 村党组织的书记是否担任村级集体经济组织负责人的村（1是 0否）
     */
    @TableField("has_economic_village")
    private Integer hasEconomicVillage;
    /**
     * 创建时间
     */
    @TableField("create_time_str")
    private String createTimeStr;

    /**
     * 更新时间
     */
    @TableField("update_time_str")
    private String updateTimeStr;
    @TableField(exist = false)
    private String deleteTime;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getEconomicOrgCode() {
        return economicOrgCode;
    }

    public void setEconomicOrgCode(String economicOrgCode) {
        this.economicOrgCode = economicOrgCode;
    }

    public String getIndustryName() {
        return industryName;
    }

    public void setIndustryName(String industryName) {
        this.industryName = industryName;
    }

    public String getIncomeChannel() {
        return incomeChannel;
    }

    public void setIncomeChannel(String incomeChannel) {
        this.incomeChannel = incomeChannel;
    }

    public String getIncomeIndustry() {
        return incomeIndustry;
    }

    public void setIncomeIndustry(String incomeIndustry) {
        this.incomeIndustry = incomeIndustry;
    }


    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getD128Code() {
        return d128Code;
    }

    public void setD128Code(String d128Code) {
        this.d128Code = d128Code;
    }

    public String getD128Name() {
        return d128Name;
    }

    public void setD128Name(String d128Name) {
        this.d128Name = d128Name;
    }

    public BigDecimal getDevelopMoney() {
        return developMoney;
    }

    public void setDevelopMoney(BigDecimal developMoney) {
        this.developMoney = developMoney;
    }

    public String getD129Code() {
        return d129Code;
    }

    public void setD129Code(String d129Code) {
        this.d129Code = d129Code;
    }

    public String getD129Name() {
        return d129Name;
    }

    public void setD129Name(String d129Name) {
        this.d129Name = d129Name;
    }

    public BigDecimal getOrganAmount() {
        return organAmount;
    }

    public void setOrganAmount(BigDecimal organAmount) {
        this.organAmount = organAmount;
    }

    public Integer getBenefitPopulation() {
        return benefitPopulation;
    }

    public void setBenefitPopulation(Integer benefitPopulation) {
        this.benefitPopulation = benefitPopulation;
    }

    public Integer getHasEconomicVillage() {
        return hasEconomicVillage;
    }

    public void setHasEconomicVillage(Integer hasEconomicVillage) {
        this.hasEconomicVillage = hasEconomicVillage;
    }

    public String getCreateTimeStr() {
        return createTimeStr;
    }

    public void setCreateTimeStr(String createTimeStr) {
        this.createTimeStr = createTimeStr;
    }

    public String getUpdateTimeStr() {
        return updateTimeStr;
    }

    public void setUpdateTimeStr(String updateTimeStr) {
        this.updateTimeStr = updateTimeStr;
    }

    public String getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(String deleteTime) {
        this.deleteTime = deleteTime;
    }
}
