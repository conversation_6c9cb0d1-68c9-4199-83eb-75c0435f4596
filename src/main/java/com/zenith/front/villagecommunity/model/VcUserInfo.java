package com.zenith.front.villagecommunity.model;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 党外系统人员信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-23
 */
@TableName("vc_user_info")
public class VcUserInfo extends Model<VcUserInfo> {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID,uuid,不能为null
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 用户账号,不能为null
     */
    @TableField("account")
    private String account;

    /**
     * 用户密码,不能为null
     */
    @TableField("password")
    private String password;

    /**
     * 关联身份证
     */
    @TableField("idcard")
    private String idCard;

    /**
     * 数据类型 0现存数据 1删除数据
     */
    @TableField("data_type")
    private String dataType;

    /**
     * 节点key
     */
    @TableField("nginx_key")
    private String nginxKey;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除时间 特殊处理账号来回切换为村社区系统时，被删除后再变为正常的情况
     */
    @TableField(value = "delete_time", updateStrategy = FieldStrategy.IGNORED)
    private Date deleteTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getNginxKey() {
        return nginxKey;
    }

    public void setNginxKey(String nginxKey) {
        this.nginxKey = nginxKey;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "VcUserInfo{" +
                "id='" + id + '\'' +
                ", account='" + account + '\'' +
                ", password='" + password + '\'' +
                ", idCard='" + idCard + '\'' +
                ", dataType='" + dataType + '\'' +
                ", nginxKey=" + nginxKey +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deleteTime=" + deleteTime +
                '}';
    }
}
