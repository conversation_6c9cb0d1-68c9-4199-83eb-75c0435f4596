package com.zenith.front.villagecommunity.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-23
 */
@TableName("vc_unit_info")
public class VcUnitInfo extends Model<VcUnitInfo> {

    private static final long serialVersionUID = 1L;

    /**
     * 单位主键
     */
    @TableId(value = "code", type = IdType.ASSIGN_UUID)
    private String code;

    /**
     * 单位名称
     */
    @TableField("unit_name")
    private String unitName;

    /**
     * 单位代码
     */
    @TableField("credit_code")
    private String creditCode;

    /**
     * 单位类别
     */
    @TableField("d04_code")
    private String d04Code;

    /**
     * 单位类别名称
     */
    @TableField("d04_name")
    private String d04Name;

    /**
     * 单位隶属关系
     */
    @TableField("d35_code")
    private String d35Code;

    /**
     * 单位隶属关系名称
     */
    @TableField("d35_name")
    private String d35Name;

    /**
     * 是否法人单位标识：1是，0否
     */
    @TableField("is_legal")
    private Integer isLegal;

    /**
     * 组织主键
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 数据类型 -1 新增数据 0现存数据 1删除数据
     */
    @TableField("data_type")
    private String dataType;

    /**
     * 节点key
     */
    @TableField("nginx_key")
    private String nginxKey;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getD04Code() {
        return d04Code;
    }

    public void setD04Code(String d04Code) {
        this.d04Code = d04Code;
    }

    public String getD04Name() {
        return d04Name;
    }

    public void setD04Name(String d04Name) {
        this.d04Name = d04Name;
    }

    public String getD35Code() {
        return d35Code;
    }

    public void setD35Code(String d35Code) {
        this.d35Code = d35Code;
    }

    public String getD35Name() {
        return d35Name;
    }

    public void setD35Name(String d35Name) {
        this.d35Name = d35Name;
    }

    public Integer getIsLegal() {
        return isLegal;
    }

    public void setIsLegal(Integer isLegal) {
        this.isLegal = isLegal;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getNginxKey() {
        return nginxKey;
    }

    public void setNginxKey(String nginxKey) {
        this.nginxKey = nginxKey;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    @Override
    protected Serializable pkVal() {
        return this.code;
    }

    @Override
    public String toString() {
        return "VcUnitInfo{" +
                "code='" + code + '\'' +
                ", unitName='" + unitName + '\'' +
                ", creditCode='" + creditCode + '\'' +
                ", d04Code='" + d04Code + '\'' +
                ", d04Name='" + d04Name + '\'' +
                ", d35Code='" + d35Code + '\'' +
                ", d35Name='" + d35Name + '\'' +
                ", isLegal=" + isLegal +
                ", orgCode='" + orgCode + '\'' +
                ", dataType='" + dataType + '\'' +
                ", nginxKey='" + nginxKey + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deleteTime=" + deleteTime +
                '}';
    }
}
