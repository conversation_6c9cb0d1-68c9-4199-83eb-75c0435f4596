package com.zenith.front.villagecommunity.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.util.Date;

@TableName("vc_unit_countryside")
public class VcUnitCountryside {

    @TableId(value = "code",type = IdType.ASSIGN_UUID)
    private String code;

    @TableField("es_id")
    private String esId;

    /**
     * 人员唯一标识
     */
    @TableField("mem_code")
    private String memCode;

    @TableField(value = "mem_name")
    private String memName;

    /**
     * 人员类型情况（是否是党员）
     */
    @TableField("mem_type_code")
    private String memTypeCode;

    /**
     * 人员类型(党员，群众)
     */
    @TableField("mem_type_name")
    private String memTypeName;

    @TableField(value = "mem_idcard")
    private String memIdcard;

    /**
     * 人员学历代码说明
     */
    @TableField("d07_code")
    private String d07Code;

    /**
     * 人员学历代码说明
     */
    @TableField("d07_name")
    private String d07Name;

    /**
     * 出生日期
     */
    @TableField("birthday_str")
    private String birthdayStr;

    /**
     * 性别代码
     */
    @TableField("sex_code")
    private String sexCode;

    /**
     * 性别代码说明
     */
    @TableField("sex_name")
    private String sexName;

    @TableField("unit_code")
    private String unitCode;

    @TableField("unit_name")
    private String unitName;

    /**
     * 人员名称,非党员情况下
     */
    @TableField("person_name")
    private String personName;

    /**
     * 行政职务说明
     */
    @TableField("remark")
    private String remark;

    @TableField("timestamp_str")
    private String timestampStr;

    @TableField("is_history")
    private Integer isHistory;

    @TableField("update_account")
    private String updateAccount;
    /**
     * 1社区工作者，2村后备干部
     */
    @TableField("type")
    private String type;

    @TableField(value = "phone")
    private String phone;

    /**
     * 是否专职党务工作者（1是）
     */
    @TableField("has_party_work")
    private Integer hasPartyWork;

    /**
     * 是否推荐为两代表一委员（1是）
     */
    @TableField("has_two_one_member")
    private Integer hasTwoOneMember;

    /**
     * 录用来源
     */
    @TableField("d116_code")
    private String d116Code;

    @TableField("d116_name")
    private String d116Name;

    /**
     * 离开去向
     */
    @TableField("d117_code")
    private String d117Code;

    @TableField("d117_name")
    private String d117Name;

    /**
     * 离开时间
     */
    @TableField("leave_time_str")
    private String leaveTimeStr;

    /**
     * 是否县乡领导班子成员帮带人（1是）
     */
    @TableField("has_leaders_help_people")
    private Integer hasLeadersHelpPeople;

    /**
     * 是否村任职选调生
     */
    @TableField("has_village_transfer_student")
    private Integer hasVillageTransferStudent;
    /**
     * 选调单位层级 dict_d144
     */
    @TableField("d144_code")
    private String d144Code;

    @TableField("d144_name")
    private String d144Name;
    /**
     * 到村任职补助经费
     */
    @TableField("subsidies")
    private BigDecimal subsidies;

    /**
     * 岗位
     */
    @TableField("d143_code")
    private String d143Code;

    /**
     * 岗位
     */
    @TableField("d143_name")
    private String d143Name;

    /**
     * 创建时间
     */
    @TableField("create_time_str")
    private String createTimeStr;
    /**
     * 修改时间
     */
    @TableField("update_time_str")
    private String updateTimeStr;
    @TableField(value = "delete_time",exist = false)
    private String deleteTime;


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEsId() {
        return esId;
    }

    public void setEsId(String esId) {
        this.esId = esId;
    }

    public String getMemCode() {
        return memCode;
    }

    public void setMemCode(String memCode) {
        this.memCode = memCode;
    }

    public String getMemName() {
        return memName;
    }

    public void setMemName(String memName) {
        this.memName = memName;
    }

    public String getMemTypeCode() {
        return memTypeCode;
    }

    public void setMemTypeCode(String memTypeCode) {
        this.memTypeCode = memTypeCode;
    }

    public String getMemTypeName() {
        return memTypeName;
    }

    public void setMemTypeName(String memTypeName) {
        this.memTypeName = memTypeName;
    }

    public String getMemIdcard() {
        return memIdcard;
    }

    public void setMemIdcard(String memIdcard) {
        this.memIdcard = memIdcard;
    }

    public String getD07Code() {
        return d07Code;
    }

    public void setD07Code(String d07Code) {
        this.d07Code = d07Code;
    }

    public String getD07Name() {
        return d07Name;
    }

    public void setD07Name(String d07Name) {
        this.d07Name = d07Name;
    }

    public String getBirthdayStr() {
        return birthdayStr;
    }

    public void setBirthdayStr(String birthdayStr) {
        this.birthdayStr = birthdayStr;
    }

    public String getSexCode() {
        return sexCode;
    }

    public void setSexCode(String sexCode) {
        this.sexCode = sexCode;
    }

    public String getSexName() {
        return sexName;
    }

    public void setSexName(String sexName) {
        this.sexName = sexName;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTimestampStr() {
        return timestampStr;
    }

    public void setTimestamp(String timestampStr) {
        this.timestampStr = timestampStr;
    }

    public Integer getIsHistory() {
        return isHistory;
    }

    public void setIsHistory(Integer isHistory) {
        this.isHistory = isHistory;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getHasPartyWork() {
        return hasPartyWork;
    }

    public void setHasPartyWork(Integer hasPartyWork) {
        this.hasPartyWork = hasPartyWork;
    }

    public Integer getHasTwoOneMember() {
        return hasTwoOneMember;
    }

    public void setHasTwoOneMember(Integer hasTwoOneMember) {
        this.hasTwoOneMember = hasTwoOneMember;
    }

    public String getD116Code() {
        return d116Code;
    }

    public void setD116Code(String d116Code) {
        this.d116Code = d116Code;
    }

    public String getD116Name() {
        return d116Name;
    }

    public void setD116Name(String d116Name) {
        this.d116Name = d116Name;
    }

    public String getD117Code() {
        return d117Code;
    }

    public void setD117Code(String d117Code) {
        this.d117Code = d117Code;
    }

    public String getD117Name() {
        return d117Name;
    }

    public void setD117Name(String d117Name) {
        this.d117Name = d117Name;
    }

    public String getLeaveTimeStr() {
        return leaveTimeStr;
    }

    public void setLeaveTimeStr(String leaveTimeStr) {
        this.leaveTimeStr = leaveTimeStr;
    }

    public Integer getHasLeadersHelpPeople() {
        return hasLeadersHelpPeople;
    }

    public void setHasLeadersHelpPeople(Integer hasLeadersHelpPeople) {
        this.hasLeadersHelpPeople = hasLeadersHelpPeople;
    }

    public Integer getHasVillageTransferStudent() {
        return hasVillageTransferStudent;
    }

    public void setHasVillageTransferStudent(Integer hasVillageTransferStudent) {
        this.hasVillageTransferStudent = hasVillageTransferStudent;
    }

    public String getD144Code() {
        return d144Code;
    }

    public void setD144Code(String d144Code) {
        this.d144Code = d144Code;
    }

    public String getD144Name() {
        return d144Name;
    }

    public void setD144Name(String d144Name) {
        this.d144Name = d144Name;
    }

    public BigDecimal getSubsidies() {
        return subsidies;
    }

    public void setSubsidies(BigDecimal subsidies) {
        this.subsidies = subsidies;
    }

    public String getD143Code() {
        return d143Code;
    }

    public void setD143Code(String d143Code) {
        this.d143Code = d143Code;
    }

    public String getD143Name() {
        return d143Name;
    }

    public void setD143Name(String d143Name) {
        this.d143Name = d143Name;
    }

    public String getCreateTimeStr() {
        return createTimeStr;
    }

    public void setCreateTimeStr(String createTimeStr) {
        this.createTimeStr = createTimeStr;
    }

    public String getUpdateTimeStr() {
        return updateTimeStr;
    }

    public void setUpdateTimeStr(String updateTimeStr) {
        this.updateTimeStr = updateTimeStr;
    }

    public String getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(String deleteTime) {
        this.deleteTime = deleteTime;
    }
}
