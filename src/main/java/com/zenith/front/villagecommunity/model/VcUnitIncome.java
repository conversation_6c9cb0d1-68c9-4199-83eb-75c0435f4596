package com.zenith.front.villagecommunity.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/11/15
 */
@TableName("vc_unit_income")
public class VcUnitIncome extends Model<VcUnitIncome> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识符code
     */
    @TableId(value = "code", type = IdType.ASSIGN_UUID)
    private String code;

    /**
     * 集体经济code
     */
    @TableField("economic_code")
    private String economicCode;
    /**
     * 收入情况 [{id:主键，type:收入类别，typeName:类别名称，amount：收入金额（万元），remark：备注}]，字典表dict_d130
     */
    @TableField("income")
    private String income;
    /**
     * 收入金额（万元）
     */
    @TableField("income_amount")
    private BigDecimal incomeAmount;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 负债金额（万元）
     */
    @TableField("incur_debts_amount")
    private BigDecimal incurDebtsAmount;

    /**
     * 支出情况（同上） dict_d1301
     */
    @TableField("outlay")
    private String outlay;

    /**
     * 资产情况（同上） dict_d1302
     */
    @TableField("property")
    private String property;

    /**
     * 所有者权益情况（同上） dict_d1303
     */
    @TableField("ownership")
    private String ownership;

    /**
     * 单位code
     */
    @TableField("unit_code")
    private String unitCode;

    /**
     * 是否由村党组织书记担任村集体经济负责人
     */
    @TableField("has_secretary_economy")
    private Integer hasSecretaryEconomy;

    /**
     * 集体经济负债（万）
     */
    @TableField("collective_economic_liabilities")
    private BigDecimal collectiveEconomicLiabilities;

    /**
     * 创建时间
     */
    @TableField("create_time_str")
    private String createTimeStr;

    /**
     * 更新时间
     */
    @TableField("update_time_str")
    private String updateTimeStr;
    @TableField(exist = false)
    private String deleteTime;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEconomicCode() {
        return economicCode;
    }

    public void setEconomicCode(String economicCode) {
        this.economicCode = economicCode;
    }

    public String getIncome() {
        return income;
    }

    public void setIncome(String income) {
        this.income = income;
    }

    public BigDecimal getIncomeAmount() {
        return incomeAmount;
    }

    public void setIncomeAmount(BigDecimal incomeAmount) {
        this.incomeAmount = incomeAmount;
    }


    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public BigDecimal getIncurDebtsAmount() {
        return incurDebtsAmount;
    }

    public void setIncurDebtsAmount(BigDecimal incurDebtsAmount) {
        this.incurDebtsAmount = incurDebtsAmount;
    }

    public String getOutlay() {
        return outlay;
    }

    public void setOutlay(String outlay) {
        this.outlay = outlay;
    }

    public String getProperty() {
        return property;
    }

    public void setProperty(String property) {
        this.property = property;
    }

    public String getOwnership() {
        return ownership;
    }

    public void setOwnership(String ownership) {
        this.ownership = ownership;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public Integer getHasSecretaryEconomy() {
        return hasSecretaryEconomy;
    }

    public void setHasSecretaryEconomy(Integer hasSecretaryEconomy) {
        this.hasSecretaryEconomy = hasSecretaryEconomy;
    }

    public BigDecimal getCollectiveEconomicLiabilities() {
        return collectiveEconomicLiabilities;
    }

    public void setCollectiveEconomicLiabilities(BigDecimal collectiveEconomicLiabilities) {
        this.collectiveEconomicLiabilities = collectiveEconomicLiabilities;
    }

    public String getCreateTimeStr() {
        return createTimeStr;
    }

    public void setCreateTimeStr(String createTimeStr) {
        this.createTimeStr = createTimeStr;
    }

    public String getUpdateTimeStr() {
        return updateTimeStr;
    }

    public void setUpdateTimeStr(String updateTimeStr) {
        this.updateTimeStr = updateTimeStr;
    }

    public String getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(String deleteTime) {
        this.deleteTime = deleteTime;
    }
}
