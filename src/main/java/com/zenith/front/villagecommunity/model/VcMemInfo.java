package com.zenith.front.villagecommunity.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * <p>
 * 村社区人员
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-23
 */
@TableName("vc_mem_info")
public class VcMemInfo extends Model<VcMemInfo> {

    private static final long serialVersionUID = 1L;

    /**
     * 人员主键
     */
    @TableId(value = "code", type = IdType.ASSIGN_UUID)
    private String code;

    /**
     * 身份证号
     */
    @TableField("idcard")
    private String idcard;

    /**
     * 是否党员 1 是 2 否
     */
    @TableField("party_mem")
    private Integer partyMem;

    /**
     * 入党时间
     */
    @TableField("join_org_date")
    private Date joinOrgDate;

    /**
     * 人员类型 1 正式党员 2 预备党员
     */
    @TableField("d08_code")
    private String d08Code;

    /**
     * 党务系统党员主键
     */
    @TableField("mem_code")
    private String memCode;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getIdcard() {
        return idcard;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }

    public Integer getPartyMem() {
        return partyMem;
    }

    public void setPartyMem(Integer partyMem) {
        this.partyMem = partyMem;
    }

    public Date getJoinOrgDate() {
        return joinOrgDate;
    }

    public void setJoinOrgDate(Date joinOrgDate) {
        this.joinOrgDate = joinOrgDate;
    }

    public String getD08Code() {
        return d08Code;
    }

    public void setD08Code(String d08Code) {
        this.d08Code = d08Code;
    }

    public String getMemCode() {
        return memCode;
    }

    public void setMemCode(String memCode) {
        this.memCode = memCode;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    protected Serializable pkVal() {
        return this.code;
    }

    @Override
    public String toString() {
        return "VcMemInfo{" +
                "code='" + code + '\'' +
                ", idcard='" + idcard + '\'' +
                ", partyMem=" + partyMem +
                ", joinOrgDate=" + joinOrgDate +
                ", d08Code='" + d08Code + '\'' +
                ", memCode='" + memCode + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
