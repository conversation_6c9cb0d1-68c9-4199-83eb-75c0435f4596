package com.zenith.front.villagecommunity.api;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zenith.front.villagecommunity.annotation.VcDecrypt;
import com.zenith.front.villagecommunity.model.*;
import com.zenith.front.villagecommunity.pojo.MemInfo;
import com.zenith.front.villagecommunity.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@RequestMapping("/vc")
@RestController
public class VcController {

    @Resource
    private IVcUnitInfoService unitInfoService;
    @Resource
    private IVcUserInfoService userInfoService;
    @Resource
    private IVcMemInfoService memInfoService;
    @Resource
    private IVcDevelopMemInfoService developMemInfoService;
    @Resource
    private IVcUnitCollectiveEconomicService iVcUnitCollectiveEconomicService;
    @Resource
    private IVcUnitIncomeService iVcUnitIncomeService;
    @Resource
    private IVcUnitCountrysideService iVcUnitCountrysideService;


    @PostMapping("/unit/push")
    public void unitPush(@VcDecrypt @RequestBody PushUnitInfoDTO pushUnitInfoDTO) {
        List<VcUnitInfo> unitInfoList = pushUnitInfoDTO.getList();
        String nginxKey = pushUnitInfoDTO.getNginxKey();
        LambdaQueryWrapper<VcUnitInfo> queryWrapper = new LambdaQueryWrapper<VcUnitInfo>().eq(VcUnitInfo::getNginxKey, nginxKey).select(VcUnitInfo::getCode);
        List<VcUnitInfo> dbUnitInfoList = unitInfoService.list(queryWrapper);
        //新增记录
        List<VcUnitInfo> addList = unitInfoList.stream()
                .filter(unitInfo -> !dbUnitInfoList.stream().map(VcUnitInfo::getCode)
                        .collect(Collectors.toList()).contains(unitInfo.getCode()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(addList)) {
            for (VcUnitInfo vcUnitInfo : addList) {
                vcUnitInfo.setDataType("-1");
                vcUnitInfo.setNginxKey(nginxKey);
                vcUnitInfo.setCreateTime(new Date());
            }
            log.info("单位信息新增:{}条", addList.size());
            unitInfoService.saveOrUpdateBatch(addList);
        }
        //更新记录
        List<VcUnitInfo> updateList = unitInfoList.stream()
                .filter(unitInfo -> dbUnitInfoList.stream().map(VcUnitInfo::getCode)
                        .collect(Collectors.toList()).contains(unitInfo.getCode()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(updateList)) {
            updateList.forEach(vcUnitInfo -> {
                vcUnitInfo.setDataType("0");
                vcUnitInfo.setUpdateTime(new Date());
            });
            log.info("单位信息更新:{}条", updateList.size());
            unitInfoService.updateBatchById(updateList);
        }
        //删除记录
        List<String> dbCodeList = dbUnitInfoList.stream().filter(vcUnitInfo -> Objects.isNull(vcUnitInfo.getDeleteTime())).map(VcUnitInfo::getCode).collect(Collectors.toList());
        List<String> codeList = unitInfoList.stream().map(VcUnitInfo::getCode).collect(Collectors.toList());
        //求差集(dbCodeList有而codeList没有)
        dbCodeList.removeAll(codeList);
        if (CollUtil.isNotEmpty(dbCodeList)) {
            List<VcUnitInfo> delList = new ArrayList<>();
            dbCodeList.forEach(id -> {
                VcUnitInfo vcUnitInfo = new VcUnitInfo();
                vcUnitInfo.setCode(id);
                vcUnitInfo.setDataType("1");
                vcUnitInfo.setDeleteTime(new Date());
                delList.add(vcUnitInfo);
            });
            log.info("单位信息删除:{}条", delList.size());
            unitInfoService.updateBatchById(delList);
        }
    }

    @PostMapping("/user/push")
    public void userPush(@VcDecrypt @RequestBody PushUserInfoDTO pushUserInfoDTO) {
        List<VcUserInfo> userInfoList = pushUserInfoDTO.getList();
        String nginxKey = pushUserInfoDTO.getNginxKey();
        LambdaQueryWrapper<VcUserInfo> queryWrapper = new LambdaQueryWrapper<VcUserInfo>().eq(VcUserInfo::getNginxKey, nginxKey).select(VcUserInfo::getId);
        List<VcUserInfo> dbUserInfoList = userInfoService.list(queryWrapper);
        //新增记录
        List<VcUserInfo> addList = userInfoList.stream()
                .filter(userInfo -> !dbUserInfoList.stream().map(VcUserInfo::getId)
                        .collect(Collectors.toList()).contains(userInfo.getId()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(addList)) {
            addList.forEach(vcUserInfo -> {
                vcUserInfo.setDataType("-1");
                vcUserInfo.setNginxKey(nginxKey);
                vcUserInfo.setCreateTime(new Date());
            });
            log.info("用户信息新增:{}条", addList.size());
            userInfoService.saveOrUpdateBatch(addList);
        }
        //更新记录
        List<VcUserInfo> updateList = userInfoList.stream()
                .filter(userInfo -> dbUserInfoList.stream().map(VcUserInfo::getId)
                        .collect(Collectors.toList()).contains(userInfo.getId()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(updateList)) {
            updateList.forEach(vcUserInfo -> {
                vcUserInfo.setDataType("0");
                vcUserInfo.setUpdateTime(new Date());
                vcUserInfo.setDeleteTime(null);
            });
            log.info("用户信息更新:{}条", updateList.size());
            userInfoService.updateBatchById(updateList);
        }
        //删除记录
        List<String> dbCodeList = dbUserInfoList.stream().filter(vcUserInfo -> Objects.isNull(vcUserInfo.getDeleteTime())).map(VcUserInfo::getId).collect(Collectors.toList());
        List<String> codeList = userInfoList.stream().map(VcUserInfo::getId).collect(Collectors.toList());
        //求差集(dbCodeList有而codeList没有)
        dbCodeList.removeAll(codeList);
        if (CollUtil.isNotEmpty(dbCodeList)) {
            List<VcUserInfo> delList = new ArrayList<>();
            dbCodeList.forEach(id -> {
                VcUserInfo vcUserInfo = new VcUserInfo();
                vcUserInfo.setId(id);
                vcUserInfo.setDataType("1");
                vcUserInfo.setDeleteTime(new Date());
                delList.add(vcUserInfo);
            });
            log.info("用户信息删除:{}条", delList.size());
            userInfoService.updateBatchById(delList);
        }
    }

    @PostMapping("/mem/push")
    public void memPush(@VcDecrypt @RequestBody PushMembersDTO pushUserInfoDTO) {
        LambdaQueryWrapper<VcMemInfo> queryWrapper = new LambdaQueryWrapper<VcMemInfo>().select(VcMemInfo::getCode, VcMemInfo::getIdcard);
        List<VcMemInfo> dbMemInfoList = memInfoService.list(queryWrapper);
        if (CollUtil.isEmpty(dbMemInfoList)) {
            return;
        }
        List<MemInfo> memInfoList = pushUserInfoDTO.getList();
        Map<String, MemInfo> memInfoMap = memInfoList.stream().collect(Collectors.toMap(MemInfo::getIdCard, t -> t, (v1, v2) -> v2));
        List<VcMemInfo> updateMemInfoList = new ArrayList<>();
        for (VcMemInfo vcMemInfo : dbMemInfoList) {
            String idCard = vcMemInfo.getIdcard();
            if (memInfoMap.containsKey(idCard)) {
                MemInfo memInfo = memInfoMap.get(idCard);
                vcMemInfo.setPartyMem(1);
                vcMemInfo.setD08Code(memInfo.getMemType());
                vcMemInfo.setMemCode(memInfo.getId());
                String joinOrgDate = memInfo.getJoinOrgDate();
                if (StringUtils.hasText(joinOrgDate)) {
                    vcMemInfo.setJoinOrgDate(DateUtil.parseDate(joinOrgDate));
                }
                vcMemInfo.setUpdateTime(new Date());
                updateMemInfoList.add(vcMemInfo);
            }
        }
        if (CollUtil.isNotEmpty(updateMemInfoList)) {
            log.info("党员信息更新:{}条", updateMemInfoList.size());
            memInfoService.updateBatchById(updateMemInfoList);
        }
    }

    @PostMapping("/develop/mem/push")
    public void developMemPush(@VcDecrypt @RequestBody PushDevelopMemInfoDTO developMemInfoDTO) {
        List<VcDevelopMemInfo> developMemInfoDTOList = developMemInfoDTO.getList();
        String nginxKey = developMemInfoDTO.getNginxKey();
        LambdaQueryWrapper<VcDevelopMemInfo> queryWrapper = new LambdaQueryWrapper<VcDevelopMemInfo>().eq(VcDevelopMemInfo::getNginxKey, nginxKey).select(VcDevelopMemInfo::getCode);
        List<VcDevelopMemInfo> dbDevelopMemInfoList = developMemInfoService.list(queryWrapper);
        //新增记录
        List<VcDevelopMemInfo> addList = developMemInfoDTOList.stream()
                .filter(developMemInfo -> !dbDevelopMemInfoList.stream().map(VcDevelopMemInfo::getCode)
                        .collect(Collectors.toList()).contains(developMemInfo.getCode()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(addList)) {
            addList.forEach(vcUserInfo -> {
                vcUserInfo.setDataType("-1");
                vcUserInfo.setNginxKey(nginxKey);
                vcUserInfo.setCreateTime(new Date());
            });
            log.info("发展党员信息新增:{}条", addList.size());
            developMemInfoService.saveOrUpdateBatch(addList);
        }
        //更新记录
        List<VcDevelopMemInfo> updateList = developMemInfoDTOList.stream()
                .filter(developMemInfo -> dbDevelopMemInfoList.stream().map(VcDevelopMemInfo::getCode)
                        .collect(Collectors.toList()).contains(developMemInfo.getCode()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(updateList)) {
            updateList.forEach(vcUserInfo -> {
                vcUserInfo.setDataType("0");
                vcUserInfo.setUpdateTime(new Date());
            });
            log.info("发展党员信息更新:{}条", updateList.size());
            developMemInfoService.updateBatchById(updateList);
        }
        //删除记录
        List<String> dbCodeList = dbDevelopMemInfoList.stream().filter(vcDevelopMemInfo -> Objects.isNull(vcDevelopMemInfo.getDeleteTime())).map(VcDevelopMemInfo::getCode).collect(Collectors.toList());
        List<String> codeList = developMemInfoDTOList.stream().map(VcDevelopMemInfo::getCode).collect(Collectors.toList());
        //求差集(dbCodeList有而codeList没有)
        dbCodeList.removeAll(codeList);
        if (CollUtil.isNotEmpty(dbCodeList)) {
            List<VcDevelopMemInfo> delList = new ArrayList<>();
            dbCodeList.forEach(code -> {
                VcDevelopMemInfo vcDevelopMemInfo = new VcDevelopMemInfo();
                vcDevelopMemInfo.setCode(code);
                vcDevelopMemInfo.setDataType("1");
                vcDevelopMemInfo.setDeleteTime(new Date());
                delList.add(vcDevelopMemInfo);
            });
            log.info("发展党员信息删除:{}条", delList.size());
            developMemInfoService.updateBatchById(delList);
        }
    }


    @PostMapping("/unitCollective/push")
    public void unitCollectivePush(@VcDecrypt @RequestBody PushUnitCollectiveEconomicDTO dto) {
        List<VcUnitCollectiveEconomic> list = dto.getList();
        if (CollUtil.isNotEmpty(list)) {
            List<String> collect = list.stream().map(VcUnitCollectiveEconomic::getCode).collect(Collectors.toList());
            iVcUnitCollectiveEconomicService.remove(new LambdaQueryWrapper<VcUnitCollectiveEconomic>().in(VcUnitCollectiveEconomic::getCode, collect));
            List<VcUnitCollectiveEconomic> saveList = list.stream().filter(e -> StrUtil.isEmpty(e.getDeleteTime())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(saveList)) {
                iVcUnitCollectiveEconomicService.saveBatch(saveList, saveList.size());
                log.info("集体经济更新:{}条", saveList.size());
            }
        }
    }

    @PostMapping("/unitIncome/push")
    public void unitIncomePush(@VcDecrypt @RequestBody PushUnitIncomeDTO dto) {
        List<VcUnitIncome> list = dto.getList();
        if (CollUtil.isNotEmpty(list)) {
            List<String> collect = list.stream().map(VcUnitIncome::getCode).collect(Collectors.toList());
            iVcUnitIncomeService.remove(new LambdaQueryWrapper<VcUnitIncome>().in(VcUnitIncome::getCode, collect));
            List<VcUnitIncome> saveList = list.stream().filter(e -> StrUtil.isEmpty(e.getDeleteTime())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(saveList)) {
                iVcUnitIncomeService.saveBatch(saveList, saveList.size());
            }
        }
    }

    @PostMapping("/unitCountryside/push")
    public void unitCountryside(@VcDecrypt @RequestBody PushUnitCountrysideDTO dto) {
        List<VcUnitCountryside> list = dto.getList();
        if (CollUtil.isNotEmpty(list)) {
            List<String> collect = list.stream().map(VcUnitCountryside::getCode).collect(Collectors.toList());
            iVcUnitCountrysideService.remove(new LambdaQueryWrapper<VcUnitCountryside>().in(VcUnitCountryside::getCode, collect));
            List<VcUnitCountryside> saveList = list.stream().filter(e -> StrUtil.isEmpty(e.getDeleteTime())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(saveList)) {
                iVcUnitCountrysideService.saveBatch(saveList, saveList.size());
            }
        }
    }


}
