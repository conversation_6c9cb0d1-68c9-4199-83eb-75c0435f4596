package com.zenith.front.villagecommunity.conf;

import com.zenith.front.villagecommunity.encryption.FeignParameterInterceptor;
import com.zenith.front.villagecommunity.client.VillageCommunityClient;
import feign.Feign;
import feign.gson.GsonDecoder;
import feign.gson.GsonEncoder;
import feign.okhttp.OkHttpClient;
import feign.spring.SpringContract;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Feign 配置
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2022/8/18 17:05
 */
@Configuration
public class FeignCustomConfig {

    public static final String HOST = "http://************:8081/";

    @Bean
    public VillageCommunityClient villageCommunityClient() {
        return Feign.builder()
                // 设置编码器
                .encoder(new GsonEncoder())
                // 设置解码器
                .decoder(new GsonDecoder())
                .client(new OkHttpClient())
                .contract(new SpringContract())
                //参数加解密
                .requestInterceptor(new FeignParameterInterceptor())
                // 目标地址
                .target(VillageCommunityClient.class, HOST);
    }
}
