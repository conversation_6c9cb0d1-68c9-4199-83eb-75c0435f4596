package com.zenith.front.villagecommunity.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 解密党务系统推送请求的参数
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2022/8/29 16:10
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD, ElementType.PARAMETER})
public @interface VcDecrypt {

}
