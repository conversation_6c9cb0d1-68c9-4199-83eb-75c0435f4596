package com.zenith.front.villagecommunity.service;

import com.zenith.front.villagecommunity.client.VillageCommunityClient;
import com.zenith.front.villagecommunity.mapper.VcUnitCountrysideMapper;
import com.zenith.front.villagecommunity.model.PushUnitCountrysideDTO;
import com.zenith.front.villagecommunity.model.PushUnitIncomeDTO;
import com.zenith.front.villagecommunity.model.VcUnitCountryside;
import com.zenith.front.villagecommunity.resulthandler.AbstractSteamQueryResultHandler;
import com.zenith.front.villagecommunity.resulthandler.ISteamQueryResulService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class steamQueryUnitCountrysideServiceImpl extends AbstractSteamQueryResultHandler<VcUnitCountryside> implements ISteamQueryResulService {

    @Resource
    private VcUnitCountrysideMapper vcUnitCountrysideMapper;
    @Resource
    private VillageCommunityClient villageCommunityClient;

    @Override
    public void batchSteamQueryHandle() {
        first = true;
        //1.按批次处理查询结果集数据
        vcUnitCountrysideMapper.streamingQuery(this);
        //2.处理最后一个批次的查询结果数据
        this.lastSteamHandle();
    }

    @Override
    public void handle() {
        try {
            log.info("---------------------:{}", rowDataList.size());

            PushUnitCountrysideDTO pushUnitCountrysideDTO = new PushUnitCountrysideDTO();
            pushUnitCountrysideDTO.setList(rowDataList);
            if (first) {
                pushUnitCountrysideDTO.setFirst(true);
            }
            if (last) {
                pushUnitCountrysideDTO.setLast(true);
            }
            villageCommunityClient.pushUnitCountryside(pushUnitCountrysideDTO);
        } finally {
            // 处理完每批数据后后将临时清空
            size = 0;
            rowDataList.clear();
            if (first) {
                first = false;
            }
            if (last) {
                last = false;
            }
        }
    }

}
