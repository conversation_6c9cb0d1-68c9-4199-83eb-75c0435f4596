package com.zenith.front.villagecommunity.service;

import com.zenith.front.villagecommunity.client.VillageCommunityClient;
import com.zenith.front.villagecommunity.mapper.VcUserInfoMapper;
import com.zenith.front.villagecommunity.model.PushUserInfoDTO;
import com.zenith.front.villagecommunity.model.VcUserInfo;
import com.zenith.front.villagecommunity.resulthandler.AbstractSteamQueryResultHandler;
import com.zenith.front.villagecommunity.resulthandler.ISteamQueryResulService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 村社区人员 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-22
 */
@Slf4j
@Service
public class SteamQueryUserServiceImpl extends AbstractSteamQueryResultHandler<VcUserInfo> implements ISteamQueryResulService {

    @Resource
    private VcUserInfoMapper vcUserInfoMapper;
    @Resource
    private VillageCommunityClient villageCommunityClient;

    @Override
    public void batchSteamQueryHandle() {
        first = true;
        //1.按批次处理查询结果集数据
        vcUserInfoMapper.streamingQuery(this);
        //2.处理最后一个批次的查询结果数据
        this.lastSteamHandle();
    }

    @Override
    public void handle() {
        try {
            log.info("---------------------:{}", rowDataList.size());

            PushUserInfoDTO pushUserInfoDTO = new PushUserInfoDTO();
            pushUserInfoDTO.setList(rowDataList);
            if (first) {
                pushUserInfoDTO.setFirst(true);
            }
            if (last) {
                pushUserInfoDTO.setLast(true);
            }
            villageCommunityClient.pushUserInfo(pushUserInfoDTO);
        } finally {
            // 处理完每批数据后后将临时清空
            size = 0;
            rowDataList.clear();
            if (first) {
                first = false;
            }
            if (last) {
                last = false;
            }
        }
    }
}
