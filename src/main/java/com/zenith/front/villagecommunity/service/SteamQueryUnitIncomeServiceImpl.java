package com.zenith.front.villagecommunity.service;

import com.zenith.front.villagecommunity.client.VillageCommunityClient;
import com.zenith.front.villagecommunity.mapper.VcUnitIncomeMapper;
import com.zenith.front.villagecommunity.model.PushUnitIncomeDTO;
import com.zenith.front.villagecommunity.model.VcUnitIncome;
import com.zenith.front.villagecommunity.resulthandler.AbstractSteamQueryResultHandler;
import com.zenith.front.villagecommunity.resulthandler.ISteamQueryResulService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 推送集体经济 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-15
 */
@Slf4j
@Service
public class SteamQueryUnitIncomeServiceImpl extends AbstractSteamQueryResultHandler<VcUnitIncome> implements ISteamQueryResulService {

    @Resource
    private VcUnitIncomeMapper vcUnitIncomeMapper;
    @Resource
    private VillageCommunityClient villageCommunityClient;

    @Override
    public void batchSteamQueryHandle() {
        first = true;
        //1.按批次处理查询结果集数据
        vcUnitIncomeMapper.streamingQuery(this);
        //2.处理最后一个批次的查询结果数据
        this.lastSteamHandle();
    }

    @Override
    public void handle() {
        try {
            log.info("---------------------:{}", rowDataList.size());

            PushUnitIncomeDTO pushUnitInfoDTO = new PushUnitIncomeDTO();
            pushUnitInfoDTO.setList(rowDataList);
            if (first) {
                pushUnitInfoDTO.setFirst(true);
            }
            if (last) {
                pushUnitInfoDTO.setLast(true);
            }
            villageCommunityClient.pushUnitIncome(pushUnitInfoDTO);
        } finally {
            // 处理完每批数据后后将临时清空
            size = 0;
            rowDataList.clear();
            if (first) {
                first = false;
            }
            if (last) {
                last = false;
            }
        }
    }
}
