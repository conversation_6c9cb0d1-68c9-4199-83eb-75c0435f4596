package com.zenith.front.villagecommunity.service;

import cn.hutool.core.date.DateUtil;
import com.zenith.front.villagecommunity.client.VillageCommunityClient;
import com.zenith.front.villagecommunity.mapper.VcMemInfoMapper;
import com.zenith.front.villagecommunity.model.PushMembersDTO;
import com.zenith.front.villagecommunity.model.VcMemInfo;
import com.zenith.front.villagecommunity.pojo.MemInfo;
import com.zenith.front.villagecommunity.resulthandler.AbstractSteamQueryResultHandler;
import com.zenith.front.villagecommunity.resulthandler.ISteamQueryResulService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 村社区人员 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-22
 */
@Slf4j
@Service
public class SteamQueryMemServiceImpl extends AbstractSteamQueryResultHandler<VcMemInfo> implements ISteamQueryResulService {

    @Resource
    private VcMemInfoMapper memInfoMapper;
    @Resource
    private VillageCommunityClient villageCommunityClient;

    @Override
    public void batchSteamQueryHandle() {
        first = true;
        //1.按批次处理查询结果集数据
        memInfoMapper.streamingQuery(this);
        //2.处理最后一个批次的查询结果数据
        this.lastSteamHandle();
    }

    @Override
    public void handle() {
        try {
            log.info("---------------------:{}", rowDataList.size());

            List<MemInfo> memInfoList = new ArrayList<>();
            //list 批量查询结果集,对此list进行业务处理
            for (VcMemInfo villageCommunity : rowDataList) {
                MemInfo memInfo = new MemInfo();
                memInfo.setId(villageCommunity.getCode());
                memInfo.setIdCard(villageCommunity.getIdcard());
                memInfo.setPartyMem(villageCommunity.getPartyMem());
                memInfo.setMemType(villageCommunity.getD08Code());
                memInfo.setJoinOrgDate(DateUtil.formatDate(villageCommunity.getJoinOrgDate()));
                memInfoList.add(memInfo);
            }
            PushMembersDTO pushMembersDTO = new PushMembersDTO();
            pushMembersDTO.setList(memInfoList);
            if (first) {
                pushMembersDTO.setFirst(true);
            }
            if (last) {
                pushMembersDTO.setLast(true);
            }
            villageCommunityClient.pushMemInfo(pushMembersDTO);
        } finally {
            // 处理完每批数据后后将临时清空
            size = 0;
            rowDataList.clear();
            if (first) {
                first = false;
            }
            if (last) {
                last = false;
            }
        }
    }
}
