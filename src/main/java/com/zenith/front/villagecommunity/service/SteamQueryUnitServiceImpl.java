package com.zenith.front.villagecommunity.service;

import com.zenith.front.villagecommunity.client.VillageCommunityClient;
import com.zenith.front.villagecommunity.mapper.VcUnitInfoMapper;
import com.zenith.front.villagecommunity.model.PushUnitInfoDTO;
import com.zenith.front.villagecommunity.model.VcUnitInfo;
import com.zenith.front.villagecommunity.resulthandler.AbstractSteamQueryResultHandler;
import com.zenith.front.villagecommunity.resulthandler.ISteamQueryResulService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 村社区人员 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-22
 */
@Slf4j
@Service
public class SteamQueryUnitServiceImpl extends AbstractSteamQueryResultHandler<VcUnitInfo> implements ISteamQueryResulService {

    @Resource
    private VcUnitInfoMapper vcUnitInfoMapper;
    @Resource
    private VillageCommunityClient villageCommunityClient;

    @Override
    public void batchSteamQueryHandle() {
        first = true;
        //1.按批次处理查询结果集数据
        vcUnitInfoMapper.streamingQuery(this);
        //2.处理最后一个批次的查询结果数据
        this.lastSteamHandle();
    }

    @Override
    public void handle() {
        try {
            log.info("---------------------:{}", rowDataList.size());

            PushUnitInfoDTO pushUnitInfoDTO = new PushUnitInfoDTO();
            pushUnitInfoDTO.setList(rowDataList);
            if (first) {
                pushUnitInfoDTO.setFirst(true);
            }
            if (last) {
                pushUnitInfoDTO.setLast(true);
            }
            villageCommunityClient.pushUnitInfo(pushUnitInfoDTO);
        } finally {
            // 处理完每批数据后后将临时清空
            size = 0;
            rowDataList.clear();
            if (first) {
                first = false;
            }
            if (last) {
                last = false;
            }
        }
    }
}
