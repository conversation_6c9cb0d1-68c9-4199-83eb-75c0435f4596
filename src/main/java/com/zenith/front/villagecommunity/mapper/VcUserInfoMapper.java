package com.zenith.front.villagecommunity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.front.villagecommunity.model.VcUserInfo;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.ResultType;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.mapping.ResultSetType;
import org.apache.ibatis.session.ResultHandler;

/**
 * <p>
 * 党外系统人员信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-23
 */
public interface VcUserInfoMapper extends BaseMapper<VcUserInfo> {

    String SQL = "SELECT * FROM vc_user_info";

    /**
     * 流式查询接口
     *
     * @param handler handler
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2022年08月22日 19时03分
     */
    @Select(SQL)
    @Options(resultSetType = ResultSetType.FORWARD_ONLY, fetchSize = 1000)
    @ResultType(VcUserInfo.class)
    void streamingQuery(ResultHandler<VcUserInfo> handler);
}
