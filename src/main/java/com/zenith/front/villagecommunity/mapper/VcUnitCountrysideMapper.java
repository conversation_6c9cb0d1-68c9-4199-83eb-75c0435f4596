package com.zenith.front.villagecommunity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.front.villagecommunity.model.VcUnitCountryside;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.ResultType;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.mapping.ResultSetType;
import org.apache.ibatis.session.ResultHandler;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-15
 */
public interface VcUnitCountrysideMapper extends BaseMapper<VcUnitCountryside> {

    String SQL = "SELECT * FROM vc_unit_countryside";

    /**
     * 流式查询接口
     *
     * @param handler handler
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2022年08月22日 19时03分
     */
    @Select(SQL)
    @Options(resultSetType = ResultSetType.FORWARD_ONLY, fetchSize = 1000)
    @ResultType(VcUnitCountryside.class)
    void streamingQuery(ResultHandler<VcUnitCountryside> handler);


}
