package com.zenith.front.villagecommunity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.front.villagecommunity.model.VcMemInfo;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.ResultType;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.mapping.ResultSetType;
import org.apache.ibatis.session.ResultHandler;

/**
 * <p>
 * 村社区人员 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-23
 */
public interface VcMemInfoMapper extends BaseMapper<VcMemInfo> {

    String SQL = "SELECT * FROM vc_mem_info";

    /**
     * 流式查询接口
     *
     * @param handler handler
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2022年08月22日 19时03分
     */
    @Select(SQL)
    @Options(resultSetType = ResultSetType.FORWARD_ONLY, fetchSize = 1000)
    @ResultType(VcMemInfo.class)
    void streamingQuery(ResultHandler<VcMemInfo> handler);

    /**
     * 拉取村社区系统人员时清空人员表信息
     *
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2022年08月26日 12时35分
     */
    void clear();
}
