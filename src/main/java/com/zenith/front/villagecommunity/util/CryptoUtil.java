package com.zenith.front.villagecommunity.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import cn.hutool.crypto.symmetric.SymmetricCrypto;

import java.nio.charset.StandardCharsets;
import java.security.KeyPair;

/**
 * 加解密工具类
 *
 * <AUTHOR>
 * @since 2022/8/18 14:51
 */
public class CryptoUtil {

    public static final String publicStr = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDrxQ6GmmRGFYxdjUE/wG+D2Ill1Lwin4xA+o684zNHgc+vKZ3uEXMJoUK3zgKCIIWaAZMp2+Ot0PZzZzwfg9cZeyrLf2COpcqVS0ruMhiWv/LISX0LB+hpAfe/Vi6INSWFjN2utfL/e4SLx/WVGd1GGXxWLHrPeSiweuW0mu6EKwIDAQAB";
    public static final String privateStr = "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAOvFDoaaZEYVjF2NQT/Ab4PYiWXUvCKfjED6jrzjM0eBz68pne4RcwmhQrfOAoIghZoBkynb463Q9nNnPB+D1xl7Kst/YI6lypVLSu4yGJa/8shJfQsH6GkB979WLog1JYWM3a618v97hIvH9ZUZ3UYZfFYses95KLB65bSa7oQrAgMBAAECgYACbcthkUvuUR116vN1+YQRo6cf+XAGvPiRxHxMzdwVYY6os7l6DYv3h78IAD/yk3OdviDv4rjg5svjLU/TJdEw0jWMmL4JcRQoAXMMzDIvhUJY8txNFoY95gRlUxOI+8qchfa48Yuri6LW2fczw4cRThM/LfUk7dcLlqSgfV/YgQJBAP8VEq/TCfRSn93SDaZYXVSauL6vLUmn+m99HbPSp18PSfyuiq3aRmGfm45uXPVxV1yqg+9cFZZ+hf5vfczoaPsCQQDsnjJ5TP1TTq1LlczLrWnDuIfcJrs5mLfVYX8cPuEDBb/Ggw1rU4ov+tNDIHeGuwsKmYwsog/X5eR6Tf3ZT8qRAkAixNeQOb7gm6aI0Mnlg6PtFU3bR/tvT8M2ESLMUQlZTrcyBwSGH/t58Qx+BJMNLY7P5booFTAoHETdYosONBujAkBKLiZgxPQ+Y9IMekAkMo+P3pApQrM7kVdAvDofXs8oJWtEDt3sIdqWxCnrR4hxYr5Jam35qC0TYG1Kspy8vjkhAkBzrKVobuPqf9DswHdm0Y+XEjzQ96ZfXGx3xQymT5yBmOAC2+JCpdUROg4xWKR+ZwiZSwIYsPk5w38D++ac7Uhl";

    /**
     * 获取密钥
     *
     * @return 密钥
     */
    public static String symmetricKey() {
        return new String(SecureUtil.generateKey(SymmetricAlgorithm.AES.getValue()).getEncoded(), StandardCharsets.ISO_8859_1);
    }

    /**
     * 根据密钥获取加解密对象
     *
     * @param key 密钥
     * @return 加解密对象
     */
    public static SymmetricCrypto symmetricCrypto(String key) {
        return new SymmetricCrypto(SymmetricAlgorithm.AES, key.getBytes(StandardCharsets.ISO_8859_1));
    }

    /**
     * 数据加密
     *
     * @param key  密钥
     * @param data 数据包
     * @return 加密数据包
     */
    public static String symmetricEncrypt(String key, String data) {
        final byte[] encrypt = symmetricCrypto(key).encrypt(data.getBytes(StandardCharsets.UTF_8));
        return Base64.encode(encrypt);
    }

    /**
     * 数据解密
     *
     * @param key  密钥
     * @param data 加密数据包
     * @return 解密数据包
     */
    public static String symmetricDecrypt(String key, String data) {
        final byte[] encrypt = symmetricCrypto(key).decrypt(Base64.decode(data));
        return new String(encrypt, StandardCharsets.UTF_8);
    }

    /**
     * 数据加密
     *
     * @param key  密钥
     * @param data 数据包
     * @return 加密数据包
     */
    public static String asymmetricEncrypt(String key, String data) {
        RSA rsa = new RSA(null, key);
        final byte[] decrypt = rsa.encrypt(data.getBytes(StandardCharsets.UTF_8), KeyType.PublicKey);
        return Base64.encode(decrypt);
    }

    /**
     * 数据解密
     *
     * @param key  密钥
     * @param data 加密数据包
     * @return 解密数据包
     */
    public static String asymmetricDecrypt(String key, String data) {
        RSA rsa = new RSA(key, null);
        final byte[] decrypt = rsa.decrypt(Base64.decode(data), KeyType.PrivateKey);
        return new String(decrypt, StandardCharsets.UTF_8);
    }

    public static void main(String[] args) {
        KeyPair pair = SecureUtil.generateKeyPair("RSA");
        System.out.println("私钥：" + Base64.encode(pair.getPrivate().getEncoded()));
        System.out.println("公钥：" + Base64.encode(pair.getPublic().getEncoded()));
        // 随机生成密钥
        String content = "{\"name\":\"张三\",\"age\":25}";
        // 获取对称加密key
        final String key = CryptoUtil.symmetricKey();
        // 数据加密
        String encrypt = CryptoUtil.symmetricEncrypt(key, content);
        CryptoData cryptoData = new CryptoData();
        cryptoData.setData(encrypt);
        // key 加密
        final String keyEncrypt = CryptoUtil.asymmetricEncrypt(publicStr, key);
        cryptoData.setKey(keyEncrypt);
        System.out.println(cryptoData);
        // key 解密
        String keyDecrypt = CryptoUtil.asymmetricDecrypt(privateStr, cryptoData.getKey());
        // 数据解密
        final String decryptData = CryptoUtil.symmetricDecrypt(keyDecrypt, cryptoData.getData());
        System.out.println(decryptData);
    }
}
