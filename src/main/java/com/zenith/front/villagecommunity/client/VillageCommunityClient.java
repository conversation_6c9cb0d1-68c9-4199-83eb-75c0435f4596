package com.zenith.front.villagecommunity.client;

import com.zenith.front.villagecommunity.model.*;
import com.zenith.front.villagecommunity.pojo.Result;
import com.zenith.front.villagecommunity.util.CryptoData;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 村社区系统客户端
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2022/8/18 15:10
 */
public interface VillageCommunityClient {

    /**
     * 拉取村社区系统人员身份证号
     *
     * @param villageCommunityMemberDTO 分页请求参数
     * @return com.zenith.front.villagecommunity.pojo.Result<com.zenith.front.villagecommunity.util.CryptoData>
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2022年08月19日 14时08分
     */
    @PostMapping(value = "/external/listCadreUser", consumes = "application/json")
    Result<CryptoData> pullMemInfo(@RequestBody VillageCommunityMemberDTO villageCommunityMemberDTO);

    /**
     * 推送已经验证过的村社区人员
     *
     * @param pushMembersDTO 村社区人员实体类
     * @return com.zenith.front.villagecommunity.util.CryptoData
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2022年08月22日 15时19分
     */
    @PostMapping(value = "/external/updateBatchByCryptoData", consumes = "application/json")
    CryptoData pushMemInfo(@RequestBody PushMembersDTO pushMembersDTO);

    /**
     * 推送村社区关联用户
     *
     * @param pushUserInfoDTO 用户实体
     * @return com.zenith.front.villagecommunity.util.CryptoData
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2022年08月22日 15时19分
     */
    @PostMapping(value = "/external/saveBatchByCryptoData", consumes = "application/json")
    CryptoData pushUserInfo(@RequestBody PushUserInfoDTO pushUserInfoDTO);

    /**
     * 推送村社区关联单位
     *
     * @param pushUnitInfoDTO 单位实体
     * @return com.zenith.front.villagecommunity.util.CryptoData
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2022年08月22日 15时19分
     */
    @PostMapping(value = "/external/saveOrgBatchByCryptoData", consumes = "application/json")
    CryptoData pushUnitInfo(@RequestBody PushUnitInfoDTO pushUnitInfoDTO);

    /**
     * 推送村社区关联发展党员
     *
     * @param pushDevelopMemInfoDTO 发展党员实体
     * @return com.zenith.front.villagecommunity.util.CryptoData
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2022年08月22日 15时19分
     */
    @PostMapping(value = "/external/saveBatchByMemInfo", consumes = "application/json")
    CryptoData pushDevelopMemInfo(@RequestBody PushDevelopMemInfoDTO pushDevelopMemInfoDTO);

    /**
     * 推送集体经济
     */
    @PostMapping(value = "/external/syncUnitCollectiveEconomic", consumes = "application/json")
    CryptoData pushUnitCollectiveEconomic(@RequestBody PushUnitCollectiveEconomicDTO pushUnitCollectiveEconomicDTO);

    @PostMapping(value = "/external/syncUnitIncome", consumes = "application/json")
    CryptoData pushUnitIncome(@RequestBody PushUnitIncomeDTO pushUnitIncomeDTO);

    @PostMapping(value = "/external/syncUnitCountryside", consumes = "application/json")
    CryptoData pushUnitCountryside(@RequestBody PushUnitCountrysideDTO pushUnitCountrysideDTO);




}
