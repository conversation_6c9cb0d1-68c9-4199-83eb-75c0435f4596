package com.zenith.front.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.entity.dto.UnitListDTO;
import com.zenith.front.entity.model.UnitExchangeArea;
import org.apache.ibatis.annotations.Param;

public interface UnitExchangeAreaMapper extends BaseMapper<UnitExchangeArea> {

    /**
     * 列表用的查询
     *
     * @param page
     * @param unitListDTO 参数
     * @return 列表数据
     */
    Page<UnitExchangeArea> findOrgByName(@Param("page")Page<UnitExchangeArea> page, @Param("unitListDTO") UnitListDTO unitListDTO);


    UnitExchangeArea findUnitByCode(@Param("code") String code);
}
