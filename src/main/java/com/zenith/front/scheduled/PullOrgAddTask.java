package com.zenith.front.scheduled;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.webservice.dzz.DzzUploadAndDownbusinessServiceImpl;
import com.webservice.dzz.DzzUploadAndDownbusinessServiceImplService;
import com.webservice.util.CryptoUtil;
import com.zenith.front.config.TaskConfig;
import com.zenith.front.constant.CommonConstant;
import com.zenith.front.entity.model.TransferOrg;
import com.zenith.front.entity.vo.TransferOrgVO;
import com.zenith.front.service.relationshiptransfer.ITransferOrgService;
import com.zenith.front.untils.StrKit;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;

import java.util.*;

import static com.webservice.util.CryptoUtil.cnToUnicode;
import static com.webservice.util.CryptoUtil.getQianMingData;

/**
 * @author: D.watermelon
 * @date: 2022/5/12 15:40
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 */

@Component("pullOrgAddTask")
@Slf4j
public class PullOrgAddTask {
    @Autowired
    private ITransferOrgService transferOrgService;
    
//    @Autowired
//    private TaskConfig taskConfig;

//    @Scheduled(cron = "${task.pullOrgAddTask.cron}")
    public void ZlTsak() throws Exception {
//        if (!taskConfig.getPullOrgAddTask().isEnabled()) {
//            log.info("PullOrgAddTask is disabled");
//            return;
//        }
        log.info("执行获取党组织增量接口情况，开始时间：：" + DateUtil.now());
        DzzUploadAndDownbusinessServiceImplService serviceImplService = new DzzUploadAndDownbusinessServiceImplService();
        DzzUploadAndDownbusinessServiceImpl serviceImplPort = serviceImplService.getDzzUploadAndDownbusinessServiceImplPort();
        JSONObject jsonObject = new JSONObject();
        Map<String, Object> map = new HashMap<>();
        //时间
        map.put("date", DateUtil.format(new Date(), "yyyyMMdd"));
        //本省根节点 s
        map.put("accessID", "052000000001");
        map.put("xzqh", "052000000001");
        map.put("dataType", "1");
        jsonObject.putAll(map);
        Map<String, String> stringMap = CryptoUtil.signData(jsonObject.toString(), "1");
        String signData = stringMap.get("signData");
        String data = stringMap.get("data");
        String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
        String unicode = cnToUnicode(qianMingData);
        String resultStr = null;
        try {
            resultStr = serviceImplPort.download(unicode);
        } catch (Exception e) {
            log.error("下载党组织增量数据失败", e);
        }
        if (StrUtil.isNotEmpty(resultStr)) {
            String s = new String(Base64Utils.decode(resultStr.getBytes()), "utf-8");
            cn.hutool.json.JSONObject info = JSONUtil.parseObj(s).getJSONObject("info");
            if (Objects.nonNull(info)) {
//                Object secretKey = JSONUtil.parseObj(s).get("secretKey");
//                String realInfo = CryptoUtil.getRealInfo(object.toString(), secretKey.toString());
//                JSONArray array = JSONUtil.parseArray(realInfo);
                JSONArray array = info.getJSONArray("DZZLIST");
                List<TransferOrgVO> transferOrgVOS = JSONUtil.toList(array, TransferOrgVO.class);
                for (TransferOrgVO transferOrgVO : transferOrgVOS) {
                    TransferOrg transferOrg = new TransferOrg();
                    transferOrg.setId(StrKit.getRandomUUID());
                    transferOrg.setD01id(transferOrgVO.getD01ID());
                    transferOrg.setJddm(transferOrgVO.getJDDM());
                    transferOrg.setD01uid(transferOrgVO.getD01UID());
                    transferOrg.setOp01(transferOrgVO.getOP01());
                    transferOrg.setD01001(transferOrgVO.getD01001());
                    transferOrg.setD01002(transferOrgVO.getD01002());
                    transferOrg.setD01003(transferOrgVO.getD01003());
                    transferOrg.setD01004(transferOrgVO.getD01004());
                    transferOrg.setD01005(transferOrgVO.getD01005());
                    transferOrg.setD01006(transferOrgVO.getD01006());
                    transferOrg.setD01007(transferOrgVO.getD01007());
                    transferOrg.setD01008(transferOrgVO.getD01008());
                    transferOrg.setD01009(transferOrgVO.getD01009());
                    transferOrg.setD01up1(CommonConstant.THREE.equals(transferOrgVO.getOP01()) ? "1" : "0");
                    transferOrg.setUpdateTime(new Date());
                    transferOrgService.saveOrUpdate(transferOrg);
                }
            }
        }
        log.info("定时任务更新组织任务完成，完成时间：：" + DateUtil.now());
    }

}
