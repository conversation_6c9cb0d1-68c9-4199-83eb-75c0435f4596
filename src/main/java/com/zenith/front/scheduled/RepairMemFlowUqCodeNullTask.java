package com.zenith.front.scheduled;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.zenith.front.config.TaskConfig;
import com.zenith.front.constant.CommonConstant;
import com.zenith.front.entity.model.MemFlow1;
import com.zenith.front.mapper.FlowUploadMapper;
import com.zenith.front.mapper.MemFlow1Mapper;
import com.zenith.front.service.flow.MemFlow1Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/1/21 20:17
 * @description: 修复流动党员唯一标识为空未上传问题
 */
// @Component
@Slf4j
@Deprecated
public class RepairMemFlowUqCodeNullTask {
    @Resource
    private FlowUploadMapper flowUploadMapper;
    @Autowired
    private MemFlow1Service memFlow1Service;
    @Autowired
    private MemFlow1Mapper memFlow1Mapper;
    @Autowired
    private TaskConfig taskConfig;

    @Scheduled(cron = "${task.repairMemFlowUqCodeNull.cron}")
    public void repairMemFlowUploadData() {
        if (!taskConfig.getRepairMemFlowUqCodeNull().isEnabled()) {
            log.info("RepairMemFlowUqCodeNull task is disabled");
            return;
        }
        try {
            log.info("开始修复流动党员唯一标识为空未上传问题");
            
            List<MemFlow1> nullUqCodeList = findNullUqCodeList();
            if (nullUqCodeList.isEmpty()) {
                log.info("未发现需要修复的唯一标识为空记录");
                return;
            }
            
            log.info("发现{}条唯一标识为空记录需要修复", nullUqCodeList.size());
            
            for (MemFlow1 memFlow : nullUqCodeList) {
                processMemFlow(memFlow);
            }
            
            log.info("流动党员唯一标识为空未上传问题修复完成");
        } catch (Exception e) {
            log.error("修复唯一标识为空记录时发生错误", e);
            throw e;
        }
    }

    private List<MemFlow1> findNullUqCodeList() {
        return memFlow1Mapper.selectList(new QueryWrapper<MemFlow1>().lambda()
                .isNull(MemFlow1::getFlowUqCode)
                .eq(MemFlow1::getIsProvince, "0")
                .ge(MemFlow1::getCreateTime, DateUtil.parseDate("2025-02-01"))
                .isNotNull(MemFlow1::getCreateTime)
                .eq(MemFlow1::getFlowStep, "1")
                .orderByAsc(MemFlow1::getCreateTime));
    }

    private void processMemFlow(MemFlow1 memFlow) {
        log.info("开始处理流动党员记录 - 党员姓名: {}", memFlow.getMemName());
        
        try {
            // 设置nginx key
            memFlow1Service.setNginxKey(memFlow, "1");
            
            // 上传到中组部
            uploadAddMemFlowToZZB(memFlow);
            
            log.info("流动党员记录处理完成 - 党员姓名: {}", memFlow.getMemName());
        } catch (Exception e) {
            log.error("处理流动党员记录时发生错误 - 党员姓名: {}", memFlow.getMemName(), e);
            throw e;
        }
    }

    private void uploadAddMemFlowToZZB(MemFlow1 memFlow) {
        String flowCode = memFlow.getCode();
        String flowUqCode = recreateFlowUqCode(memFlow.getCreateTime());
        
        log.info("开始上传流动党员数据 - 党员姓名: {}, 流动码: {}, 唯一码: {}", 
                memFlow.getMemName(), flowCode, flowUqCode);
        
        memFlow.setFlowUqCode(flowUqCode);
        JSONObject uploadMemFlowData = memFlow1Service.createUploadMemFlowData(memFlow);
        log.info("上传流动党员结构数据：{}", uploadMemFlowData.toJSONString());
        
        String infoUpload = uploadMemFlowData.getString("infoUpload");
        try {
            uploadMemFlowData.remove("infoUpload");
            String resultJson;
            if (taskConfig.getSyncToZzB().isEnabled()) {
                resultJson = memFlow1Service.uploadLDDY(uploadMemFlowData, false);
            } else {
                resultJson = CommonConstant.NOT_UPLOAD_EXCHANGE;
            }
            
            log.info("上传流动党员结果数据 - 党员姓名: {}, 唯一码: {}, 结果: {}", 
                    memFlow.getMemName(), flowUqCode, resultJson);
            
            uploadMemFlowData.put("info", infoUpload);
            memFlow1Service.deailWithUploadData(resultJson, flowCode, flowUqCode, 
                    CommonConstant.ONE_INT, uploadMemFlowData.toJSONString());
            
            // 更新唯一码
            LambdaUpdateWrapper<MemFlow1> updateWrapper = new UpdateWrapper<MemFlow1>().lambda()
                    .set(MemFlow1::getFlowUqCode, flowUqCode)
                    .eq(MemFlow1::getCode, flowCode);
            memFlow1Service.update(updateWrapper);
            
        } catch (Exception e) {
            log.error("上传流动党员数据时发生错误 - 党员姓名: {}, 唯一码: {}", 
                    memFlow.getMemName(), flowUqCode, e);
            
            uploadMemFlowData.put("info", infoUpload);
            memFlow1Service.catchDeailData(flowCode, flowUqCode, 
                    CommonConstant.ONE_INT, uploadMemFlowData.toJSONString());
            
            // 更新唯一码
            MemFlow1 updateMemFlow = new MemFlow1();
            updateMemFlow.setCode(flowCode);
            updateMemFlow.setFlowUqCode(flowUqCode);
            memFlow1Service.updateById(updateMemFlow);
        }
    }

    private String recreateFlowUqCode(Date createTime) {
        String yyyyMMdd = "052000012223" + DateUtil.format(createTime, "yyyyMMdd");
        String nextUniqueCode = memFlow1Service.generateNextUniqueCodeByDate(yyyyMMdd);
        log.info("生成新唯一码: {}", nextUniqueCode);
        return nextUniqueCode;
    }
}
