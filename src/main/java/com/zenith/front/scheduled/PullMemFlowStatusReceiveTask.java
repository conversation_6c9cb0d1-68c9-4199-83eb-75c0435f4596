package com.zenith.front.scheduled;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.webservice.util.CryptoUtil;
import com.zenith.front.config.TaskConfig;
import com.zenith.front.constant.CommonConstant;
import com.zenith.front.entity.model.FlowContent;
import com.zenith.front.entity.model.MemFlow1;
import com.zenith.front.mapper.FlowContentMapper;
import com.zenith.front.service.flow.MemFlow1Service;
import com.zenith.front.service.flow.ZyMemFlowExchangeService;
import com.zenith.front.untils.StrKit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 流动党员接收信息下载
 * <AUTHOR>
 * @since 2025/3/11 15:19
 */
@Component("pullMemFlowStatusReceiveTask")
@Slf4j
public class PullMemFlowStatusReceiveTask {
//    @Autowired
//    private TaskConfig taskConfig;
    @Autowired
    private MemFlow1Service memFlow1Service;
    @Autowired
    private ZyMemFlowExchangeService zyMemFlowExchangeService;
    @Resource
    private FlowContentMapper flowContentMapper;

//    @Scheduled(cron = "${task.pullMemFlowStatusReceiveTask.cron}")
    public void configureTasks() {
//        if (!taskConfig.getPullMemFlowStatusReceiveTask().isEnabled()) {
//            log.info("pullMemFlowStatusReceiveTask is disabled");
//            return;
//        }
        log.info("执行==根据根节点批量接收信息下载接口,开始");
        for (int i = 1; i < 10000; i++) {
            try {
                log.info("根据根节点批量接收信息下载接口，第" + i + "页数下载开始");
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("accessID", "052000000001");
                jsonObject.put("type", "1");
                jsonObject.put("dataType", "33");
                jsonObject.put("pagesize", "100");
                jsonObject.put("pageno", i);
                String resultStr = memFlow1Service.downloadLDDY(jsonObject);

                JSONObject dataJson = JSONObject.parseObject(resultStr);
                // 解密请求数据
                if (StrUtil.isNotEmpty(dataJson.getString("secretKey")) && StrUtil.isNotEmpty(dataJson.getString("info"))) {
                    String realInfo = CryptoUtil.getRealInfo(dataJson.getString("info"), dataJson.getString("secretKey"));
                    if (!realInfo.equals("[]")) {
                        List<String> flowUqCodeList = new ArrayList<>();
                        FlowContent flowContent = new FlowContent();
                        String contentId = StrKit.getRandomUUID();
                        flowContent.setId(contentId);
                        flowContent.setContent(realInfo);
                        flowContent.setCreateTime(new Date());
                        flowContent.setDataType("根据根节点批量接收信息下载接口PullMemFlowStatusReceiveTask");
                        flowContentMapper.insert(flowContent);
                        JSONArray jsonArray = JSONArray.parseArray(realInfo);
                        for (Object object : jsonArray) {
                            JSONObject forObj = (JSONObject) object;
                            String uniqueKey = forObj.getString("uniqueKey");
                            JSONObject lddj = forObj.getJSONObject("LDDJ");
                            boolean flag = this.updateMemFlowByFlowUqCode(uniqueKey, lddj);
                            if (flag) {
                                flowUqCodeList.add(uniqueKey);
                            }
                        }
                        flowContent.setFlowUqCode(JSONUtil.toJsonStr(flowUqCodeList));
                        flowContentMapper.updateById(flowContent);
                        if (CollUtil.isNotEmpty(flowUqCodeList)) {
                            zyMemFlowExchangeService.confirm("3", "1", flowUqCodeList);
                        }
                        log.info("根据根节点批量接收信息下载接口，第" + i + "页数下载完成");
                    } else {
                        log.info("根据根节点批量接收信息下载接口，所有数据下载完成！");
                        break;
                    }
                } else {
                    log.info("根据根节点批量接收信息下载接口，所有数据下载完成！");
                    break;
                }
            } catch (Exception e) {
                log.error("批量下载接收消息异常", e);
            }

        }

        log.info("执行==根据根节点批量接收信息下载接口,完成");
    }

    private boolean updateMemFlowByFlowUqCode(String flowUqCode, JSONObject jsonObject) {
        try {
            MemFlow1 memFlow = memFlow1Service.findByUqCode(flowUqCode);
            if (Objects.isNull(memFlow)) {
                return false;
            }
            String flowStep = memFlow.getFlowStep();
            if(!StrUtil.equals(flowStep,"1")){
                return true;
            }
            String lddj0015 = jsonObject.getString("LDDJ0015");
            memFlow.setInOrgPhone(StrUtil.isBlank(lddj0015) ? "" : lddj0015);
            jsonObject.getString("LDDJ0014");//流入地党支部书记
            String lddj0024 = jsonObject.getString("LDDJ0024");
            memFlow.setInOrgCode(StrUtil.isBlank(lddj0024) ? "" : lddj0024);
            String lddj0013 = jsonObject.getString("LDDJ0013");
            memFlow.setInMemD09Code(StrUtil.isBlank(lddj0013) ? "" : lddj0013);
            memFlow.setInMemD09Name(StrUtil.isBlank(lddj0013) ? "" : lddj0013);
            String lddj0017 = jsonObject.getString("LDDJ0017");
            //10:已登记 20 已接收 30 被退回 40 已流回 50 被终止 70 已撤销
            // 1已流出（未纳入管理） 2已纳入支部管理 3流出被退回 4流出终止 5流动完成 6撤销流出
            if (lddj0017.equals(CommonConstant.TWENTY)) {
                memFlow.setFlowStep(CommonConstant.TWO);
                Date lddj0027 = jsonObject.getDate("LDDJ0027");
                memFlow.setInReceivingTime(ObjectUtil.isNull(lddj0027) ? new Date() : lddj0027);
            }
            if (lddj0017.equals(CommonConstant.THIRTY)) {
                memFlow.setFlowStep(CommonConstant.THREE);
            }
            if (lddj0017.equals(CommonConstant.FOURTY)) {
                memFlow.setFlowStep(CommonConstant.FIVE);
                memFlow.setHasFlowBack(CommonConstant.ONE);
                Date lddj0028 = jsonObject.getDate("LDDJ0028");
                memFlow.setFlowBackTime(ObjectUtil.isNull(lddj0028) ? new Date() : lddj0028);
            }
            if (lddj0017.equals(CommonConstant.FIVETY)) {
                memFlow.setFlowStep(CommonConstant.FOUR);
            }
            String ldbj0002 = jsonObject.getString("LDBJ0002");
            if (StrUtil.isNotBlank(ldbj0002)) {
                memFlow.setInOrgLifeCode(ldbj0002);
                if (ldbj0002.equals(CommonConstant.ONE)) {
                    memFlow.setInOrgLife("正常参加线下组织生活");
                }
                if (ldbj0002.equals(CommonConstant.TWO)) {
                    memFlow.setInOrgLife("正常参加线上组织生活，半年参加一次线下组织生活");
                }
                if (ldbj0002.equals(CommonConstant.THREE)) {
                    memFlow.setInOrgLife("未正常参加组织生活");
                }
            }
            String ldbj0003 = jsonObject.getString("LDBJ0003");
            memFlow.setInFeedback(StrUtil.isBlank(ldbj0003) ? "" : ldbj0003);
            String ldbj0001 = jsonObject.getString("LDBJ0001");
            memFlow.setPartyExpensesInTime(StrUtil.isBlank(ldbj0001) ? new Date() : new SimpleDateFormat("yyyy-mm").parse(ldbj0001));
            String lddj0012 = jsonObject.getString("LDDJ0012");
            memFlow.setInOrgName(StrUtil.isBlank(lddj0012) ? "" : lddj0012);
            memFlow.setFlowIn(CommonConstant.ONE);
            memFlow.setUpdateTime(new Date());
            memFlow.setFlowMemTypeCode(jsonObject.getString("JYQTDM"));
            memFlow.setFlowMemTypeRemark(jsonObject.getString("JYQTQTXQ"));
            memFlow.setFlowMemTypeNewRemark(jsonObject.getString("XJYQTQTXQ"));
            memFlow.setLrdIsFarmer(jsonObject.getString("LRDSFNMG"));

            return memFlow1Service.updateById(memFlow);
        } catch (Exception e) {
            log.error("执行==根据根节点批量接收信息下载接口,处理数据异常，{}", flowUqCode, e);
        }
        return false;
    }

}
