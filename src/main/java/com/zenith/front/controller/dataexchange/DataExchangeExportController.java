package com.zenith.front.controller.dataexchange;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.common.InMessage;
import com.zenith.front.common.OutMessage;
import com.zenith.front.common.Status;
import com.zenith.front.entity.dto.DataExchangeExportDTO;
import com.zenith.front.entity.model.DataExchangeExport;
import com.zenith.front.service.dataexchange.api.DataExchangeExportService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 导出数据包信息
 *
 * <AUTHOR>
 * @create_date 2024-10-29 17:43
 * @description
 */
@RestController
@RequestMapping("/dataExchangeExport")
public class DataExchangeExportController {

    @Resource
    private DataExchangeExportService dataExchangeExportService;

    /**
     * 列表
     */
    @PostMapping("/list")
    public OutMessage<Page<DataExchangeExport>> list(@RequestBody InMessage<DataExchangeExportDTO> dto) {
        return dataExchangeExportService.list(dto.getData());
    }

    /**
     * 列表
     */
    @GetMapping("/remove")
    public OutMessage<?> remove(@RequestParam("id") String id) {
        return dataExchangeExportService.remove(id);
    }

    /**
     * 验证下载
     *
     * @param id
     * @param password
     */
    @GetMapping(value = "/download")
    public OutMessage download(@RequestParam("id") String id, @RequestParam("password") String password) {
        DataExchangeExport entity = dataExchangeExportService.getById(id);
        if (Objects.isNull(entity) || !StrUtil.equals(password, entity.getPassword())) {
            return new OutMessage<>(500100, "下载密码错误!", null);
        }
        if (Objects.nonNull(entity.getDeleteTime())) {
            return new OutMessage<>(500200, "数据已删除，无法下载!", null);
        }
        return new OutMessage<>(Status.SUCCESS, entity.getZipPath());
    }

    /**
     * 附件下载列表
     */
    @GetMapping(value = "/downloads")
    public OutMessage<?> downloads(@RequestParam("pageNum") int pageNum, @RequestParam("pageSize") int pageSize,
                                   @RequestParam("id") String id, @RequestParam("password") String password) {
        DataExchangeExport entity = dataExchangeExportService.getById(id);
        if (Objects.isNull(entity) || !StrUtil.equals(password, entity.getPassword())) {
            return new OutMessage<>(5001, "下载密码错误!", null);
        }
        if (Objects.nonNull(entity.getDeleteTime())) {
            return new OutMessage<>(5002, "数据已删除，无法下载!", null);
        }
        // 附件下载列表
        Page<Object> page = dataExchangeExportService.getAnnexPathList(pageNum, pageSize, entity.getId());
        return new OutMessage<>(Status.SUCCESS, page);
    }

    /**
     * @param id
     * @return
     */
    @GetMapping(value = "/doneCallback")
    public void doneCallback(@RequestParam("id") String id) {
        dataExchangeExportService.doneCallback(id);
    }
}
