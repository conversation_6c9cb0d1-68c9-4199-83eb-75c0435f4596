package com.zenith.front.controller.flow;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.webservice.dzz.DzzUploadAndDownbusinessServiceImpl;
import com.webservice.dzz.DzzUploadAndDownbusinessServiceImplService;
import com.webservice.util.CryptoUtil;
import com.webservice.util.JackSonUtil;
import com.zenith.front.constant.CommonConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Base64Utils;
import org.springframework.web.bind.annotation.*;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/2/21 11:19
 * @Version 1.0
 */
@RestController
@RequestMapping("/flow")
@Slf4j
public class DzzUpload {
    @PostMapping("dzz")
    @ResponseBody
    public Object dzzUpload(@RequestBody Object body) throws Exception {
        Map<String, String> map = CryptoUtil.signData(JackSonUtil.toJson(body), "1");
        String signData = map.get("signData");
        String data = map.get("data");
        String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
        String unicode = cnToUnicode(qianMingData);
        com.webservice.dzz.DzzUploadAndDownbusinessServiceImplService serviceImplService = new DzzUploadAndDownbusinessServiceImplService();
        DzzUploadAndDownbusinessServiceImpl serviceImplPort = serviceImplService.getDzzUploadAndDownbusinessServiceImplPort();
        String responseJson = serviceImplPort.upload(unicode);
        String responseData = new String(Base64Utils.decode(responseJson.getBytes()), StandardCharsets.UTF_8);
        log.info("接口url：dzz/upload, 报文：" + responseData);
        return responseData;
    }

    private String getQianMingData(String data, String signData, String accessId){
        JSONObject jo = new JSONObject();
        jo.put("Data", data);
        jo.put("SignedData", signData);
        jo.put("yqsf", accessId);
        return jo.toString();
    }

    private String cnToUnicode(String cn) {
        final char[] chars = cn.toCharArray();
        StringBuilder returnStr = new StringBuilder();
        for (char aChar : chars) {
            returnStr.append("\\u").append(Integer.toString(aChar, 16));
        }
        return returnStr.toString();
    }

}
