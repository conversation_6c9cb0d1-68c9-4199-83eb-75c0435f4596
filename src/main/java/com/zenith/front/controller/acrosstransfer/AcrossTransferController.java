package com.zenith.front.controller.acrosstransfer;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.webservice.esb.ESBUploadAnQuerybusinessServiceImpl;
import com.webservice.esb.ESBUploadAnQuerybusinessServiceImplService;
import com.webservice.util.CryptoUtil;
import com.webservice.util.JackSonUtil;
import com.zenith.front.common.InMessage;
import com.zenith.front.common.OutMessage;
import com.zenith.front.common.Status;
import com.zenith.front.constant.CommonConstant;
import com.zenith.front.controller.base.BaseController;
import com.zenith.front.entity.dto.CLGCDTO;
import com.zenith.front.entity.vo.JSXVO;
import com.zenith.front.entity.vo.ProvincesVo;
import com.zenith.front.service.relationshiptransfer.ITransferLetterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Base64Utils;
import org.springframework.web.bind.annotation.*;

import java.nio.charset.StandardCharsets;
import java.util.*;
import static com.webservice.util.CryptoUtil.cnToUnicode;
import static com.webservice.util.CryptoUtil.getQianMingData;

/**
 * <AUTHOR>
 * @Date 2022/4/13 12:24
 * @Version 1.0
 */
@RestController
@RequestMapping("/across")
public class AcrossTransferController extends BaseController {

    @Autowired
    private ITransferLetterService letterService;

    /**
     * 上传跨省组织关系转接
     *
     * @param
     * @return
     * @throws Exception
     */
    @PostMapping("/uploadESB")
    public OutMessage uploadESB(@RequestBody JSONObject object) throws Exception {
        return letterService.uploadESB(object);
    }

    /**
     * 下载介绍信
     *
     * @throws Exception
     */
    @PostMapping("downloadJSX")
    public void downloadJSX() throws Exception {
        ESBUploadAnQuerybusinessServiceImplService service = new ESBUploadAnQuerybusinessServiceImplService();
        ESBUploadAnQuerybusinessServiceImpl serviceImplPort = service.getESBUploadAnQuerybusinessServiceImplPort();
        cn.hutool.json.JSONObject jsonObject = new cn.hutool.json.JSONObject();
        jsonObject.put("dataType", "1");
        //请求（本地）系统接入标识
        jsonObject.put("xzqh", "052000000001");
        //系统接入标识
        jsonObject.put("accessID", "052000000001");
        Map<String, String> stringMap = CryptoUtil.signData(jsonObject.toString(), "1");
        String signData = stringMap.get("signData");
        String data = stringMap.get("data");
        String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
        String unicode = cnToUnicode(qianMingData);
        String resultStr = null;
        try {
            resultStr = serviceImplPort.query(unicode);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StrUtil.isNotEmpty(resultStr)) {
            String responseData = new String(Base64Utils.decode(resultStr.getBytes()), StandardCharsets.UTF_8);
            JSXVO jsxvo = JackSonUtil.toObject(responseData, JSXVO.class);
            String realInfo = "";
            if (StrUtil.isNotEmpty(jsxvo.getSecretKey()) && StrUtil.isNotEmpty(jsxvo.getInfo())) {
                realInfo = CryptoUtil.getRealInfo(jsxvo.getInfo(), jsxvo.getSecretKey());
                cn.hutool.json.JSONArray objects = JSONUtil.parseArray(realInfo);
                letterService.processLetter(objects);
                List<String> uniqueKeys = new ArrayList<>();
                for (Object object : objects) {
                    String uniqueKey = JSONObject.parseObject(object.toString()).get("uniqueKey").toString();
                    if (StrUtil.isNotEmpty(uniqueKey)) {
                        uniqueKeys.add(uniqueKey);
                    }
                }
                letterService.uploadJsxResult(uniqueKeys);
            } else {
                realInfo = jsxvo.getMsg();
                System.out.println(realInfo);
            }
        } else {
            System.out.println("连接服务器异常");
        }
    }


    /**
     * 上传办理过程
     *
     * @param dto
     * @return
     * @throws Exception
     */
    @PostMapping("uploadBLGC")
    public OutMessage uploadBLGC(@RequestBody InMessage<List<CLGCDTO>> dto) throws Exception {
        return letterService.uploadBLGC(dto.getData());
    }



    @PostMapping("downloadBLGC")
    public void downloadBLGC(@RequestBody InMessage<List<String>> dto) throws Exception {
        ESBUploadAnQuerybusinessServiceImplService service = new ESBUploadAnQuerybusinessServiceImplService();
        ESBUploadAnQuerybusinessServiceImpl serviceImplPort = service.getESBUploadAnQuerybusinessServiceImplPort();
        cn.hutool.json.JSONObject jsonObject = new cn.hutool.json.JSONObject();
        List<String> strings = new ArrayList<>();
        strings.addAll(dto.getData());
        jsonObject.put("dataType", "2");
        //批量查询，多个电子介绍信唯一码
        jsonObject.put("uniqueKey", strings);
        //系统接入标识
        jsonObject.put("accessID", "052000000001");
        Map<String, String> stringMap = CryptoUtil.signData(jsonObject.toString(), "1");
        String signData = stringMap.get("signData");
        String data = stringMap.get("data");
        String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
        String unicode = cnToUnicode(qianMingData);
        String resultStr = serviceImplPort.query(unicode);
        String responseData = new String(Base64Utils.decode(resultStr.getBytes()), StandardCharsets.UTF_8);
        JSXVO jsxvo = JackSonUtil.toObject(responseData, JSXVO.class);
        String realInfo = "";
        if (StrUtil.isNotEmpty(jsxvo.getSecretKey()) && StrUtil.isNotEmpty(jsxvo.getInfo())) {
            realInfo = CryptoUtil.getRealInfo(jsxvo.getInfo(), jsxvo.getSecretKey());
            cn.hutool.json.JSONArray objects = JSONUtil.parseArray(realInfo);
            letterService.processBLGC(objects);
            Set<String> uniqueKeys = new HashSet<>();
            for (Object object : objects) {
                String uniqueKey = JSONObject.parseObject(object.toString()).get("uniqueKey").toString();
                uniqueKeys.add(uniqueKey);
            }
            if (CollectionUtil.isNotEmpty(uniqueKeys)) {
                letterService.uploadBLGCResult(uniqueKeys);
            }
        } else {
            realInfo = jsxvo.getMsg();
        }
        System.out.println("报文：" + realInfo);
    }

    @GetMapping("getProvinces")
    public OutMessage getProvinces() throws Exception {
        ESBUploadAnQuerybusinessServiceImplService service = new ESBUploadAnQuerybusinessServiceImplService();
        ESBUploadAnQuerybusinessServiceImpl serviceImplPort = service.getESBUploadAnQuerybusinessServiceImplPort();
        cn.hutool.json.JSONObject jsonObject = new cn.hutool.json.JSONObject();
        jsonObject.put("dataType", "3");
        //系统接入标识
        jsonObject.put("accessID", "052000000001");
        Map<String, String> stringMap = CryptoUtil.signData(jsonObject.toString(), "1");
        String signData = stringMap.get("signData");
        String data = stringMap.get("data");
        String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
        String unicode = cnToUnicode(qianMingData);
        String resultStr = serviceImplPort.query(unicode);
        String responseData = new String(Base64Utils.decode(resultStr.getBytes()), StandardCharsets.UTF_8);
        String info = JSONUtil.parseObj(responseData).get("info").toString();
        List<ProvincesVo> provincesVos = JSONUtil.parseArray(info).toList(ProvincesVo.class);
        return new OutMessage<>(Status.SUCCESS, provincesVos);
    }
}
