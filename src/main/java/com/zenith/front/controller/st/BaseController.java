package com.zenith.front.controller.st;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.webservice.util.JackSonUtil;
import com.zenith.front.annotation.HasHttpBodyDecrypt;
import com.zenith.front.common.InMessage;
import com.zenith.front.common.OutMessage;
import com.zenith.front.common.Result;
import com.zenith.front.common.Status;
import com.zenith.front.entity.dto.st.Download;
import com.zenith.front.entity.model.st.SysFile;
import com.zenith.front.service.st.SysFileService;
import com.zenith.front.untils.DefaultFileRenamePolicy;
import com.zenith.front.untils.FileRenamePolicy;
import com.zenith.front.untils.StrKit;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Controller;
import org.springframework.util.ResourceUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Controller
public class BaseController {

    @Resource
    public HttpServletRequest httpServletRequest;

    @Resource
    private SysFileService fileService;

    public static String basePath;

    static {
        try {
            basePath = ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX).getPath();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
    }

    static final FileRenamePolicy DEFAULT_FILE_RENAME_POLICY = new DefaultFileRenamePolicy();

    @PostMapping("/base/upload")
    @ResponseBody
    @HasHttpBodyDecrypt
    public OutMessage upload(@RequestParam("file") MultipartFile[] files) throws Exception {
        List<MultipartFile> multipartFileList = Arrays.stream(files).filter(multipartFile -> !multipartFile.isEmpty()).collect(Collectors.toList());

        if (CollUtil.isEmpty(multipartFileList)) {
            return new OutMessage<>(Status.FILE_NULL);
        }
        List<Map<String, String>> records = new ArrayList<>();
        String folder = "upload" + System.getProperty("file.separator") + "st";
        File folderFile = new File(basePath + folder);
        if (!folderFile.getParentFile().exists()) {
            folderFile.getParentFile().mkdir();
        }
        for (MultipartFile uploadFile : multipartFileList) {
            Map<String, String> record = new HashMap<>();
            // 获取文件名
            String name = uploadFile.getOriginalFilename();
            if (StrUtil.isBlank(name)) {
                name = StrKit.getRandomUUID();
            }
            String toFileName = name.replaceAll(" ", "");
            toFileName = new String(toFileName.getBytes(), StandardCharsets.UTF_8);
            //String dest = basePath + "/" + folder + "/" + toFileName;
            String dest = basePath + System.getProperty("file.separator") + folder + System.getProperty("file.separator") + toFileName;
            //String dest=basePath+ "/" +folder+ "/" +toFileName;
            File file = new File(dest);
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdir();
            }
            //文件重命名，防止覆盖
            File renameFile = DEFAULT_FILE_RENAME_POLICY.rename(file);
            uploadFile.transferTo(renameFile);
            record.put("name", renameFile.getName());
            record.put("id", StrKit.getRandomUUID());
            record.put("url", System.getProperty("file.separator") + folder + System.getProperty("file.separator") + renameFile.getName());
            records.add(record);
        }

        // 驻村附件数据库留存记录
        List<SysFile> saves = records.stream().map(file -> {
            // 存储附件表
            SysFile dto = new SysFile();
            dto.setFileName(file.get("name"));
            dto.setFilePath(file.get("url"));
            dto.setId(file.get("id"));
            dto.setStoreType("local");
            dto.setCreateTime(new Date());
            return dto;
        }).collect(Collectors.toList());
        fileService.saveBatch(saves);
        return new OutMessage<>(Status.SUCCESS, records);
    }

    @GetMapping(value = "/upload")
    public void upload(@RequestParam("name") String name, HttpServletResponse response) throws Exception {
        File file = new File(basePath + System.getProperty("file.separator") + "upload" + System.getProperty("file.separator") + name);
        response.setContentType("application/octet-stream");
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(name, "UTF-8"));

        byte[] buffer = new byte[1024];
        try (FileInputStream fis = new FileInputStream(file);
             BufferedInputStream bis = new BufferedInputStream(fis)) {
            OutputStream os = response.getOutputStream();
            int i = bis.read(buffer);
            while (i != -1) {
                os.write(buffer, 0, i);
                i = bis.read(buffer);
            }
        }
    }

    @PostMapping("/base/download")
    @ApiOperation(value = "下载")
    @ResponseBody
    @HasHttpBodyDecrypt
    public void download(@RequestBody InMessage<Download> inMessage, HttpServletResponse response) throws Exception {
        Download download = inMessage.getData();
        File file = new File(basePath + System.getProperty("file.separator") + download.getUrl());
        if (!FileUtil.exist(file)) {
            response.reset();
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().println(JackSonUtil.toJson(Result.build(Status.NOT_FILE)));
            response.flushBuffer();
            return;
        }
        final byte[] bytes = FileUtil.readBytes(file);
        String fileName = URLEncoder.encode(file.getName(), "UTF-8");
        response.reset();
        response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
        response.addHeader("Content-Length", "" + bytes.length);
        response.setContentType("application/octet-stream;charset=UTF-8");
        OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
        outputStream.write(bytes);
        outputStream.flush();
        outputStream.close();
        response.flushBuffer();
    }


}
