package com.zenith.front;

import cn.hutool.extra.spring.EnableSpringUtil;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * @author: D.watermelon
 * @date: 2021/10/13 23:25
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 */
@SpringBootApplication
@EnableTransactionManagement(proxyTargetClass = true)
@EnableScheduling
@EnableAsync
@MapperScan({"com.zenith.**.mapper"})
@EnableConfigurationProperties
@EnableSpringUtil
public class AllUserAppliction {

    public static void main(String[] args) {
        SpringApplication.run(AllUserAppliction.class, args);
    }

}
