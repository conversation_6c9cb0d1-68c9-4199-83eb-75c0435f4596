package com.micheal.springbootpostgresqldemo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.zenith.front.AllUserAppliction;
import com.zenith.front.config.InternalProperties;
import com.zenith.front.constant.FileConstant;
import com.zenith.front.dataexchange.XmlDataExportServiceImpl;
import com.zenith.front.entity.model.MemDigital;
import com.zenith.front.service.devops.IDevOpsService;
import com.zenith.front.untils.DruidUtil;
import com.zenith.front.untils.ShellUtil;
import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.exception.ZipException;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create_date 2025-06-03 14:46
 * @description
 */
@Slf4j
@SpringBootTest(classes = AllUserAppliction.class)
public class MinioCPTest {
    @Autowired
    private IDevOpsService devOpsService;

    public static ThreadPoolTaskExecutor MINIO_EXECUTOR;
    static {
        MINIO_EXECUTOR = new ThreadPoolTaskExecutor();
        // 核心线程数
        MINIO_EXECUTOR.setCorePoolSize(5);
        // 最大线程数
        MINIO_EXECUTOR.setMaxPoolSize(5);
        // 队列数量
        MINIO_EXECUTOR.setQueueCapacity(1000);
        // 空闲线程存活时间 默认秒数
        MINIO_EXECUTOR.setKeepAliveSeconds(20);
        // 自定义线程名前缀
        MINIO_EXECUTOR.setThreadNamePrefix("MINIO_EXECUTOR-");
        // 拒绝策略 AbortPolicy-丢弃任务抛异常（默认） DiscardPolicy-丢弃不抛异常 DiscardOldestPolicy-丢弃队列最前面的任务，CallerRunsPolicy 由主线程（调用线程）处理该任务
        MINIO_EXECUTOR.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 初始化线程池
        MINIO_EXECUTOR.initialize();
    }

    @Test
    void t1() throws InterruptedException, ZipException {
        // tar -cvf - 'data/fzdx/image/202504141634298800e9824251758429f9a61d71f3ec3cf89_转正记录.webp' | pigz > test.tar.gz

        // 1、 将执行目录下的压缩文件及进程清理，然后根据数据分组后分批多线程执行打包压缩
        // 2、 执行的命令进行进程监听
        // 3、 将路径保存到数据库
        // 4、 涉及到光盘大小限制，先不要合并到一个包

        // 获取全部节点信息
        List<String>  verifyDatabaseList = Arrays.asList("052002101-20250414");
        //加载数据源
        DruidUtil.load(verifyDatabaseList);
        XmlDataExportServiceImpl xmlDataExportService = new XmlDataExportServiceImpl();

        // 所有库的档案附件总数
        final List<String> allFilePathList = new ArrayList<>();
        for (String database : verifyDatabaseList) {
            log.warn("开始导出数据节点:{}", database);
            try {
                List<String> filePaths = xmlDataExportService.getMemDigital(database, null)
                        .stream().map(MemDigital::getPath).filter(StrUtil::isNotBlank).collect(Collectors.toList());
                if (CollUtil.isEmpty(filePaths)) {
                    continue;
                }
                allFilePathList.addAll(filePaths);
            } catch (Exception e) {
                log.info("文件复制异常: " + e.getMessage());
            }
        }

        // 根据执行数量分组
        // 计算向上取整总页数
        final int carryCount = 2000; // 执行数量
        List<List<String>> groupList = new ArrayList<>();
        int nums = (int) Math.ceil((double) allFilePathList.size()/(double) carryCount);
        for (int i = 0; i < nums; i++){
            List<String> saveList;
            if(i == (nums-1)){
                saveList = allFilePathList.subList(i * carryCount, allFilePathList.size());
            }else {
                saveList = allFilePathList.subList(i * carryCount, (i + 1) * carryCount);
            }
            log.info("总页：{}；当前页：{}; 数量：{}", nums, i+1, saveList.size());
            groupList.add(saveList);
        }


        // 在本地创建脚本，然后传输过去
        String folderPath = FileConstant.CLASS_PATH + FileConstant.FILE_SEPARATOR + "public" + FileConstant.FILE_SEPARATOR + "minio";
        File folderFile =  new File(folderPath);
        if (!folderFile.exists()) {
            folderFile.mkdirs();
        }
        List<File> shSet = new ArrayList<>(); // 脚本文件
        String strId = DateUtil.format(new Date(), "yyMMddHHmmss");
        final String tarStr = "tar -cvf - ";
        int no = 0;
        for (List<String> paths : groupList) {
            no++;
            // 后台执行
            StringBuilder sb = new StringBuilder(tarStr);
            for (String path : paths) {
                sb.append(" '/home/<USER>/minio/data/").append(path).append("'");
            }
            sb.append(" | pigz > ").append(strId).append("_minio_data_").append(no).append(".tar.gz");
            // 脚本名称
            String shName = "minio_data_" + no + ".sh";
            // 将执行内容写入脚本中
            File file = FileUtil.writeUtf8String(sb.toString(), folderPath + FileConstant.FILE_SEPARATOR + shName);
            shSet.add(file);
        }
        // 总任务脚本
        StringBuilder jobSb = new StringBuilder("ps -ef | grep \"" + tarStr + "\" | grep -v grep | awk '{print $2}' | xargs kill");
        for (File file : shSet) {
            jobSb.append("\n").append("nohup  sh ").append(file.getName()).append(" > /dev/null 2>&1 &");
        }
        // 监听脚本中打包进程是否结束，结束后请求接口修改为完成
        jobSb.append("sleep 5\n") // 等待5秒
                .append("while true\n")
                .append("do\n")
                .append("PID=$(ps -ef | grep \"" + tarStr + "\" | grep -v grep | awk '{print $2}')\n")
                .append("if [ -z \"$PID\" ]; then\n")
                .append("# 这里替换为要执行的任务命令\n") // 这里可替换为请求完成接口
                .append("echo \"任务执行完成！\"  > done.log\n")
                .append("exit 0\n")
                .append("fi\n")
                .append("sleep 5  # 每5秒检查一次\n")
                .append("done\n");
        File jobFile = FileUtil.writeUtf8String(jobSb.toString(), folderPath + FileConstant.FILE_SEPARATOR + "job.sh");

        // 添加压缩文件
        File f = new File(folderPath + FileConstant.FILE_SEPARATOR  + "back_minio.zip");
        if (FileUtil.exist(f)) {
            FileUtil.del(f);
        }
        ZipFile zipFile = new ZipFile(f);
        zipFile.addFiles(shSet);
        zipFile.addFile(jobFile);

        // 将打包程序放到 /tmp 临时目录
        final String targetPath = "/tmp/minio-back/";
        final InternalProperties.SshProperties ssh = new InternalProperties.SshProperties();
        ssh.setSshUser("root");
        ssh.setSshPass("&GZY)#A8AA");
        ssh.setSshHost("************");
        ssh.setSshPort(22);
        // 传输压缩包到40服务器
        // 跳到指定目录，当前目录下
        String[] mkdirCmd = new String[]{
                "rm -rf " + targetPath, // 删除临时打包文件夹
                "mkdir " + targetPath // 创建文件夹
        };
        if(ShellUtil.cmd(ssh, mkdirCmd)) {
            boolean flag = ShellUtil.sftp(ssh, f.getPath(), targetPath + f.getName());
            log.info("{}上传结果：{}", jobFile.getName(), flag);

            if(flag) {
                // 跳到指定目录，当前目录下
                String[] cmds = new String[]{
                        "cd " + targetPath, // 切换到指定目录
                        "unzip " + f.getName(), // 解压
                        "nohup sh " + jobFile.getName() + " > /dev/null 2>&1 &"}; // 执行脚本
                boolean flag1 = ShellUtil.cmd(ssh, cmds);
                log.info("Job脚本已经执行：{}", flag1);
            }
        }
        log.info("结束");
    }


}
