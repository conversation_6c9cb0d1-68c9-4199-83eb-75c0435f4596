package com.micheal.springbootpostgresqldemo;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.webservice.lddy.LddyWebServiceImpl;
import com.webservice.lddy.LddyWebServiceImplService;
import com.webservice.util.CryptoUtil;
import com.zenith.front.AllUserAppliction;
import com.zenith.front.constant.CommonConstant;
import com.zenith.front.entity.model.DevOps;
import com.zenith.front.entity.model.FlowMonthStatistics;
import com.zenith.front.mapper.DevOpsMapper;
import com.zenith.front.mapper.FlowMonthStatisticsMapper;
import com.zenith.front.scheduled.PushTransferMonthStaticsTask;
import com.zenith.front.service.st.StIService;
import com.zenith.front.untils.StrKit;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.Base64Utils;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.webservice.util.CryptoUtil.cnToUnicode;
import static com.webservice.util.CryptoUtil.getQianMingData;

/**
 * @author: D.watermelon
 * @date: 2023/5/20 23:43
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 */
@SpringBootTest(classes = AllUserAppliction.class)
public class FlowStatiscTest {
    public static final Logger LOGGER = LoggerFactory.getLogger(PushTransferMonthStaticsTask.class);

    @Resource
    private DevOpsMapper opsMapper;
    @Resource
    private FlowMonthStatisticsMapper flowMonthStatisticsMapper;
    @Resource
    Map<String, StIService> stIServiceMap;

    @Test
    void testStatiscTest() {
        stIServiceMap.forEach((k, v) -> {
            System.out.println(k);
        });
        System.out.println(stIServiceMap);
    }

    @Test
    public void testUpload(){
        // TODO: 2023/5/18 最后确定的时候， 提交代码要换成这个
        //DateTime dateTime = DateUtil.lastMonth();
        DateTime dateTime = new DateTime();
        //校验本月是否存在数据
        LambdaQueryWrapper<FlowMonthStatistics> flowMonthStatisticsLambdaQueryWrapper = new LambdaQueryWrapper<>();
        flowMonthStatisticsLambdaQueryWrapper.eq(FlowMonthStatistics::getLDTJ0040, CommonConstant.TWO_INT);
        flowMonthStatisticsLambdaQueryWrapper.last(" and to_char(ldtj0002,'yyyy-mm')='"+ DateUtil.format(dateTime,"yyyy-MM")+"'");
        Integer nowMonthCount = flowMonthStatisticsMapper.selectCount(flowMonthStatisticsLambdaQueryWrapper);
        if (nowMonthCount>=CommonConstant.ONE_INT){
            LOGGER.info("本月已经生成上报数据===>"+DateUtil.format(dateTime,"yyyy-MM"));
            return;
        }
        //生成地级市汇总数据
        LambdaQueryWrapper<DevOps> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(
                DevOps::getMonthDatCode,
                DevOps::getMonthDatName,
                DevOps::getAdministrativeAreaCode,
                DevOps::getAdministrativeAreaName);
        List<DevOps> devOpsList = opsMapper.selectList(queryWrapper);
        Map<String, List<DevOps>> monthDatCodeMap= devOpsList.stream().collect(Collectors.groupingBy(DevOps::getMonthDatCode));
        Set<String> monthDatCodeSet = monthDatCodeMap.keySet();
        List<FlowMonthStatistics> saveList= new ArrayList<>();
        Date ldtj0033 = new Date();
        monthDatCodeSet.forEach(code->{
            DevOps devOps = monthDatCodeMap.get(code).get(CommonConstant.ZERO_INT);
            String monthDatCode = devOps.getMonthDatCode();
            String monthDatName = devOps.getMonthDatName();
            String administrativeAreaCode = devOps.getAdministrativeAreaCode();
            String administrativeAreaName = devOps.getAdministrativeAreaName();
            //获取当前年月， 上一个月时间
            //获取当前节点数据
            QueryWrapper<FlowMonthStatistics> flowMonthStatisticsLambdaQueryWrapperJD = new QueryWrapper<FlowMonthStatistics>()
                    .select("sum(ldtj0006)as ldtj0006,sum(ldtj0007)as ldtj0007,sum(ldtj0008)as ldtj0008,sum(ldtj0009)as ldtj0009,sum(ldtj0011)as ldtj0011,sum(ldtj0012)as ldtj0012," +
                            "sum(ldtj0013)as ldtj0013,sum(ldtj0014)as ldtj0014, sum(ldtj0015)as ldtj0015,sum(ldtj0035)as ldtj0035,sum(ldtj0036)as ldtj0036 ,sum(ldtj0016)as ldtj0016," +
                            "sum(ldtj0017)as ldtj0017,sum(ldtj0018)as ldtj0018");
            flowMonthStatisticsLambdaQueryWrapperJD.eq("ldtj0003", monthDatCode);
            flowMonthStatisticsLambdaQueryWrapperJD.last(" and to_char(ldtj0002,'yyyy-mm')='"+ DateUtil.format(dateTime,"yyyy-MM")+"'");
            FlowMonthStatistics flowMonthStatistics = flowMonthStatisticsMapper.selectOne(flowMonthStatisticsLambdaQueryWrapperJD);
            if (ObjectUtil.isNull(flowMonthStatistics)){
                LOGGER.info("暂未找到相关节点数据");
                flowMonthStatistics=new FlowMonthStatistics();
            }
            flowMonthStatistics.setID(StrKit.getRandomUUID());
            flowMonthStatistics.setLDTJ0001("052000000001");
            flowMonthStatistics.setLDTJ0002(dateTime);
            flowMonthStatistics.setLDTJ0003(monthDatCode);
            flowMonthStatistics.setLDTJ0004(monthDatName);
            flowMonthStatistics.setLDTJ0005("5048C51OE8B74ACF891A1EE5143F85A7");
            flowMonthStatistics.setLDTJ0031(administrativeAreaCode);
            flowMonthStatistics.setLDTJ0032(administrativeAreaName);
            flowMonthStatistics.setLDTJ0033(ldtj0033);
            //排序号问题
            flowMonthStatistics.setLDTJ0040(CommonConstant.TWO_INT);
            flowMonthStatistics.setLDTJ0041(CommonConstant.ZERO_INT);
            flowMonthStatistics.setCreateTime(new Date());
            flowMonthStatistics.setLDTJ0038(monthDatCode);
            flowMonthStatistics.setLDTJ0039(monthDatName);
            saveList.add(flowMonthStatistics);

        });
        saveList.forEach(flowMonthStatistics -> flowMonthStatisticsMapper.insert(flowMonthStatistics));

        //生成贵州省总体数据
        QueryWrapper<FlowMonthStatistics> flowMonthStatisticsLambdaQueryWrapperGZ = new QueryWrapper<FlowMonthStatistics>()
                .select("sum(ldtj0006)as ldtj0006,sum(ldtj0007)as ldtj0007,sum(ldtj0008)as ldtj0008,sum(ldtj0009)as ldtj0009,sum(ldtj0011)as ldtj0011,sum(ldtj0012)as ldtj0012," +
                        "sum(ldtj0013)as ldtj0013,sum(ldtj0014)as ldtj0014, sum(ldtj0015)as ldtj0015,sum(ldtj0035)as ldtj0035,sum(ldtj0036)as ldtj0036 ,sum(ldtj0016)as ldtj0016," +
                        "sum(ldtj0017)as ldtj0017,sum(ldtj0018)as ldtj0018");
        flowMonthStatisticsLambdaQueryWrapperGZ.eq("ldtj0040",CommonConstant.TWO_INT);
        flowMonthStatisticsLambdaQueryWrapperGZ.eq("ldtj0041",CommonConstant.ZERO_INT);
        flowMonthStatisticsLambdaQueryWrapperGZ.eq("ldtj0005","5048C51OE8B74ACF891A1EE5143F85A7");
        flowMonthStatisticsLambdaQueryWrapperGZ.last(" and to_char(ldtj0002,'yyyy-mm')='"+DateUtil.format(dateTime,"yyyy-MM")+"'");
        FlowMonthStatistics flowMonthStatisticsGZ = flowMonthStatisticsMapper.selectOne(flowMonthStatisticsLambdaQueryWrapperGZ);
        if (ObjectUtil.isNull(flowMonthStatisticsGZ)){
            LOGGER.info("生成顶层数据， 暂未找到下级节点数据");
            flowMonthStatisticsGZ=new FlowMonthStatistics();
        }
        //创建贵州实体队对象，然后放入数据库
        flowMonthStatisticsGZ.setID(StrKit.getRandomUUID());
        flowMonthStatisticsGZ.setLDTJ0001("052000000001");
        flowMonthStatisticsGZ.setLDTJ0002(dateTime);
        flowMonthStatisticsGZ.setLDTJ0003(CommonConstant.MINUS_ZERO);
        flowMonthStatisticsGZ.setLDTJ0004("中共贵州省委员会");
        flowMonthStatisticsGZ.setLDTJ0005("5048C51OE8B74ACF891A1EE5143F85A7");
        //贵州省行政区划代码和Code
        flowMonthStatisticsGZ.setLDTJ0031("530000");
        flowMonthStatisticsGZ.setLDTJ0032("贵州省");
        flowMonthStatisticsGZ.setLDTJ0033(ldtj0033);
        flowMonthStatisticsGZ.setLDTJ0034(CommonConstant.ONE_INT);
        flowMonthStatisticsGZ.setLDTJ0040(CommonConstant.TWO_INT);
        flowMonthStatisticsGZ.setLDTJ0041(CommonConstant.ZERO_INT);
        flowMonthStatisticsGZ.setCreateTime(new Date());
        flowMonthStatisticsGZ.setLDTJ0038("5048C51OE8B74ACF891A1EE5143F85A7");
        flowMonthStatisticsGZ.setLDTJ0039("中共贵州省委员会");
        flowMonthStatisticsMapper.insert(flowMonthStatisticsGZ);

        //对接生成上报中组部数据
        List<String> allFlowStatistics = devOpsList.stream().map(DevOps::getMonthDatCode).collect(Collectors.toList());
        allFlowStatistics.add(CommonConstant.MINUS_ZERO);
        LambdaQueryWrapper<FlowMonthStatistics> flowMonthAllStatisticsLambdaQueryWrapper = new LambdaQueryWrapper<>();
        flowMonthAllStatisticsLambdaQueryWrapper.in(FlowMonthStatistics::getLDTJ0003,allFlowStatistics);
        flowMonthAllStatisticsLambdaQueryWrapper.eq(FlowMonthStatistics::getLDTJ0040,CommonConstant.TWO_INT);
        flowMonthAllStatisticsLambdaQueryWrapper.eq(FlowMonthStatistics::getLDTJ0041,CommonConstant.ZERO_INT);
        List<FlowMonthStatistics> flowMonthStatisticsZZB = flowMonthStatisticsMapper.selectList(flowMonthAllStatisticsLambdaQueryWrapper);
        cn.hutool.json.JSONArray zzbJsonArray = JSONUtil.parseArray(flowMonthStatisticsZZB);
        //生成上报中组部的json 数据
        zzbJsonArray.forEach(json->{
            JSONObject jsonObj= (JSONObject) json;
            jsonObj.remove("LDTJ0037");
            jsonObj.remove("LDTJ0038");
            jsonObj.remove("LDTJ0039");
            jsonObj.remove("LDTJ0040");
            jsonObj.remove("LDTJ0041");
            jsonObj.remove("createTime");
            Long ldtj0002 = jsonObj.getLong("LDTJ0002");
            String ldtj0002Str = DateUtil.format(DateUtil.date(ldtj0002), "yyyy-MM-dd hh:mm:ss");
            Long ldtj0033Date = jsonObj.getLong("LDTJ0033");
            String ldtj0033Str = DateUtil.format(DateUtil.date(ldtj0033Date), "yyyy-MM-dd hh:mm:ss");
            jsonObj.set("LDTJ0002",ldtj0002Str);
            jsonObj.set("LDTJ0033",ldtj0033Str);
        });
        LOGGER.info("流动党员转接月度数据上报中组部数据生成JSONArray情况====>"+zzbJsonArray);
        if (zzbJsonArray.size()>CommonConstant.ZERO_INT){
            //需要上传中组部
            String resultStr = null;
            try {
            JSONObject uploadJson= new JSONObject();
            uploadJson.set("accessID","052000000001");
            uploadJson.set("dataType","9");
            uploadJson.set("info",zzbJsonArray);
            uploadJson.set("secretKey", CryptoUtil.getSm2Keys("1")[1]);
            Map<String, String> stringMap = CryptoUtil.signData(uploadJson.toString(), "1");
            String signData = stringMap.get("signData");
            String data = stringMap.get("data");
            String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
            String unicode = cnToUnicode(qianMingData);
            LddyWebServiceImplService lddyWebServiceImplService = new LddyWebServiceImplService();
            LddyWebServiceImpl lddyWebServiceImplPort = lddyWebServiceImplService.getLddyWebServiceImplPort();
            resultStr = lddyWebServiceImplPort.upload(unicode);
            } catch (Exception e) {
                e.printStackTrace();
                //处理上报失败数据情况
                this.uploadStatus(flowMonthStatisticsZZB,CommonConstant.TWO_INT);
            }
            if (StrUtil.isNotEmpty(resultStr)) {
                String responseData = new String(Base64Utils.decode(resultStr.getBytes()), StandardCharsets.UTF_8);
                com.alibaba.fastjson.JSONObject resultObj = com.alibaba.fastjson.JSONObject.parseObject(responseData);
                LOGGER.info("流动党员月度上报中组部数据反馈情况====>"+resultObj.toString());
                if (resultObj.containsKey("code")&&resultObj.getString("code").equals("00")){
                    this.uploadStatus(flowMonthStatisticsZZB,CommonConstant.ONE_INT);
                }else {
                    this.uploadStatus(flowMonthStatisticsZZB,CommonConstant.TWO_INT);
                }
            }
        }
    }

    public void uploadStatus(List<FlowMonthStatistics> dataList,Integer status){
        dataList.forEach(flowMonthStatistics -> {
            Date updateTime = new Date();
            flowMonthStatistics.setUpdateTime(updateTime);
            flowMonthStatistics.setCreateTime(updateTime);
            flowMonthStatistics.setLDTJ0041(status);
            flowMonthStatisticsMapper.updateById(flowMonthStatistics);
        });
    }
}
