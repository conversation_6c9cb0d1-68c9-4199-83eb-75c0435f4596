package com.micheal.springbootpostgresqldemo.webservice;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.koalii.hsmapi.trade.service.HsmApiService;
import com.koalii.hsmapi.trade.service.HsmApiTradeException;
import com.koalii.kgsp.bc.util.encoders.Base64;
import com.koalii.svs.client.Svs2ClientCupHelper;
import com.koalii.svs.client.Svs2ClientHelper;
import com.webservice.dzz.DzzUploadAndDownbusinessServiceImpl;
import com.webservice.dzz.DzzUploadAndDownbusinessServiceImplService;
import com.webservice.esb.ESBUploadAnQuerybusinessServiceImpl;
import com.webservice.esb.ESBUploadAnQuerybusinessServiceImplService;
import com.webservice.lddy.LddyWebServiceImpl;
import com.webservice.lddy.LddyWebServiceImplService;
import com.webservice.response.ResponseResult;
import com.webservice.tz.HeartBeatServiceImpl;
import com.webservice.tz.HeartBeatServiceImplService;
import com.webservice.util.JackSonUtil;
import com.webservice.util.SignatureUtil;
import com.webservice.util.CryptoUtil;
import com.zenith.front.constant.CommonConstant;
import com.zenith.front.entity.dto.DZZLISTDTO;
import com.zenith.front.entity.model.*;
import com.zenith.front.entity.vo.JSXVO;
import com.zenith.front.entity.vo.ProvincesVo;
import com.zenith.front.entity.vo.TransferOrgVO;
import com.zenith.front.mapper.DevOpsMapper;
import com.zenith.front.mapper.TransferMonthStatisticsMapper;
import com.zenith.front.scheduled.PushTransferMonthStaticsTask;
import com.zenith.front.untils.StrKit;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Base64Utils;
;
import javax.annotation.Resource;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.webservice.util.CryptoUtil.cnToUnicode;
import static com.webservice.util.CryptoUtil.getQianMingData;

/**
 * 流动党员接口
 *
 * <AUTHOR>
 * @since 2021/11/24 19:14
 */
public class LddyTest {



    /**
     * 本服务通过查询接口，根据单个唯一码查询交换区是否有流动信息
     * @throws Exception
     */
    @Test
    public void test01() throws Exception {
        // 流动党员
        LddyWebServiceImplService lddyWebServiceImplService = new LddyWebServiceImplService();
        LddyWebServiceImpl lddyWebServiceImplPort = lddyWebServiceImplService.getLddyWebServiceImplPort();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("uniqueKey", "0332000456862020110601872");//流动党员唯一码
        jsonObject.put("accessID", "052000000001");//系统接入标识
        String resultStr = lddyWebServiceImplPort.queryLddyByUniquekey(jsonObject.toString());
        String responseData = new String(Base64Utils.decode(resultStr.getBytes()), "UTF-8");
        JSXVO jsxvo = JackSonUtil.toObject(responseData, JSXVO.class);
        System.out.println("报文：" + jsxvo.toString());
        //ResponseResult responseResult = JackSonUtil.toObject(resultStr, ResponseResult.class);

    }



    /**
     * 交换区探针接口
     * **/
    @Test
    public void  testTz() throws UnsupportedEncodingException {
        HeartBeatServiceImplService heartBeatServiceImplService = new HeartBeatServiceImplService();
        HeartBeatServiceImpl heartBeatServiceImplPort = heartBeatServiceImplService.getHeartBeatServiceImplPort();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("GJDDM","052000000001");
        jsonObject.put("GJDDZZMC","贵州省委");
        String resultStr = heartBeatServiceImplPort.checkStatus(jsonObject.toString());
        System.out.println("报文：" + resultStr);
    }


    /**
     * 上传流动党员
     *
     * @throws Exception
     */
    @Test
    public void uploadLDDy() throws Exception {
        LddyWebServiceImplService lddyWebServiceImplService = new LddyWebServiceImplService();
        LddyWebServiceImpl lddyWebServiceImplPort = lddyWebServiceImplService.getLddyWebServiceImplPort();
        JSONObject jsonObject = new JSONObject();
        MemFlow memFlow = new MemFlow();
        OrgAll orgAll = new OrgAll();
        Map<String, Object> objectMap = new HashMap<>();
        Map<String, Object> map = this.uploadLDDJ(memFlow,orgAll);
        List<Map<String, Object>> maps = this.uploadDYXX();
        objectMap.put("LDDJ", map);
        objectMap.put("DYXX", maps);
        jsonObject.put("accessID", "052000000001");//本省根节点
        jsonObject.put("dataType", "1");//传输的类型
        jsonObject.put("uniqueKey", "0332000456862020110601872");//本流动信息唯一码
        jsonObject.put("secretKey", CryptoUtil.getSm2Keys("1")[1]);//随机码,sm2加密后的
        jsonObject.put("info", objectMap);//info里放转加密后的数据
        Map<String, String> stringMap = CryptoUtil.signData(jsonObject.toString(), "1");
        String signData = stringMap.get("signData");
        String data = stringMap.get("data");
        String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
        String unicode = cnToUnicode(qianMingData);
        String resultStr = lddyWebServiceImplPort.upload(unicode);
        String s = new String(Base64Utils.decode(resultStr.getBytes()), "utf-8");
        System.out.println(s);
        //ResponseResult responseResult = JackSonUtil.toObject(resultStr, ResponseResult.class);

    }

    /**
     * 流动登记数据结构
     * @return
     */
    public Map<String,Object> uploadLDDJ(MemFlow memFlow, OrgAll orgAll){
        Map<String, Object> map = new HashMap<>();
        map.put("LDDJ0001", "0332000456862020110601872");//流动党员登记唯一码
        map.put("LDDJ0002", "刘大爷");//姓名
        map.put("LDDJ0003", "500123199121212121");//公民身份号码
        map.put("LDDJ0004", "321313");//联系电话
        map.put("LDDJ0005", "2022-03-08");//外出日期
        map.put("LDDJ0006", "1");//外出类型 流动类型，01：跨省（直辖市）流动，02：省内跨市（地）流动
        map.put("LDDJ0007", "修文县");//外出地点（党组织）补充说明
        map.put("LDDJ0008", "贵州测试组织");//流出地党支部名称
        map.put("LDDJ0009", "王五");//流出地党支部书记
        map.put("LDDJ0010", "3231232132312");//流出地党支部联系方式
        map.put("LDDJ0011", "052000000001");//流出地建库单位节点编码
        map.put("LDDJ0012", "鼓楼区测试组织");//流入地党支部名称
        map.put("LDDJ0013", "网络安全");//现从事行业
        map.put("LDDJ0014", "刘大爷");//流入地党支部书记
        map.put("LDDJ0015", "31231223");//流入地党支部联系方式
        map.put("LDDJ0016", "035000000001");//流入地建库节点编码
        map.put("LDDJ0017", "10");//流动状态
        map.put("LDDJ0018", "");//失去联系情形
        map.put("LDDJ0019", "");//失去联系日期
        map.put("LDDJ0020", "");//流出地党费交至日期
        map.put("LDDJ0021", "6B90E6F8AC8B4627805B36B09335B418");//审批组织
        map.put("LDDJ0022", "");//流出地从事行业
        map.put("LDDJ0023", "2970AE66697D4AF2AC3A2ABCBA62649E");//流出地党支部标志
        map.put("LDDJ0024", "2970AE666weqweqwewqeqABCBA626wqe");//流入地党支部标志
        map.put("LDDJ0025", "1");//流动原因  党员流动原因代码，1：工作调动；2：入学或毕业；3：入伍或退役；4：入职或离职；5：离开户籍地定居；9：其他。
        map.put("LDDJ0026", "");//流动原因详情
        map.put("LDDJ0027", "");//流动党员接收日期
        map.put("LDDJ0028", "");//流回日期
        map.put("LDDJ0029", "");//行政区划
        map.put("BLGC0010", "2022-03-08 10:29:53");//时间戳
        map.put("DOWNSTATUS", "2");//下载标识 0：不可下载；2：流入地可下载；1：流出地可下载。
        return map;
    }

    /**
     * 党员信息数据结构
     * @return
     */
    public List<Map<String,Object>> uploadDYXX(){
        List<Map<String, Object>> maps = new ArrayList<>();
        Map<String, Object> stringMap = new HashMap<>();
        Map<String, String> map = new HashMap<>();
        map.put("C01ID", "b8c66df1cea545528f45046f2c4b0bf2");//党员（入党申请人）记录ID
        map.put("D01ID", "c2c66df1cea545528f45046f2c4b0fg4");//党组织记录ID
        map.put("C01002", "刘大爷");//姓名
        map.put("A01097", "1");//照片
        map.put("C01003", "1");//性别
        map.put("C01004", "500123199121212121");//身份证
        map.put("C01005", "1991-12-12");//出生日期
        map.put("C01008", "01");//民族
        map.put("C01006", "21");//学历
        map.put("C01007", "");//学位
        map.put("C01010", "1998-06-23");//入党日期
        map.put("C01011", "1999-07-01");//转正日期
        map.put("C01013", "011");//工作岗位
        map.put("C01014", "");//新社会阶层类型
        map.put("C01015", "");//从事专业技术职务
        map.put("C01001", "1");//人员类别 标识该人在组织管理过程中的身份类别代码，包括正式党员、预备党员、发展对象、积极分子、入党申请人等。
        map.put("C01016", "1");//是否“农民工”
        map.put("C01017", "15800000000");//手机号码
        map.put("C01018", "");//党员档案所在单位
        map.put("C01019", "");//户籍所在地
        map.put("C01020", "");//现居住地
        map.put("C01021", "");//其他党团
        map.put("C01022", "");//加入其他党团日期
        map.put("C01023", "");//离开其他党团日期
        map.put("C01012", "");//党龄校正值
        stringMap.put("T_A01", map);
        maps.add(stringMap);
        return maps;
    }

    /**
     * 流动党员流程信息
     */
    private Map<String,String> LCXX(){
        Map<String, String> map = new HashMap<>();
        map.put("LDDJ0001", "1");//流动党员登记唯一码
        map.put("LDDJ0017", "1");//流动状态
        return map;
    }

    /**
     * 上传确认流入数据（接收）
     */
    @Test
    public void uploadConfirmFlow() throws Exception {
        LddyWebServiceImplService lddyWebServiceImplService = new LddyWebServiceImplService();
        LddyWebServiceImpl lddyWebServiceImplPort = lddyWebServiceImplService.getLddyWebServiceImplPort();
        JSONObject jsonObject = new JSONObject();
        Map<String, Object> objectMap = new HashMap<>();
        Map<String, String> flowMap = this.getConfirmFlowMap();
        objectMap.put("LDJS", flowMap);
        jsonObject.put("dataType", "3");//传输的类型
        jsonObject.put("uniqueKey", "1234567890122018020900011");//本流动信息唯一码
        jsonObject.put("accessID", "052000000001");//本省根节点
        jsonObject.put("secretKey", CryptoUtil.getSm2Keys("1")[1]);//随机码,sm2加密后的
        jsonObject.put("info", objectMap);//info里放转加密后的数据
        Map<String, String> stringMap = CryptoUtil.signData(jsonObject.toString(), "1");
        String signData = stringMap.get("signData");
        String data = stringMap.get("data");
        String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
        String unicode = cnToUnicode(qianMingData);
        String resultStr = lddyWebServiceImplPort.upload(unicode);
        String s = new String(Base64Utils.decode(resultStr.getBytes()), "utf-8");
        System.out.println(s);
    }


    public Map<String, String> getConfirmFlowMap() {
        Map<String, String> map = new HashMap<>();
        map.put("LDDJ0012", "柯桥党支部");
        map.put("LDDJ0013", "教师");
        map.put("LDDJ0014", "马某某");
        map.put("LDDJ0015", "13800000002");
        map.put("LDDJ0024", "0e23c20db3b248448e788fdb86a66359");
        map.put("LDDJ0017", "1000000001");
        return map;
    }

    /**
     * 流入方上传结束流动信息
     */
    @Test
    public void intoEnd() throws Exception {
        LddyWebServiceImplService lddyWebServiceImplService = new LddyWebServiceImplService();
        LddyWebServiceImpl lddyWebServiceImplPort = lddyWebServiceImplService.getLddyWebServiceImplPort();
        JSONObject jsonObject = new JSONObject();
        Map<String, Object> objectMap = new HashMap<>();
        Map<String, String> flowMap = this.getFlowMap();
        objectMap.put("LHXX", flowMap);
        jsonObject.put("dataType", "4");//传输的类型
        jsonObject.put("uniqueKey", "1234567890122018020900011");//本流动信息唯一码
        jsonObject.put("accessID", "052000000001");//本省根节点
        jsonObject.put("secretKey", CryptoUtil.getSm2Keys("1")[1]);//随机码,sm2加密后的
        jsonObject.put("info", objectMap);//info里放转加密后的数据
        Map<String, String> stringMap = CryptoUtil.signData(jsonObject.toString(), "1");
        String signData = stringMap.get("signData");
        String data = stringMap.get("data");
        String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
        String unicode = cnToUnicode(qianMingData);
        String resultStr = lddyWebServiceImplPort.upload(unicode);
        String s = new String(Base64Utils.decode(resultStr.getBytes()), "utf-8");
        System.out.println(s);
    }

    public Map<String, String> getFlowMap() {
        Map<String,String> map=new HashMap<>();
        map.put("LDDJ0017", "4000000004");//流动状态
        map.put("LDDJ0028", "2018-07-26");//返回日期
        return map;
    }

    /**
     * 流出方上传结束流动信息
     */
    @Test
    public void outflow() throws Exception {
        LddyWebServiceImplService lddyWebServiceImplService = new LddyWebServiceImplService();
        LddyWebServiceImpl lddyWebServiceImplPort = lddyWebServiceImplService.getLddyWebServiceImplPort();
        JSONObject jsonObject = new JSONObject();
        Map<String, Object> objectMap = new HashMap<>();
        Map<String, String> flowMap = this.getFlowMap();
        objectMap.put("LHXX", flowMap);
        jsonObject.put("dataType", "5");//传输的类型
        jsonObject.put("uniqueKey", "1234567890122018020900011");//本流动信息唯一码
        jsonObject.put("accessID", "052000000001");//本省根节点
        jsonObject.put("secretKey", CryptoUtil.getSm2Keys("1")[1]);//随机码,sm2加密后的
        jsonObject.put("info", objectMap);//info里放转加密后的数据
        Map<String, String> stringMap = CryptoUtil.signData(jsonObject.toString(), "1");
        String signData = stringMap.get("signData");
        String data = stringMap.get("data");
        String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
        String unicode = cnToUnicode(qianMingData);
        String resultStr = lddyWebServiceImplPort.upload(unicode);
        String s = new String(Base64Utils.decode(resultStr.getBytes()), "utf-8");
        System.out.println(s);
    }

    /**
     * 流入方上传流动提醒信息
     */
    public void inflowsRemind() throws Exception {
        LddyWebServiceImplService lddyWebServiceImplService = new LddyWebServiceImplService();
        LddyWebServiceImpl lddyWebServiceImplPort = lddyWebServiceImplService.getLddyWebServiceImplPort();
        JSONObject jsonObject = new JSONObject();
        Map<String, Object> objectMap = new HashMap<>();
        Map<String, String> flowMap = this.getLDTXMap();
        objectMap.put("LDTX", flowMap);
        jsonObject.put("dataType", "6");//传输的类型
        jsonObject.put("uniqueKey", "1234567890122018020900011");//本流动信息唯一码
        jsonObject.put("accessID", "052000000001");//本省根节点
        jsonObject.put("secretKey", CryptoUtil.getSm2Keys("1")[1]);//随机码,sm2加密后的
        jsonObject.put("info", objectMap);//info里放转加密后的数据
        Map<String, String> stringMap = CryptoUtil.signData(jsonObject.toString(), "1");
        String signData = stringMap.get("signData");
        String data = stringMap.get("data");
        String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
        String unicode = cnToUnicode(qianMingData);
        String resultStr = lddyWebServiceImplPort.upload(unicode);
        String s = new String(Base64Utils.decode(resultStr.getBytes()), "utf-8");
        System.out.println(s);
    }


    public Map<String, String> getLDTXMap() {
        Map<String,String> map=new HashMap<>();
        map.put("LDTX0001", "接收提醒根节点代码");//流出地根节点代码
        map.put("LDTX0002", "登记提醒根节点代码");//登记提醒根节点代码
        map.put("LDTX0003", "张三");//姓名
        map.put("LDTX0004", "410221198712231232");//身份证号码
        map.put("LDTX0005", "6B90E6F8AC8B4627805B36B09335B418");//流出党组织ID
        map.put("LDTX0006", "5412f05199934d78948b5165cfeb919e");//流入党组织ID
        map.put("LDTX0007", "党组织2");//流入党组织名称
        return map;
    }

    /**
     * 流入和流出方上传流动党员办理过程
     */
    @Test
    public void flowBLGC() throws Exception {
        LddyWebServiceImplService lddyWebServiceImplService = new LddyWebServiceImplService();
        LddyWebServiceImpl lddyWebServiceImplPort = lddyWebServiceImplService.getLddyWebServiceImplPort();
        JSONObject jsonObject = new JSONObject();
        Map<String, Object> objectMap = new HashMap<>();
        Map<String, String> flowMap = this.getFlowBLGCMap();
        objectMap.put("LDDYBLGC", flowMap);
        jsonObject.put("dataType", "7");//传输的类型
        jsonObject.put("uniqueKey", "1234567890122018020900011");//本流动信息唯一码
        jsonObject.put("accessID", "052000000001");//本省根节点
        jsonObject.put("secretKey", CryptoUtil.getSm2Keys("1")[1]);//随机码,sm2加密后的
        jsonObject.put("info", objectMap);//info里放转加密后的数据
        Map<String, String> stringMap = CryptoUtil.signData(jsonObject.toString(), "1");
        String signData = stringMap.get("signData");
        String data = stringMap.get("data");
        String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
        String unicode = cnToUnicode(qianMingData);
        String resultStr = lddyWebServiceImplPort.upload(unicode);
        String s = new String(Base64Utils.decode(resultStr.getBytes()), "utf-8");
        System.out.println(s);
    }

    public Map<String, String> getFlowBLGCMap() {
        Map<String, String> map = new HashMap<>();
        map.put("LDDJ0001", "1234567890122018020900011");//流动登记唯一码
        map.put("LDDJ0017", "6000000006");//流动状态
        map.put("LDDJ0021", "6v90E6F8AC8B4627805B36B09335B418");//审批党组织
        map.put("BLGC001", "退回原因");//退回原因
        map.put("BLGC002", "不满足条件");//退回原因详细
        map.put("BLGC003", "3");//该节点在流程中的顺序号
        map.put("DOWNSTATUS", "1");//0：不可下载；1：流入地可下载；2：流出地可下载。
        return map;
    }

    /**
     * 流入和流出方上传流动党员编辑信息
     */
    public void flowUpdate() throws Exception {
        LddyWebServiceImplService lddyWebServiceImplService = new LddyWebServiceImplService();
        LddyWebServiceImpl lddyWebServiceImplPort = lddyWebServiceImplService.getLddyWebServiceImplPort();
        JSONObject jsonObject = new JSONObject();
        Map<String, Object> objectMap = new HashMap<>();
        Map<String, String> flowMap = this.getFlowUpdateMap();
        objectMap.put("LDDYBLGC", flowMap);
        jsonObject.put("dataType", "8");//传输的类型
        jsonObject.put("uniqueKey", "1234567890122018020900011");//本流动信息唯一码
        jsonObject.put("accessID", "052000000001");//本省根节点
        jsonObject.put("secretKey", CryptoUtil.getSm2Keys("1")[1]);//随机码,sm2加密后的
        jsonObject.put("operType", "1");//1:修改（均为跨省）  2 删除 （跨省改为非跨省，或者改为无固定地点，或者不掌握流向）
        jsonObject.put("info", objectMap);//info里放转加密后的数据
        Map<String, String> stringMap = CryptoUtil.signData(jsonObject.toString(), "1");
        String signData = stringMap.get("signData");
        String data = stringMap.get("data");
        String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
        String unicode = cnToUnicode(qianMingData);
        String resultStr = lddyWebServiceImplPort.upload(unicode);
        String s = new String(Base64Utils.decode(resultStr.getBytes()), "utf-8");
        System.out.println(s);
    }

    public Map<String, String> getFlowUpdateMap() {
        Map<String, String> map = new HashMap<>();
        map.put("LDDJ0001", "1234567890122018020900011");//流动登记唯一码
        map.put("LDDJ0020", "2019-08-10");//流出地党费交至
        map.put("LDBJ0001", "2019-12-10");//流入地党费交至
        map.put("LDBJ0002", "1000001");//参加组织生活情况代码
        map.put("LDBJ0003", "不满足条件");//表现反馈
        map.put("LDDJ0024", "6v90E6F8AC8B4627805B36B09335B418");//流入党组织id
        map.put("LDDJ0012", "支部2");//流入党组织名称
        map.put("DOWNSTATUS", "1");//0：不可下载；1：流入地可下载；2：流出地可下载。3：流出地和流入地均可下载
        return map;
    }

    /**
     * 查询下载流动登记数据和党员信息或流入方下载结束流动数据示例
     */
    @Test
    public void downloadFlow() throws Exception {
        LddyWebServiceImplService lddyWebServiceImplService = new LddyWebServiceImplService();
        LddyWebServiceImpl lddyWebServiceImplPort = lddyWebServiceImplService.getLddyWebServiceImplPort();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("dataType", "1");//流动党员唯一码
        jsonObject.put("accessID", "052000000001");//系统接入标识
        jsonObject.put("secretKey", CryptoUtil.getSm2Keys("1")[1]);//系统接入标识
        jsonObject.put("gjddm", "052000000001");//根节点代码
        jsonObject.put("type", "1");// 1是待流出方下载信息，2是待流入方下载的信息
        String resultStr = lddyWebServiceImplPort.queryLddyByUniquekey(jsonObject.toString());
        String responseData = new String(Base64Utils.decode(resultStr.getBytes()), "UTF-8");
        JSXVO jsxvo = JackSonUtil.toObject(responseData, JSXVO.class);
        System.out.println("报文：" + jsxvo.toString());
    }

    /**
     * 接收党员数据下载示例
     */
    @Test
    public void downloadAcceptParty() throws Exception {
        LddyWebServiceImplService lddyWebServiceImplService = new LddyWebServiceImplService();
        LddyWebServiceImpl lddyWebServiceImplPort = lddyWebServiceImplService.getLddyWebServiceImplPort();
        JSONObject jsonObject = getJsonObject("3");
        String resultStr = lddyWebServiceImplPort.queryLddyByUniquekey(jsonObject.toString());
        String responseData = new String(Base64Utils.decode(resultStr.getBytes()), "UTF-8");
        JSXVO jsxvo = JackSonUtil.toObject(responseData, JSXVO.class);
        System.out.println("报文：" + jsxvo.toString());
    }

    /**
     * 流入方结束流动党员数据下载示例
     */
    @Test
    public void downloadInflowEnd() throws Exception {
        LddyWebServiceImplService lddyWebServiceImplService = new LddyWebServiceImplService();
        LddyWebServiceImpl lddyWebServiceImplPort = lddyWebServiceImplService.getLddyWebServiceImplPort();
        JSONObject jsonObject = getJsonObject("4");
        String resultStr = lddyWebServiceImplPort.queryLddyByUniquekey(jsonObject.toString());
        String responseData = new String(Base64Utils.decode(resultStr.getBytes()), "UTF-8");
        JSXVO jsxvo = JackSonUtil.toObject(responseData, JSXVO.class);
        System.out.println("报文：" + jsxvo.toString());
    }

    /**
     * 流出方结束流动党员数据下载示例
     */
    @Test
    public void downloadOutflowEnd() throws Exception {
        LddyWebServiceImplService lddyWebServiceImplService = new LddyWebServiceImplService();
        LddyWebServiceImpl lddyWebServiceImplPort = lddyWebServiceImplService.getLddyWebServiceImplPort();
        JSONObject jsonObject = getJsonObject("5");
        String resultStr = lddyWebServiceImplPort.queryLddyByUniquekey(jsonObject.toString());
        String responseData = new String(Base64Utils.decode(resultStr.getBytes()), "UTF-8");
        JSXVO jsxvo = JackSonUtil.toObject(responseData, JSXVO.class);
        System.out.println("报文：" + jsxvo.toString());
    }

    /**
     * 流动提醒数据下载示例
     */
    @Test
    public void downloadFlowRemind() throws Exception {
        LddyWebServiceImplService lddyWebServiceImplService = new LddyWebServiceImplService();
        LddyWebServiceImpl lddyWebServiceImplPort = lddyWebServiceImplService.getLddyWebServiceImplPort();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("dataType", "6");
        jsonObject.put("accessID", "052000000001");//系统接入标识
        jsonObject.put("secretKey", CryptoUtil.getSm2Keys("1")[1]);//系统接入口令
        jsonObject.put("gjddm", "052000000001");//系统接入标识
        String resultStr = lddyWebServiceImplPort.queryLddyByUniquekey(jsonObject.toString());
        String responseData = new String(Base64Utils.decode(resultStr.getBytes()), "UTF-8");
        JSXVO jsxvo = JackSonUtil.toObject(responseData, JSXVO.class);
        System.out.println("报文：" + jsxvo.toString());
    }

    /**
     * 流动党员流程信息下载示例
     */
    @Test
    public void downloadProcessInformation() throws Exception {
        LddyWebServiceImplService lddyWebServiceImplService = new LddyWebServiceImplService();
        LddyWebServiceImpl lddyWebServiceImplPort = lddyWebServiceImplService.getLddyWebServiceImplPort();
        JSONObject jsonObject = getJsonObject("7");
        String resultStr = lddyWebServiceImplPort.queryLddyByUniquekey(jsonObject.toString());
        String responseData = new String(Base64Utils.decode(resultStr.getBytes()), "UTF-8");
        JSXVO jsxvo = JackSonUtil.toObject(responseData, JSXVO.class);
        System.out.println("报文：" + jsxvo.toString());
    }

    /**
     * 流动信息修改数据下载示例
     */
    @Test
    public void downloadFlowInformationUpdate() throws Exception {
        LddyWebServiceImplService lddyWebServiceImplService = new LddyWebServiceImplService();
        LddyWebServiceImpl lddyWebServiceImplPort = lddyWebServiceImplService.getLddyWebServiceImplPort();
        JSONObject jsonObject = getJsonObject("8");
        jsonObject.put("operType", "1");//1修改 2 删除（从有目标组织改成无目标组织，不掌握流向，或者不跨省）
        String resultStr = lddyWebServiceImplPort.queryLddyByUniquekey(jsonObject.toString());
        String responseData = new String(Base64Utils.decode(resultStr.getBytes()), "UTF-8");
        JSXVO jsxvo = JackSonUtil.toObject(responseData, JSXVO.class);
        System.out.println("报文：" + jsxvo.toString());
    }



    private JSONObject getJsonObject(String s) throws Exception {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("dataType", s);//流动党员唯一码
        jsonObject.put("uniqueKey", "234567890122018020900012");
        jsonObject.put("accessID", "052000000001");//系统接入标识
        jsonObject.put("secretKey", CryptoUtil.getSm2Keys("1")[1]);//系统接入标识
        jsonObject.put("gjddm", "052000000001");//根节点代码
        jsonObject.put("type", "1");//1是待流出方下载信息，2是待流入方下载的信息
        return jsonObject;
    }


    /**
     * 党组织上传接口
     * 本服务通过上传接口，将具有预备党员审批权限的组织，上传到交换区。支持批量和单条。
     */
    @Test
    public void uploadOrg() throws Exception {
        DzzUploadAndDownbusinessServiceImplService serviceImplService = new DzzUploadAndDownbusinessServiceImplService();
        DzzUploadAndDownbusinessServiceImpl serviceImplPort = serviceImplService.getDzzUploadAndDownbusinessServiceImplPort();
        JSONObject jsonObject = new JSONObject();
        List<DZZLISTDTO> DZZLIST = new ArrayList<>();
        DZZLISTDTO dzzlistdto = new DZZLISTDTO();
        //党组织12位代码
        //dzzlistdto.setD01001("052"+String.valueOf(System.currentTimeMillis()).substring(4, 13));
        dzzlistdto.setD01001("052000013217");//党组织12位代码
        dzzlistdto.setD01ID("b8c66df2cea222312f45046f2c488888");
        dzzlistdto.setD01002("南明区新华路测试组织");
        dzzlistdto.setD01008("520100");//行政区划代码
        dzzlistdto.setOP01("2");//1新增，2编辑，3删除
        dzzlistdto.setJDDM("052000000001");//根节点代码
        DZZLIST.add(dzzlistdto);
        Map<String, Object> map = new HashMap<>();
        map.put("DZZLIST", DZZLIST);
        jsonObject.put("accessID", "052000000001");//本省根节点
        jsonObject.put("info", map);
        Map<String, String> stringMap = CryptoUtil.signData(jsonObject.toString(), "1");
        String signData = stringMap.get("signData");
        String data = stringMap.get("data");
        String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
        String unicode = cnToUnicode(qianMingData);
        String responseJson = serviceImplPort.upload(unicode);
        String responseData = new String(Base64Utils.decode(responseJson.getBytes()), "UTF-8");
        System.out.println("报文：" + responseData);
    }

    private String getQianMingData(String data, String signData, String accessId){
        JSONObject jo = new JSONObject();
        jo.put("Data", data);
        jo.put("SignedData", signData);
        jo.put("yqsf", accessId);
        return jo.toString();
    }

    private String cnToUnicode(String cn) {
        final char[] chars = cn.toCharArray();
        String returnStr = "";
        for (int i = 0; i < chars.length; i++) {
            returnStr += "\\u" + Integer.toString(chars[i], 16);
        }
        return returnStr;
    }

    /**
     * 党组织下载接口  带页码查全量接口
     * 本服务通过下载接口，将其他省份具有预备党员审批权限的组织，下载到本省，供跨省转接查询和选择。支持批量和单条。
     */
    @Test
    public void downloadOrg() throws Exception {
        DzzUploadAndDownbusinessServiceImplService serviceImplService = new DzzUploadAndDownbusinessServiceImplService();
        DzzUploadAndDownbusinessServiceImpl serviceImplPort = serviceImplService.getDzzUploadAndDownbusinessServiceImplPort();
        JSONObject jsonObject = new JSONObject();
        Map<String, Object> map = new HashMap<>();
        map.put("date", "20220613");//时间
        map.put("accessID", "052000000001");//本省根节点 s
        map.put("xzqh", "052000000001");
        map.put("dataType", "1");
        //map.put("pagesize", "100");
        //map.put("pageno", "100");
        jsonObject.putAll(map);
        Map<String, String> stringMap = CryptoUtil.signData(jsonObject.toString(), "1");
        String signData = stringMap.get("signData");
        String data = stringMap.get("data");
        String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
        String unicode = cnToUnicode(qianMingData);
        String resultStr = serviceImplPort.download(unicode);
        String s = new String(Base64Utils.decode(resultStr.getBytes()), "utf-8");
        Object info = JSONUtil.parseObj(s).get("info");
        String dzzlist = JSONUtil.parseObj(info).get("DZZLIST").toString();
        JSONArray objects = JSONUtil.parseArray(dzzlist);
        List<TransferOrgVO> transferOrgVOS = JSONUtil.toList(objects, TransferOrgVO.class);
        List<TransferOrgVO> collect = transferOrgVOS.stream().filter(r -> r.getD01002().contains("福建医科大学临床医学院学生联")).collect(Collectors.toList());
        System.out.println(collect);
        /*DzzUploadAndDownbusinessServiceImplService serviceImplService = new DzzUploadAndDownbusinessServiceImplService();
        DzzUploadAndDownbusinessServiceImpl serviceImplPort = serviceImplService.getDzzUploadAndDownbusinessServiceImplPort();
        JSONObject jsonObject = new JSONObject();
        Map<String, Object> map = new HashMap<>();
        for (int i = 55; i < 2400; i++) {
            System.out.println("正在下载"+i+"页数据");
            //时间
            map.put("date", "");
            //本省根节点 s
            map.put("accessID", "052000000001");
            map.put("xzqh", "052000000001");
            map.put("dataType", "1");
            map.put("pagesize", "2000");
            map.put("pageno", i);
            jsonObject.putAll(map);
            Map<String, String> stringMap = CryptoUtil.signData(jsonObject.toString(), "1");
            String signData = stringMap.get("signData");
            String data = stringMap.get("data");
            String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
            String unicode = cnToUnicode(qianMingData);
            String resultStr = null;
            try {
                resultStr = serviceImplPort.download(unicode);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (StrUtil.isNotEmpty(resultStr)) {
                String s = new String(Base64Utils.decode(resultStr.getBytes()), "utf-8");
                Object object = JSONUtil.parseObj(s).get("info");
                if (Objects.nonNull(object)) {
                    String info = object.toString();
                    String dzzlist = JSONUtil.parseObj(info).get("DZZLIST").toString();
                    JSONArray objects = JSONUtil.parseArray(dzzlist);
                    List<TransferOrgVO> transferOrgVOS = JSONUtil.toList(objects, TransferOrgVO.class);
                    for (TransferOrgVO transferOrgVO : transferOrgVOS) {
                        if (transferOrgVO.getD01002().contains("松滋市新江口街道谢家渡")) {
                            System.out.println(1);
                        }
                    }
                    System.out.println("第" + i + "页数据已完成");
                } else {
                    System.out.println("已下载完成");
                    break;
                }
            } else {
                i--;
            }
        }*/
    }

    /**
     * 本服务通过上传接口，可将跨省转接和省内转接的信息上传到全国业务数据交换区，跨省转接时需要附带党员信息（DYXX）的JSON数组，省内转接时则不需要附带党员信息（DYXX）。
     *
     * @throws Exception
     */
    @Test
    public void uploadESB() throws Exception {
        ESBUploadAnQuerybusinessServiceImplService service = new ESBUploadAnQuerybusinessServiceImplService();
        ESBUploadAnQuerybusinessServiceImpl serviceImplPort = service.getESBUploadAnQuerybusinessServiceImplPort();
        JSONObject jsonObject = new JSONObject();
        String yyyyMMdd = CryptoUtil.getJSX("052000012223" + DateUtil.format(new Date(), "yyyyMMdd") + "0024");
        Map<String, Object> info = this.mapInfo();
        jsonObject.put("dataType", "1");//定义上传或下载的数据包类型
        jsonObject.put("uniqueKey", CryptoUtil.getJSX("052000012223"+DateUtil.format(new Date(),"yyyyMMdd")+"0024"));//介绍信唯一码
        jsonObject.put("accessID", "052000000001");//系统接入标识
        jsonObject.put("secretKey", CryptoUtil.getSm2Keys("1")[1]); //SM4数据加密串
        jsonObject.put("info", info);
        Map<String, String> stringMap = CryptoUtil.signData(jsonObject.toString(), "1");
        String signData = stringMap.get("signData");
        String data = stringMap.get("data");
        String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
        String unicode = cnToUnicode(qianMingData);
        //String resultStr = serviceImplPort.upload(unicode);
        //String responseData = new String(Base64Utils.decode(resultStr.getBytes()), "UTF-8");
        //System.out.println("报文："+yyyyMMdd +"---"+ responseData);
    }

    public Map<String, Object> mapInfo() {
        Map<String, Object> mapInfo = new HashMap<>();
        Map<String, String> jsxMap = getJsxMap();
        List<Map<String, String>> blgcMap = getBLGCMap();
        List<Map<String, Object>> dyxxMap = getDYXXMap();
        mapInfo.put("JSX", jsxMap);
        mapInfo.put("BLGC", blgcMap);
        mapInfo.put("DYXX", dyxxMap);
        return mapInfo;
    }

    /**
     * 电子介绍信
     *
     * @return
     */
    private Map<String, String> getJsxMap() {
        Map<String, String> jsxMap = new HashMap<>();
        jsxMap.put("JSX0001", "95359e21221e4909b9c32c8fd7385432");//党员ID
        jsxMap.put("JSX0002", "1");//转接类型
        jsxMap.put("JSX0003", "某基层党组织抬头"); //基层党组织名称抬头
        jsxMap.put("JSX0004", "老王");//姓名
        jsxMap.put("JSX0005", "1"); //性别
        jsxMap.put("JSX0006", "03"); //民族
        jsxMap.put("JSX0007", "1"); //人员类别
        jsxMap.put("JSX0008", "500211193523123219"); //公民身份证号码
        jsxMap.put("JSX0009", "45"); //年龄
        jsxMap.put("JSX0010", "011"); //党员的原工作岗位
        jsxMap.put("JSX0011", "b8c66df1cea111312f45046f2c4b0123"); //转出党组织id
        jsxMap.put("JSX0012", "052000012223"); //转出党组织代码
        jsxMap.put("JSX0013", "贵阳测试组织"); //转出党组织名称
        jsxMap.put("JSX0014", "4151"); //党员所在党组织单位性质
        jsxMap.put("JSX0015", "4028b331556a332701556d084e87010f"); //接收党组织ID
        jsxMap.put("JSX0016", "061000000001"); //接收党组织代码
        jsxMap.put("JSX0017", "xxx集团股份有限公司委员会"); //接收党组织名称
        jsxMap.put("JSX0018", "2022-03"); //党费缴至日期
        jsxMap.put("JSX0019", "90"); //有效期
        jsxMap.put("JSX0020", "转出基层党委名称"); //落款
        jsxMap.put("JSX0021", "2022-03-02"); //开具日期
        jsxMap.put("JSX0022", "15800000000"); //党员联系电话
        jsxMap.put("JSX0025", "15823123412"); //基层党组织联系电话
        jsxMap.put("JSX0029", "061000000001"); //接收地系统接入标识
        jsxMap.put("JSX0030", "1"); //更新标志 1：首次上传；2：再次上传，更新
        jsxMap.put("JSX0031", "1");//转接原因 1：工作调动；2：入学或毕业；3：入伍或退伍；4：入职或离职；5：离开户籍地定居；9：其他。
        return jsxMap;
    }

    /**
     * 办理过程
     *
     * @return
     */
    private List<Map<String, String>> getBLGCMap() {
        List<Map<String, String>> list = new ArrayList<>();
        Map<String, String> BLGCMap = new HashMap<>();
        BLGCMap.put("BLGC0001", "052000012223");//经办党组织代码
        BLGCMap.put("BLGC0002", "贵阳测试组织");//经办党组织名称
        BLGCMap.put("BLGC0003", "李经办员");//经办人的姓名
        BLGCMap.put("BLGC0004", "13222221111");//经办人的联系电话（手机）
        BLGCMap.put("BLGC0005", "2022-03-30");//办理日期
        BLGCMap.put("BLGC0006", "0");//办理情况
        BLGCMap.put("BLGC0007", "可以通过");//办理意见
        BLGCMap.put("BLGC0008", "表现良好，予以转接");//备注
        BLGCMap.put("BLGC0009", "");//与党员见面人 党员报到时，与党员见面人的姓名，办理状态为“5”时填写
        BLGCMap.put("BLGC0010", "");//党员报到日期 党员报到日期，办理状态为“5”时填写
        BLGCMap.put("BLGC0011", "1");//顺序号
        BLGCMap.put("BLGC0012", "1");//是否由上级党组织代办,1是0否
        list.add(BLGCMap);
        return list;
    }

    /**
     * 党员信息 多个
     *
     * @return
     */
    private List<Map<String, Object>> getDYXXMap() {
        List<Map<String, Object>> maps = new ArrayList<>();
        Map<String, Object> DYXXMap = new HashMap<>();
        Map<String, String> a3Map = this.getA3Map();
        List<Map<String, String>> c2Map = this.getC2Map();
        List<Map<String, String>> b9Map = this.getB9Map();
        List<Map<String, String>> b11Map = this.getB11Map();
        List<Map<String, String>> c5Map = this.getC5Map();
        List<Map<String, String>> c6Map = this.getC6Map();
        DYXXMap.put("C01", a3Map);//党员基本信息
        DYXXMap.put("C02", c2Map);
        DYXXMap.put("C03", b9Map);//党员奖惩信息
        DYXXMap.put("C04", b11Map);
        DYXXMap.put("C05", c5Map);
        DYXXMap.put("C06", c6Map);
        maps.add(DYXXMap);
        return maps;
    }

    /**
     * 党员基本信息
     *
     * @return
     */
    private Map<String, String> getA3Map() {
        Map<String, String> a3Map = new HashMap<>();
        a3Map.put("C01ID", "95359e21221e4909b9c32c8fd7385432"); //党员唯一标识
        a3Map.put("D01ID", "b8c66df1cea111312f45046f2c4b0123"); //党组织唯一标识
        a3Map.put("C01001", "1"); //人员类别 1正式党员 2预备党员3发展对象4入党积极分子5入党申请人
        a3Map.put("C01002", "老王"); //姓名
        a3Map.put("C01003", "1"); //性别
        a3Map.put("C01004", "500211193523123212"); //公民身份号码
        a3Map.put("C01005", "1935-02-31"); //出生日期
        a3Map.put("C01006", "11"); //学历
        a3Map.put("C01007", "1");//学位
        a3Map.put("C01008", "01"); //民族
        a3Map.put("C01009", "2020-07-01"); //入党日期
        a3Map.put("C01010", "2021-07-02"); //转正日期
        a3Map.put("C01011", "1"); //党龄校正值
        a3Map.put("C01012", "10101"); //工作岗位
        a3Map.put("C01013", "39"); //新社会阶层类型
        a3Map.put("C01014", "4"); //从事专业技术职务
        a3Map.put("C01015", "1"); //是否农民工
        a3Map.put("C01016", "15110091092"); //手机号码
        a3Map.put("C01017", ""); //联合支部所在单位
        a3Map.put("C01018", ""); //户籍所在地
        a3Map.put("C01019", "河北省保定市"); //现居住地
        a3Map.put("C01023", "1"); //进入本信息系统类型
        a3Map.put("C01024", "2020-03-20 18:18:40"); //进入本信息系统日期
        a3Map.put("C01025", "b8c66df1cea111312f45046f2c4b0123"); //进入本信息系统操作党组织
        a3Map.put("C01026", ""); //离开本信息系统类型
        a3Map.put("C01027", "2020-03-20 18:18:40"); //离开本信息系统日期
        a3Map.put("C01028", "71185561f8b54cc99d63c6c4e01f9d0b"); //离开本信息系统操作党组织
        a3Map.put("C01UP1", "1"); //人员状态
        a3Map.put("C01UP2", "2020-03-20 18:18:40"); //更新时间戳
        a3Map.put("C01UP3", "b8c66df1cea111312f45046f2c4b0123"); //操作党组织
        return a3Map;
    }

    /**
     * 多个
     *
     * @return
     */
    private List<Map<String, String>> getC2Map() {
        List<Map<String, String>> maps = new ArrayList<>();
        Map<String, String> c2Map = new HashMap<>();
        c2Map.put("C02ID", "02d3319c81264834baa394bb13a2d212"); //记录ID
        c2Map.put("C01ID", "95359e21221e4909b9c32c8fd7385432"); //党员唯一标识
        c2Map.put("C02001", "19"); //“两代表一委员”类别
        c2Map.put("C02002", "1"); //任职届次
        c2Map.put("C02003", "2020-07-01"); //该届起始日期
        c2Map.put("C02004", "2020-07-31"); //该届届满日期
        c2Map.put("C02005", "10"); //党代表终止原因代码
        c2Map.put("C02006", "2020-07-10"); //党代表资格终止日期
        maps.add(c2Map);
        return maps;
    }

    /**
     * 党员奖惩信息 多个
     *
     * @return
     */
    private List<Map<String, String>> getB9Map() {
        List<Map<String, String>> maps = new ArrayList<>();
        Map<String, String> b9Map = new HashMap<>();
        b9Map.put("C03002", "26,25,24,23,221,22,21,2,1"); //纪律处分和组织处置原因
        b9Map.put("C03ID", "3fedcc48e720316b8414e7021a5521f2"); //记录ID
        b9Map.put("C01ID", "95359e21221e4909b9c32c8fd7385432"); //党员唯一标识
        b9Map.put("C03001", "C11"); //奖惩名称
        b9Map.put("C03003", "121212"); //批准机关
        b9Map.put("C03004", "2019-07-02"); //批准日期
        b9Map.put("C03005", "2020-07-02"); //撤销日期
        maps.add(b9Map);
        return maps;
    }

    /**
     * 党员出国出境情况 多个
     *
     * @return
     */
    private List<Map<String, String>> getB11Map() {
        List<Map<String, String>> maps = new ArrayList<>();
        Map<String, String> b11Map = new HashMap<>();
        b11Map.put("C04ID", "1b0d2c4fed6f4112aa4720fe10321334"); //记录ID
        b11Map.put("C01ID", "95359e21221e4909b9c32c8fd7385432"); //党员唯一标识
        b11Map.put("C04001", "344"); //前往国家(地区)
        b11Map.put("C04002", "2020-07-02"); //出国（境）日期
        b11Map.put("C04003", "11"); //出国(境)原因
        b11Map.put("C04004", "11"); //组织关系处理方式
        b11Map.put("C04005", "2020-07-01"); //申请保留组织关系日期
        b11Map.put("C04006", "2020-07-01"); //回国日期
        b11Map.put("C04007", "2020-07-19"); //批准恢复组织生活日期
        maps.add(b11Map);
        return maps;
    }

    /**
     * 党员帮扶 多个
     *
     * @return
     */
    private List<Map<String, String>> getC5Map() {
        List<Map<String, String>> maps = new ArrayList<>();
        Map<String, String> c5Map = new HashMap<>();
        c5Map.put("C05ID", "c69b3e0421cd43ef2217f51dfb451081"); //记录ID
        c5Map.put("C01ID", "95359e21221e4909b9c32c8fd7385432"); //党员唯一标识
        c5Map.put("C05001", "01"); //关怀帮扶对象类别
        c5Map.put("C05002", "12345678"); //生活困难情况补充说明
        c5Map.put("C05003", "1"); //健康状况
        c5Map.put("C05004", "01"); //关爱帮扶措施
        maps.add(c5Map);
        return maps;
    }

    /**
     * 党员入党情况 多个
     *
     * @return
     */
    private List<Map<String, String>> getC6Map() {
        List<Map<String, String>> maps = new ArrayList<>();
        Map<String, String> c6Map = new HashMap<>();
        c6Map.put("C01ID", "95359e21221e4909b9c32c8fd7385432"); //党员唯一标识
        c6Map.put("C06001", "11"); //入党类型
        c6Map.put("C06002", "2020-07-01 00:00:00"); //入党日期
        c6Map.put("C06003", "1");//转正情况
        c6Map.put("C06004", "2021-07-02 00:00:00"); //转正日期
        c6Map.put("C06005", ""); //入党时所在党支部唯一标识
        c6Map.put("C06006", "121212"); //入党时所在党支部名称
        c6Map.put("C06007", "1"); //延长预备期时间
        maps.add(c6Map);
        return maps;
    }


    /**
     * 办理过程
     *
     * @return
     */
    @Test
    public void uploadBLGCMap() throws Exception {
        ESBUploadAnQuerybusinessServiceImplService service = new ESBUploadAnQuerybusinessServiceImplService();
        ESBUploadAnQuerybusinessServiceImpl serviceImplPort = service.getESBUploadAnQuerybusinessServiceImplPort();
        List<Map<String, String>> blgcMap = getBLGCMap();
        JSONObject jsonObject = new JSONObject();
        Map<String, Object> info = new HashMap<>();
        info.put("BLGC", blgcMap);
        jsonObject.put("dataType", "2");//定义上传或下载的数据包类型
        jsonObject.put("uniqueKey", "0350100004182022040200015");//介绍信唯一码
        jsonObject.put("accessID", "052000000001");//系统接入标识
        jsonObject.put("secretKey", CryptoUtil.getSm2Keys("1")[1]); //SM4数据加密串
        jsonObject.put("info", info);
        Map<String, String> stringMap = CryptoUtil.signData(jsonObject.toString(), "1");
        String signData = stringMap.get("signData");
        String data = stringMap.get("data");
        String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
        String unicode = cnToUnicode(qianMingData);
        //String resultStr = serviceImplPort.upload(unicode);
        //String responseData = new String(Base64Utils.decode(resultStr.getBytes()), "UTF-8");
        //System.out.println("报文：" + responseData);
    }

    /**
     * 3.6	组织关系转接介绍信下载结果返回接口 下载完介绍信再调用
     * 本服务通过上传接口，将组织关系转接介绍信下载处理结果，上传到交换区。
     */
    @Test
    public void uploadJsxResult() throws Exception {
        ESBUploadAnQuerybusinessServiceImplService service = new ESBUploadAnQuerybusinessServiceImplService();
        ESBUploadAnQuerybusinessServiceImpl serviceImplPort = service.getESBUploadAnQuerybusinessServiceImplPort();
        JSONObject jsonObject = new JSONObject();
        List<String> strings = new ArrayList<>();
        strings.add("0350100001132022030700011");
        jsonObject.put("dataType", "10");
        jsonObject.put("code", "00");
        jsonObject.put("accessID", "052000000001");
        jsonObject.put("uniqueKey", strings);
        Map<String, String> stringMap = CryptoUtil.signData(jsonObject.toString(), "1");
        String signData = stringMap.get("signData");
        String data = stringMap.get("data");
        String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
        String unicode = cnToUnicode(qianMingData);
        String resultStr = serviceImplPort.upload(unicode);
        String responseData = new String(Base64Utils.decode(resultStr.getBytes()), "UTF-8");
        System.out.println("报文：" + responseData);
    }

    /**
     * 3.7	组织关系转接介绍信下载结果返回接口
     * 本服务通过上传接口，将组织关系转接介绍信下载处理结果，上传到交换区。
     */
    @Test
    public void uploadBLGCResult() throws Exception {
        ESBUploadAnQuerybusinessServiceImplService service = new ESBUploadAnQuerybusinessServiceImplService();
        ESBUploadAnQuerybusinessServiceImpl serviceImplPort = service.getESBUploadAnQuerybusinessServiceImplPort();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("dataType", "20");
        jsonObject.put("code", "00");
        jsonObject.put("accessID", "052000000001");
        jsonObject.put("msg", "数据上传成功");//数据上传成功
        jsonObject.put("uniqueKey", "");
        Map<String, String> stringMap = CryptoUtil.signData(jsonObject.toString(), "1");
        String signData = stringMap.get("signData");
        String data = stringMap.get("data");
        String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
        String unicode = cnToUnicode(qianMingData);
        String resultStr = serviceImplPort.upload(unicode);
        String responseData = new String(Base64Utils.decode(resultStr.getBytes()), "UTF-8");
        System.out.println("报文：" + responseData);
    }

    /**
     * 3.8	组织关系转接介绍信下载接口
     * 本服务通过组织关系转接下载接口，将跨省转接转到本省的介绍信信息，下载到本地。
     */
    @Test
    public void downloadJSX() throws Exception {
        ESBUploadAnQuerybusinessServiceImplService service = new ESBUploadAnQuerybusinessServiceImplService();
        ESBUploadAnQuerybusinessServiceImpl serviceImplPort = service.getESBUploadAnQuerybusinessServiceImplPort();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("dataType", "1");
        jsonObject.put("xzqh", "052000000001");//请求（本地）系统接入标识
        jsonObject.put("accessID", "052000000001");//系统接入标识
        Map<String, String> stringMap = CryptoUtil.signData(jsonObject.toString(), "1");
        String signData = stringMap.get("signData");
        String data = stringMap.get("data");
        String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
        String unicode = cnToUnicode(qianMingData);
        String resultStr = serviceImplPort.query(unicode);
        String responseData = new String(Base64Utils.decode(resultStr.getBytes()), "UTF-8");
        JSXVO jsxvo = JackSonUtil.toObject(responseData, JSXVO.class);
        String realInfo = "";
        if (StrUtil.isNotEmpty(jsxvo.getSecretKey()) && StrUtil.isNotEmpty(jsxvo.getInfo())) {
            realInfo = CryptoUtil.getRealInfo(jsxvo.getInfo(), jsxvo.getSecretKey());
        } else {
            realInfo = jsxvo.getMsg();
        }
        System.out.println("报文：" + realInfo);
    }

    /**
     * 通过唯一码查询介绍信
     * @throws Exception
     */
    @Test
    public void downloadJSXByCode() throws Exception {
        ESBUploadAnQuerybusinessServiceImplService service = new ESBUploadAnQuerybusinessServiceImplService();
        ESBUploadAnQuerybusinessServiceImpl serviceImplPort = service.getESBUploadAnQuerybusinessServiceImplPort();
        List<String> strings = new ArrayList<>();
        strings.add("0520000122232022061503536");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("dataType", "5");
        jsonObject.put("uniqueKey", strings);//请求（本地）系统接入标识
        jsonObject.put("accessID", "052000000001");//系统接入标识
        Map<String, String> stringMap = CryptoUtil.signData(jsonObject.toString(), "1");
        String signData = stringMap.get("signData");
        String data = stringMap.get("data");
        String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
        String unicode = cnToUnicode(qianMingData);
        String resultStr = serviceImplPort.query(unicode);
        String responseData = new String(Base64Utils.decode(resultStr.getBytes()), "UTF-8");
        JSXVO jsxvo = JackSonUtil.toObject(responseData, JSXVO.class);
        String realInfo = "";
        if (StrUtil.isNotEmpty(jsxvo.getSecretKey()) && StrUtil.isNotEmpty(jsxvo.getInfo())) {
            realInfo = CryptoUtil.getRealInfo(jsxvo.getInfo(), jsxvo.getSecretKey());
        } else {
            realInfo = jsxvo.getMsg();
        }
        System.out.println("报文：" + realInfo);
    }

    /**
     * 3.9	组织关系转接办理过程下载接口
     * 组织关系转接，通过此接口下载办理过程，支持批量下载，dataType为2，下载的办理过程的最新状态
     */
    @Test
    public void downloadBLGC() throws Exception {
        ESBUploadAnQuerybusinessServiceImplService service = new ESBUploadAnQuerybusinessServiceImplService();
        ESBUploadAnQuerybusinessServiceImpl serviceImplPort = service.getESBUploadAnQuerybusinessServiceImplPort();
        JSONObject jsonObject = new JSONObject();
        List<String> strings = new ArrayList<>();
        strings.add("0520000122232022040200133");
        jsonObject.put("dataType", "2");
        jsonObject.put("uniqueKey", strings);//批量查询，多个电子介绍信唯一码
        jsonObject.put("accessID", "052000000001");//系统接入标识
        Map<String, String> stringMap = CryptoUtil.signData(jsonObject.toString(), "1");
        String signData = stringMap.get("signData");
        String data = stringMap.get("data");
        String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
        String unicode = cnToUnicode(qianMingData);
        String resultStr = serviceImplPort.query(unicode);
        String responseData = new String(Base64Utils.decode(resultStr.getBytes()), "UTF-8");
        JSXVO jsxvo = JackSonUtil.toObject(responseData, JSXVO.class);
        String realInfo = "";
        if (StrUtil.isNotEmpty(jsxvo.getSecretKey()) && StrUtil.isNotEmpty(jsxvo.getInfo())) {
            realInfo = CryptoUtil.getRealInfo(jsxvo.getInfo(), jsxvo.getSecretKey());
        } else {
            realInfo = jsxvo.getMsg();
        }
        System.out.println("报文：" + realInfo);
    }

    /**
     * 3.10	下载接入交换区系统的省份信息
     */
    @Test
    public void getProvinces() throws Exception {
        ESBUploadAnQuerybusinessServiceImplService service = new ESBUploadAnQuerybusinessServiceImplService();
        ESBUploadAnQuerybusinessServiceImpl serviceImplPort = service.getESBUploadAnQuerybusinessServiceImplPort();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("dataType", "3");
        jsonObject.put("accessID", "052000000001");//系统接入标识
        Map<String, String> stringMap = CryptoUtil.signData(jsonObject.toString(), "1");
        String signData = stringMap.get("signData");
        String data = stringMap.get("data");
        String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
        String unicode = cnToUnicode(qianMingData);
        String resultStr = serviceImplPort.query(unicode);
        String responseData = new String(Base64Utils.decode(resultStr.getBytes()), "UTF-8");
        String info = JSONUtil.parseObj(responseData).get("info").toString();
        List<ProvincesVo> provincesVos = JSONUtil.parseArray(info).toList(ProvincesVo.class);
        List<ProvincesVo> collect = provincesVos.stream().filter(s -> "1".equals(s.getJHQCSZ())).collect(Collectors.toList());
        System.out.println("报文：" + collect);
    }

    @Test
    public void KKS() {
        /**
         *  *************:5000是格尔服务器地址，根据环境替换
         */
        String geHost = "*************:5000";
        int timeoutSec = 300; //超时时间，秒

        /**
         * 初始化签名对象（程序启动时一次就可以），
         */
        HsmApiService qmHelper = null;
        try {
            qmHelper = new HsmApiService();
        } catch (IOException e) {
            System.out.println("初始化签名对象失败！");
        }
        boolean b = qmHelper.initServers(geHost, timeoutSec);
        System.out.println(b);
        try {
            qmHelper.exportCert("1");
        } catch (HsmApiTradeException e) {
            e.printStackTrace();
        }
        /**
         * 初始化加密对象（程序启动时一次就可以）
         */
        Svs2ClientCupHelper jmHelper = Svs2ClientCupHelper.getInstance();
        jmHelper.initServers(geHost, timeoutSec);
        //jmHelper.enableCipherPositionAtServer();

        /**
         * 模拟地方节点发送数据到中央节点，加密签名操作
         */
        String oriData = "业务测试数据原文";
        String encCert = "encCert"; //中央节点的加密证书
        int signCert = 1; //地方节点的签名证书
        //生成对称密钥
        Svs2ClientHelper.Cipher cipher1 = jmHelper.newCipher( Svs2ClientCupHelper.SM4_CBC,
                Svs2ClientCupHelper.PADDING_PKCS7);
        //对称加密
        Svs2ClientHelper.SvsResultData result1 = jmHelper.cipherEncrypt(cipher1,
                oriData.getBytes(StandardCharsets.UTF_8));
        if( result1.m_errno == 0){
            System.out.println("业务数据加密成功！");
            System.out.println("业务数据加密后结果： " + result1.m_b64EncData);
        }else{
            System.out.println("业务数据加密失败！");
            System.out.println("错误码： " + result1.m_errno);
        }
        //公钥加密
        System.out.println("------------------------------------------------");
        Svs2ClientHelper.SvsResultData result2 = jmHelper.publicKeyEncryptEx(cipher1.key, encCert);
        if( result2.m_errno == 0){
            System.out.println("对称密钥加密成功！");
            System.out.println("对称密钥加密后结果： " + result2.m_b64EvpData);
        }else{
            System.out.println("对称密钥加密失败！");
            System.out.println("错误码： " + result2.m_errno);
        }
        //签名
        System.out.println("------------------------------------------------");
        byte[] signData = null;
        try {
            signData = qmHelper.signData(HsmApiService.DIGEST_ALGO_SM3WITHSM2, signCert,
                    null, result1.m_b64EncData.getBytes(StandardCharsets.UTF_8));
            System.out.println("签名成功！");
            System.out.println("签名后结果(b64)： " + new String(Base64.encode(signData)));
        } catch (HsmApiTradeException e) {
            System.out.println("签名失败！");
            System.out.println("错误码： " + e.getErrorCode());
        }


        /**
         *模拟中央节点接收地方节点发送数据，验签解密操作
         */
        String verifyCert = "verifyCert"; //地方节点的验签证书
        String decCert = "decCert"; //中央节点的解密证书
        //验签
        System.out.println("------------------------------------------------");
        try {
            qmHelper.verifySignedData(2, null, verifyCert,
                    result1.m_b64EncData.getBytes(StandardCharsets.UTF_8), signData, 2);
            System.out.println("验签成功！");
        } catch (HsmApiTradeException e) {
            System.out.println("验签失败！");
            System.out.println("错误码： " + e.getErrorCode());
        }
        //私钥密钥
        System.out.println("------------------------------------------------");
        byte[] key = jmHelper.privateKeyDecryptEx(result2.m_b64EvpData, decCert);
        if( key != null){
            System.out.println("私钥解密成功！");
            System.out.println("解密结果（b64）： " + new String(Base64.encode(key)));
        }else{
            System.out.println("私钥解密失败！");
        }
        //生成对称密钥
        Svs2ClientHelper.Cipher cipher2 = jmHelper.newCipher( key, Svs2ClientCupHelper.SM4_CBC,
                Svs2ClientCupHelper.PADDING_PKCS7);
        //对称密钥解密
        System.out.println("------------------------------------------------");
        Svs2ClientHelper.SvsResultData result3 = jmHelper.cipherDecrypt(cipher2,
                result1.m_b64EncData);
        if( result3.m_errno == 0){
            System.out.println("业务数据解密成功！");
            System.out.println("业务数据： " + new String(result3.m_originData));
        }else{
            System.out.println("业务数据解密失败！");
            System.out.println("错误码： " + result3.m_errno);
        }

        /**
         * 模拟数据完整性保护
         */
        //生成cmac
        //System.out.println("------------------------------------------------");
        //Svs2ClientHelper.SvsResultData result4 = jmHelper.signCmac(oriData.getBytes(StandardCharsets.UTF_8));
        //if( result4.m_errno == 0){
        //    System.out.println("生成cmac成功！");
        //    System.out.println("cmac结果： " + result4.m_cmacData);
        //}else{
        //    System.out.println("生成cmac失败！");
        //    System.out.println("错误码： " + result4.m_errno);
        //}
        ////验证cmac
        //Svs2ClientHelper.SvsResultData result5 = jmHelper.verifyCmac(oriData.getBytes(StandardCharsets.UTF_8), result4.m_cmacData);
        //if( result5.m_errno == 0){
        //    System.out.println("验签cmac成功！");
        //}else{
        //    System.out.println("验签cmac失败！");
        //    System.out.println("错误码： " + result5.m_errno);
        //}

        /**
         * 模拟文件完整性保护
         */
        //String file = "D://测试文件.txt";
        ////生成cmac
        //System.out.println("------------------------------------------------");
        //Svs2ClientHelper.SvsResultData result6 = jmHelper.signCmacFile(file);
        //if( result6.m_errno == 0){
        //    System.out.println("生成文件cmac成功！");
        //    System.out.println("文件cmac结果： " + result6.m_cmacData);
        //}else{
        //    System.out.println("生成文件cmac失败！");
        //    System.out.println("错误码： " + result6.m_errno);
        //}
        ////验证cmac
        //Svs2ClientHelper.SvsResultData result7 = jmHelper.verifyCmacFile( file, result6.m_cmacData);
        //if( result7.m_errno == 0){
        //    System.out.println("验签文件cmac成功！");
        //}else{
        //    System.out.println("验签文件cmac失败！");
        //    System.out.println("错误码： " + result7.m_errno);
        //}

    }
}
