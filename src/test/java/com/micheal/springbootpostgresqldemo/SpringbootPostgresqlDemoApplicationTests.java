package com.micheal.springbootpostgresqldemo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.webservice.esb.ESBUploadAnQuerybusinessServiceImpl;
import com.webservice.esb.ESBUploadAnQuerybusinessServiceImplService;
import com.webservice.util.CryptoUtil;
import com.zenith.front.AllUserAppliction;
import com.zenith.front.constant.CommonConstant;
import com.zenith.front.entity.model.DevOps;
import com.zenith.front.entity.model.TransferMonthStatistics;
import com.zenith.front.mapper.DevOpsMapper;
import com.zenith.front.mapper.TransferMonthStatisticsMapper;
import com.zenith.front.scheduled.PushTransferMonthStaticsTask;
import com.zenith.front.untils.DruidUtil;
import com.zenith.front.untils.PgUtil;
import com.zenith.front.untils.SM4Untils;
import com.zenith.front.untils.StrKit;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.Base64Utils;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.webservice.util.CryptoUtil.cnToUnicode;
import static com.webservice.util.CryptoUtil.getQianMingData;

@SpringBootTest(classes = AllUserAppliction.class)
class SpringbootPostgresqlDemoApplicationTests {



    public static final Logger LOGGER = LoggerFactory.getLogger(PushTransferMonthStaticsTask.class);

    @Resource
    private DevOpsMapper opsMapper;
    @Resource
    private TransferMonthStatisticsMapper transferMonthStatisticsMapper;

    @Test
    void initAdministrativeRegion() {
        List<String> verifyDatabaseList = new ArrayList<>();
        verifyDatabaseList.add("052001");
        DruidUtil.load(verifyDatabaseList);

        ExcelWriter bigWriter = cn.hutool.poi.excel.ExcelUtil.getBigWriter("C:\\Users\\<USER>\\Desktop\\xzqh.xlsx");
        // 一次性写出内容，使用默认样式
        List<Map<String, Object>> orgList = new ArrayList<>();
        for (String database : verifyDatabaseList) {
            List<Map<String, Object>> orgListMap = PgUtil.executeQuery(database, "SELECT code,administrative_region FROM ccp_org_all WHERE administrative_region is not null ORDER BY create_time", null);
            if (CollUtil.isNotEmpty(orgListMap)) {
                orgList.addAll(orgListMap);
            }
        }
        bigWriter.write(orgList);
        bigWriter.close();
    }

    @Test
    void json() {
        String strings = FileUtil.readUtf8String("remote-ssh-mapping.json");

        System.out.println(strings);
        String s = SM4Untils.encryptContent("4676d068-2048-4109-b8eb-98208e7e9652", strings);
        System.out.println();
        System.out.println(s);
        System.out.println();
        String ss = SM4Untils.decryptContent("4676d068-2048-4109-b8eb-98208e7e9652", s);
        System.out.println(ss);

//        Map<String, Map<String, String>> jsonMap = JSON.parseObject(ss, new TypeReference<HashMap<String, Map<String, String>>>() {});
//        System.out.println(jsonMap);
//        Map<String, String> stringStringMap = jsonMap.get("172.16.23.40");
//        String ke = stringStringMap.entrySet().stream().findFirst().map(Map.Entry::getKey).orElse("");
//        String vc = stringStringMap.get(ke);
//        System.out.println("111111111111111111111");
    }


    @Test
    public void uploadTransfer() throws Exception {
        // TODO: 2023/5/18 最后确定的时候， 提交代码要换成这个
        //DateTime dateTime = DateUtil.lastMonth();
        DateTime dateTime = new DateTime();
        //校验本月是否存在数据
        LambdaQueryWrapper<TransferMonthStatistics> transferMonthAllStatisticsCountWrapper = new LambdaQueryWrapper<>();
        transferMonthAllStatisticsCountWrapper.eq(TransferMonthStatistics::getZJTJ0033,CommonConstant.TWO_INT);
        transferMonthAllStatisticsCountWrapper.last(" and to_char(zjtj0002,'yyyy-mm')='"+ DateUtil.format(dateTime,"yyyy-MM")+"'");
        Integer nowMonthCount = transferMonthStatisticsMapper.selectCount(transferMonthAllStatisticsCountWrapper);
        if (nowMonthCount>=CommonConstant.ONE_INT){
            LOGGER.info("本月已经生成上报数据===>"+DateUtil.format(dateTime,"yyyy-MM"));
            return;
        }
        //生成地级市汇总数据
        LambdaQueryWrapper<DevOps> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(
                DevOps::getMonthDatCode,
                DevOps::getMonthDatName,
                DevOps::getAdministrativeAreaCode,
                DevOps::getAdministrativeAreaName);
        List<DevOps> devOpsList = opsMapper.selectList(queryWrapper);
        Map<String, List<DevOps>>  monthDatCodeMap= devOpsList.stream().collect(Collectors.groupingBy(DevOps::getMonthDatCode));
        Set<String> monthDatCodeSet = monthDatCodeMap.keySet();
        List<TransferMonthStatistics> saveList= new ArrayList<>();
        Date zjtj0028 = new Date();
        monthDatCodeSet.forEach(code->{
            DevOps devOps = monthDatCodeMap.get(code).get(CommonConstant.ZERO_INT);
            String monthDatCode = devOps.getMonthDatCode();
            String monthDatName = devOps.getMonthDatName();
            String administrativeAreaCode = devOps.getAdministrativeAreaCode();
            String administrativeAreaName = devOps.getAdministrativeAreaName();
            //获取当前年月， 上一个月时间
            //获取当前节点数据
            QueryWrapper<TransferMonthStatistics> transferMonthStatisticsLambdaQueryWrapper = new QueryWrapper<TransferMonthStatistics>()
                    .select("sum(zjtj0013)as zjtj0013,sum(zjtj0014)as zjtj0014,sum(zjtj0015)as zjtj0015,sum(zjtj0016)as zjtj0016,sum(zjtj0030)as zjtj0030,sum(zjtj0031)as zjtj0031,sum(zjtj0021)as zjtj0021,sum(zjtj0032)as zjtj0032");
            transferMonthStatisticsLambdaQueryWrapper.eq("zjtj0003", monthDatCode);
            transferMonthStatisticsLambdaQueryWrapper.last(" and to_char(zjtj0002,'yyyy-mm')='"+ DateUtil.format(dateTime,"yyyy-MM")+"'");
            TransferMonthStatistics transferMonthStatistics = transferMonthStatisticsMapper.selectOne(transferMonthStatisticsLambdaQueryWrapper);
            if (ObjectUtil.isNull(transferMonthStatistics)){
                LOGGER.info("暂未找到相关节点数据");
                transferMonthStatistics=new TransferMonthStatistics();
            }
            transferMonthStatistics.setID(StrKit.getRandomUUID());
            transferMonthStatistics.setZJTJ0001("052000000001");
            transferMonthStatistics.setZJTJ0002(dateTime);
            transferMonthStatistics.setZJTJ0003(monthDatCode);
            transferMonthStatistics.setZJTJ0004(monthDatName);
            transferMonthStatistics.setZJTJ0005("5048C51OE8B74ACF891A1EE5143F85A7");
            transferMonthStatistics.setZJTJ0026(administrativeAreaCode);
            transferMonthStatistics.setZJTJ0027(administrativeAreaName);
            transferMonthStatistics.setZJTJ0028(zjtj0028);
            //排序号问题
            //transferMonthStatistics.setZJTJ0029();
            transferMonthStatistics.setZJTJ0033(CommonConstant.TWO_INT);
            transferMonthStatistics.setZJTJ0034(CommonConstant.ZERO_INT);
            transferMonthStatistics.setCreateTime(new Date());
            transferMonthStatistics.setZJTJ0036(monthDatCode);
            transferMonthStatistics.setZJTJ0037(monthDatName);
            saveList.add(transferMonthStatistics);

        });
        saveList.forEach(transferMonthStatistics -> transferMonthStatisticsMapper.insert(transferMonthStatistics));

        //生成贵州省总体数据
        QueryWrapper<TransferMonthStatistics> transferMonthStatisticsLambdaQueryWrapper = new QueryWrapper<TransferMonthStatistics>()
                .select("sum(zjtj0013)as zjtj0013,sum(zjtj0014)as zjtj0014,sum(zjtj0015)as zjtj0015,sum(zjtj0016)as zjtj0016,sum(zjtj0030)as zjtj0030,sum(zjtj0031)as zjtj0031,sum(zjtj0021)as zjtj0021,sum(zjtj0032)as zjtj0032");
        transferMonthStatisticsLambdaQueryWrapper.eq("zjtj0033",CommonConstant.TWO_INT);
        transferMonthStatisticsLambdaQueryWrapper.eq("zjtj0034",CommonConstant.ZERO_INT);
        transferMonthStatisticsLambdaQueryWrapper.eq("zjtj0005","5048C51OE8B74ACF891A1EE5143F85A7");
        transferMonthStatisticsLambdaQueryWrapper.last(" and to_char(zjtj0002,'yyyy-mm')='"+DateUtil.format(dateTime,"yyyy-MM")+"'");
        TransferMonthStatistics transferMonthStatisticsGZ = transferMonthStatisticsMapper.selectOne(transferMonthStatisticsLambdaQueryWrapper);
        if (ObjectUtil.isNull(transferMonthStatisticsGZ)){
            LOGGER.info("生成顶层数据， 暂未找到下级节点数据");
            transferMonthStatisticsGZ=new TransferMonthStatistics();
        }
        //创建贵州实体队对象，然后放入数据库
        transferMonthStatisticsGZ.setID(StrKit.getRandomUUID());
        transferMonthStatisticsGZ.setZJTJ0001("052000000001");
        transferMonthStatisticsGZ.setZJTJ0002(dateTime);
        transferMonthStatisticsGZ.setZJTJ0003(CommonConstant.MINUS_ZERO);
        transferMonthStatisticsGZ.setZJTJ0004("中共贵州省委员会");
        transferMonthStatisticsGZ.setZJTJ0005("5048C51OE8B74ACF891A1EE5143F85A7");
        //贵州省行政区划代码和Code
        transferMonthStatisticsGZ.setZJTJ0026("530000");
        transferMonthStatisticsGZ.setZJTJ0027("贵州省");
        transferMonthStatisticsGZ.setZJTJ0028(zjtj0028);
        transferMonthStatisticsGZ.setZJTJ0029(CommonConstant.ONE_INT);
        transferMonthStatisticsGZ.setZJTJ0033(CommonConstant.TWO_INT);
        transferMonthStatisticsGZ.setZJTJ0034(CommonConstant.ZERO_INT);
        transferMonthStatisticsGZ.setCreateTime(new Date());
        transferMonthStatisticsGZ.setZJTJ0036("5048C51OE8B74ACF891A1EE5143F85A7");
        transferMonthStatisticsGZ.setZJTJ0037("中共贵州省委员会");
        transferMonthStatisticsMapper.insert(transferMonthStatisticsGZ);

        //对接生成上报中组部数据
        List<String> allTransferStatistics = devOpsList.stream().map(DevOps::getMonthDatCode).collect(Collectors.toList());
        allTransferStatistics.add(CommonConstant.MINUS_ZERO);
        LambdaQueryWrapper<TransferMonthStatistics> transferMonthAllStatisticsLambdaQueryWrapper = new LambdaQueryWrapper<>();
        transferMonthAllStatisticsLambdaQueryWrapper.in(TransferMonthStatistics::getZJTJ0003,allTransferStatistics);
        transferMonthAllStatisticsLambdaQueryWrapper.eq(TransferMonthStatistics::getZJTJ0033,CommonConstant.TWO_INT);
        transferMonthAllStatisticsLambdaQueryWrapper.eq(TransferMonthStatistics::getZJTJ0034,CommonConstant.ZERO_INT);
        List<TransferMonthStatistics> transferMonthStatisticsZZB = transferMonthStatisticsMapper.selectList(transferMonthAllStatisticsLambdaQueryWrapper);
        cn.hutool.json.JSONArray zzbJsonArray = JSONUtil.parseArray(transferMonthStatisticsZZB);
        //生成上报中组部的json 数据
        zzbJsonArray.forEach(json->{
            JSONObject jsonObj= (JSONObject) json;
            jsonObj.remove("ZJTJ0033");
            jsonObj.remove("ZJTJ0034");
            jsonObj.remove("ZJTJ0036");
            jsonObj.remove("ZJTJ0037");
            Long zjtj0002 = jsonObj.getLong("ZJTJ0002");
            String zjtj0002Str = DateUtil.format(DateUtil.date(zjtj0002), "yyyy-MM-dd hh:mm:ss");
            Long zjtj0028Date = jsonObj.getLong("ZJTJ0028");
            String zjtj0028Str = DateUtil.format(DateUtil.date(zjtj0028Date), "yyyy-MM-dd hh:mm:ss");
            jsonObj.set("ZJTJ0002",zjtj0002Str);
            jsonObj.set("ZJTJ0028",zjtj0028Str);
        });
        LOGGER.info("关系转接月度数据上报中组部数据生成JSONArray情况====>"+zzbJsonArray);
        if (zzbJsonArray.size()>CommonConstant.ZERO_INT){
            //需要上传中组部
            JSONObject uploadJson= new JSONObject();
            uploadJson.set("accessID","052000000001");
            uploadJson.set("dataType","9");
            uploadJson.set("info",zzbJsonArray);
            uploadJson.set("secretKey", CryptoUtil.getSm2Keys("1")[1]);
            Map<String, String> stringMap = CryptoUtil.signData(uploadJson.toString(), "1");
            String signData = stringMap.get("signData");
            String data = stringMap.get("data");
            String qianMingData = getQianMingData(data, signData, CommonConstant.XZQH_GUIZHOU);
            String unicode = cnToUnicode(qianMingData);
            ESBUploadAnQuerybusinessServiceImplService service = new ESBUploadAnQuerybusinessServiceImplService();
            ESBUploadAnQuerybusinessServiceImpl serviceImplPort = service.getESBUploadAnQuerybusinessServiceImplPort();
            String resultStr = null;
            try {
                resultStr = serviceImplPort.upload(unicode);
            } catch (Exception e) {
                e.printStackTrace();
                //处理上报失败数据情况
                this.uploadStatus(transferMonthStatisticsZZB,CommonConstant.TWO_INT);
            }
            if (StrUtil.isNotEmpty(resultStr)) {
                String responseData = new String(Base64Utils.decode(resultStr.getBytes()), StandardCharsets.UTF_8);
                com.alibaba.fastjson.JSONObject resultObj = com.alibaba.fastjson.JSONObject.parseObject(responseData);
                LOGGER.info("上报中组部数据反馈情况====>"+resultObj.toString());
                if (resultObj.containsKey("code")&&resultObj.getString("code").equals("00")){
                    this.uploadStatus(transferMonthStatisticsZZB,CommonConstant.ONE_INT);
                }else {
                    this.uploadStatus(transferMonthStatisticsZZB,CommonConstant.TWO_INT);
                }
            }
        }
    }
    public void uploadStatus(List<TransferMonthStatistics> dataList,Integer status){
        dataList.forEach(transferMonthStatistics -> {
            Date updateTime = new Date();
            transferMonthStatistics.setUpdateTime(updateTime);
            transferMonthStatistics.setCreateTime(updateTime);
            transferMonthStatistics.setZJTJ0034(status);
            transferMonthStatisticsMapper.updateById(transferMonthStatistics);
        });
    }

}
