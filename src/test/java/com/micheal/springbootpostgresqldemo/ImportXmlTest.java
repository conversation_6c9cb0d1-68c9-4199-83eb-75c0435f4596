package com.micheal.springbootpostgresqldemo;

import com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.druid.DruidConfig;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zenith.front.AllUserAppliction;
import com.zenith.front.entity.model.DevOps;
import com.zenith.front.mapper.MemAllInfoMapper;
import com.zenith.front.service.dataexchange.api.ImportDataNodeConfigService;
import com.zenith.front.service.devops.IDevOpsService;
import com.zenith.front.service.devops.NewDevOpsService;
import com.zenith.front.service.dynamic.DynamicService;
import com.zenith.front.untils.DruidUtil;
import com.zenith.front.untils.DynamicUtil;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/7/20
 */
@SpringBootTest(classes = AllUserAppliction.class)
public class ImportXmlTest {

    @Resource
    private NewDevOpsService newDevOpsService;
    @Resource
    private DynamicService dynamicService;
    @Resource
    private DefaultDataSourceCreator dataSourceCreator;
    @Resource
    private MemAllInfoMapper allInfoMapper;
    @Resource
    private IDevOpsService devOpsService;
    @Resource
    private ImportDataNodeConfigService importDataNodeConfigService;

    @Test
    void test1() throws IOException {
        importDataNodeConfigService.start(null, "1");
    }

    @Test
    void test() throws Exception {
//        newDevOpsService.reloadNginx(null);
//        String zipFilePath = "H:\\tmp_package.zip";
//        File zipFile = new File(zipFilePath);
//        ZipFile zFile = new ZipFile(zipFile);
//        if (!zFile.isValidZipFile()) {
//            System.out.println(  "压缩文件不合法,可能被损坏.联系管理员");
//        }


        LambdaQueryWrapper<DevOps> wrapper = Wrappers.lambdaQuery();
        wrapper.select(DevOps::getDatname, DevOps::getNodeKey, DevOps::getNodeName)
                .orderByAsc(DevOps::getDatname);
        List<DevOps> devOpsList = devOpsService.list(wrapper);
        Set<String> set = new HashSet<>();
        for (DevOps devOps : devOpsList) {
            boolean flag = dynamicService.add(devOps.getDatname(), DruidUtil.getDataSourceProperty(devOps.getDatname()));
            if(flag){
                set.add(devOps.getDatname());
            }
        }
        Integer count1 = DynamicUtil.ds((v) -> allInfoMapper.selectCount(null), "052004109");
        System.out.println(count1);
        Integer count2 = DynamicUtil.ds((v) -> allInfoMapper.selectCount(null), "052002109");
        System.out.println(count2);
        for (String s : set) {
            boolean flag = dynamicService.remove(s);
            System.out.println(s+":"+flag);
        }
//        Properties prop = new Properties();
//        prop.setProperty("driver", "org.postgresql.Driver");
//        prop.setProperty("url", "jdbc:postgresql://" + 1 + ":" + 2 + "/" + 2);
//        prop.setProperty("username", "postgres");
//        prop.setProperty("password", "20191809");
//        prop.setProperty("initialSize", "3");
//        prop.setProperty("maxActive", "6");
//        prop.setProperty("minIdle", "1");
//        prop.setProperty("maxWait", "60000");
//        prop.setProperty("filters", "stat");
//        prop.setProperty("timeBetweenEvictionRunsMillis", "35000");
//        prop.setProperty("minEvictableIdleTimeMillis", "30000");
//        prop.setProperty("testWhileIdle", "true");
//        prop.setProperty("testOnBorrow", "false");
//        prop.setProperty("testOnReturn", "false");
//        prop.setProperty("poolPreparedStatements", "false");
//        prop.setProperty("maxPoolPreparedStatementPerConnectionSize", "200");
//        prop.setProperty("removeAbandoned", "true");
//        dynamicService.add("052004109", null);


    }

    private DruidConfig getDruidConfig(){
        DruidConfig druidConfig = new DruidConfig();
        druidConfig.setInitialSize(2);
        druidConfig.setMaxActive(4);
        druidConfig.setMaxWait(60000);
        druidConfig.setMinIdle(1);
        druidConfig.setTimeBetweenEvictionRunsMillis(35000L);
        druidConfig.setTimeBetweenEvictionRunsMillis(30000L);
        druidConfig.setTestWhileIdle(true);
        druidConfig.setTestOnBorrow(false);
        druidConfig.setTestOnReturn(false);
        druidConfig.setPoolPreparedStatements(false);
        druidConfig.setMaxPoolPreparedStatementPerConnectionSize(200);
        druidConfig.setRemoveAbandoned(true);
        return druidConfig;
    }
}
