package com.micheal.springbootpostgresqldemo;

import com.zenith.front.AllUserAppliction;
import com.zenith.front.common.InMessage;
import com.zenith.front.controller.dataexchange.ExportXmlController;
import com.zenith.front.dataexchange.ExchangeXmlServiceImpl;
import com.zenith.front.dataexchange.IExchangeXmlService;
import com.zenith.front.dataexchange.XmlNode;
import com.zenith.front.entity.dto.DataExChangeExportXmlDTO;
import com.zenith.front.entity.model.st.VcFlowInfo;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.*;

/**
 * 驻村干部导入导出测试
 * <AUTHOR>
 * @date 2023/7/19
 */
@SpringBootTest(classes = AllUserAppliction.class)
public class ExportXmlTest {
    @Resource
    private ExportXmlController exportXmlController;
    @Resource
    private IExchangeXmlService exchangeXmlService;

    @Test
    void exportXml(){
        InMessage<DataExChangeExportXmlDTO> dto = new InMessage<>();
        DataExChangeExportXmlDTO d1 = new DataExChangeExportXmlDTO();
        d1.setDatabaseList(Arrays.asList("052004109"));
        dto.setData(d1);
        exportXmlController.exportXml(dto);
    }
    @Test
    void stSaveBatch(){
        XmlNode xmlNode = new XmlNode();
        List<VcFlowInfo> vcFlowInfoList = new ArrayList<>();
        for(int i = 0; i < 3; i++){
            VcFlowInfo info = new VcFlowInfo();
            info.setId(i+"111");
            info.setBizNo(i+"pp");
            vcFlowInfoList.add(info);
        }
        xmlNode.setVcFlowInfoList(vcFlowInfoList);
        exchangeXmlService.stSaveBatch(xmlNode, "1", new LinkedHashMap<>());
    }
}
