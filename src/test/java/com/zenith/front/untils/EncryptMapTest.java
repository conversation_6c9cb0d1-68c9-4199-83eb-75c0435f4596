package com.zenith.front.untils;

import java.util.LinkedHashMap;
import java.util.LinkedHashSet;

/**
 * 测试ENCRYPT_MAP是否包含MemFlow2
 */
public class EncryptMapTest {
    
    public static void main(String[] args) {
        System.out.println("=== ENCRYPT_MAP内容检查 ===");
        
        LinkedHashMap<String, LinkedHashSet<String>> encryptMap = ReflectionUtil.ENCRYPT_MAP;
        
        System.out.println("ENCRYPT_MAP总数: " + encryptMap.size());
        System.out.println();
        
        // 检查是否包含MemFlow2
        String memFlow2ClassName = "com.zenith.front.entity.model.MemFlow2";
        boolean hasMemFlow2 = encryptMap.containsKey(memFlow2ClassName);
        
        System.out.println("是否包含MemFlow2: " + hasMemFlow2);
        
        if (hasMemFlow2) {
            LinkedHashSet<String> memFlow2Fields = encryptMap.get(memFlow2ClassName);
            System.out.println("MemFlow2加密字段数量: " + memFlow2Fields.size());
            System.out.println("MemFlow2加密字段: " + memFlow2Fields);
        } else {
            System.out.println("❌ MemFlow2未在ENCRYPT_MAP中找到！");
        }
        
        System.out.println();
        System.out.println("=== 所有加密类列表 ===");
        
        encryptMap.forEach((className, fields) -> {
            System.out.println("类名: " + className);
            System.out.println("  加密字段: " + fields);
            System.out.println();
        });
        
        // 特别检查包含"MemFlow"的类
        System.out.println("=== 包含MemFlow的类 ===");
        encryptMap.keySet().stream()
            .filter(className -> className.contains("MemFlow"))
            .forEach(className -> {
                System.out.println("找到MemFlow相关类: " + className);
                System.out.println("  字段: " + encryptMap.get(className));
            });
    }
}
